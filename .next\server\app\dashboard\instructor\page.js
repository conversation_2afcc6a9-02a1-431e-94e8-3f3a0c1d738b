(()=>{var e={};e.id=338,e.ids=[338],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},31285:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>h,tree:()=>d}),a(46869),a(22834),a(39285),a(35866);var t=a(23191),r=a(88716),l=a(37922),i=a.n(l),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["dashboard",{children:["instructor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,46869)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\instructor\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,22834)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\instructor\\page.tsx"],m="/dashboard/instructor/page",o={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/instructor/page",pathname:"/dashboard/instructor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28173:(e,s,a)=>{Promise.resolve().then(a.bind(a,90878))},18519:(e,s,a)=>{Promise.resolve().then(a.bind(a,7022))},90878:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(10326),r=a(17577),l=a(77109),i=a(35047),n=a(90434),c=a(46226),d=a(99837),x=a(47375),m=a(36792),o=a(8555),h=a(17334);function u(){let{data:e,status:s}=(0,l.useSession)();(0,i.useRouter)();let[a,u]=(0,r.useState)([]),[p,g]=(0,r.useState)(null),[j,N]=(0,r.useState)([]),[b,v]=(0,r.useState)(!0),[f,y]=(0,r.useState)(null),[D,w]=(0,r.useState)("all"),k=(e,s)=>0===e?"Miễn ph\xed":new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"===s?"VND":"USD"}).format(e),C=e=>{switch(e){case"published":return t.jsx(m.C,{variant:"success",children:"Đ\xe3 xuất bản"});case"draft":return t.jsx(m.C,{variant:"secondary",children:"Bản nh\xe1p"});case"archived":return t.jsx(m.C,{variant:"outline",children:"Đ\xe3 lưu trữ"});default:return t.jsx(m.C,{variant:"outline",children:e})}},A=a.filter(e=>"all"===D||e.status===D);return"loading"===s||b?t.jsx(h.t6,{}):e?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx("div",{className:"bg-white shadow-sm",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard Giảng vi\xean"}),t.jsx("p",{className:"mt-2 text-gray-600",children:"Quản l\xfd kh\xf3a học v\xe0 theo d\xf5i hiệu suất"})]}),t.jsx(n.default,{href:"/instructor/courses/create",children:t.jsx(d.z,{children:"Tạo kh\xf3a học mới"})})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[f&&t.jsx("div",{className:"mb-6",children:t.jsx(o.g7,{variant:"error",message:f})}),p&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[t.jsx(x.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDA"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Tổng kh\xf3a học"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.totalCourses})]})]})}),t.jsx(x.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"✅"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Đ\xe3 xuất bản"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.publishedCourses})]})]})}),t.jsx(x.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"\uD83D\uDC65"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Tổng học vi\xean"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.totalStudents})]})]})}),t.jsx(x.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCB0"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Doanh thu"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:k(p.totalRevenue,"VND")})]})]})}),t.jsx(x.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"⭐"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Đ\xe1nh gi\xe1 TB"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.averageRating>0?p.averageRating.toFixed(1):"0.0"})]})]})}),t.jsx(x.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDD"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Lượt đ\xe1nh gi\xe1"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.totalRatings})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Kh\xf3a học của t\xf4i"}),t.jsx("div",{className:"flex gap-2",children:[{key:"all",label:"Tất cả"},{key:"published",label:"Đ\xe3 xuất bản"},{key:"draft",label:"Bản nh\xe1p"}].map(e=>t.jsx(d.z,{variant:D===e.key?"default":"outline",size:"sm",onClick:()=>w(e.key),children:e.label},e.key))})]}),A.length>0?t.jsx("div",{className:"space-y-4",children:A.map(e=>t.jsx(x.Zb,{className:"p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[t.jsx("div",{className:"w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0 relative overflow-hidden",children:e.thumbnail?t.jsx(c.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover"}):t.jsx("div",{className:"flex items-center justify-center h-full text-gray-400 text-2xl",children:"\uD83D\uDCDA"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold text-lg text-gray-900 mb-1",children:e.title}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[C(e.status),t.jsx("span",{className:"text-sm text-gray-500",children:k(e.pricing.basePrice,e.pricing.currency)})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[t.jsx(n.default,{href:`/instructor/courses/${e.slug}/edit`,children:t.jsx(d.z,{variant:"outline",size:"sm",children:"Chỉnh sửa"})}),t.jsx(n.default,{href:`/courses/${e.slug}`,children:t.jsx(d.z,{variant:"outline",size:"sm",children:"Xem"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:e.stats.totalStudents}),t.jsx("span",{className:"ml-1",children:"học vi\xean"})]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:e.stats.totalLessons}),t.jsx("span",{className:"ml-1",children:"b\xe0i học"})]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:e.stats.averageRating>0?e.stats.averageRating.toFixed(1):"0.0"}),(0,t.jsxs)("span",{className:"ml-1",children:["⭐ (",e.stats.totalRatings,")"]})]})]})]})]})},e._id))}):(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDA"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"all"===D?"Chưa c\xf3 kh\xf3a học n\xe0o":"published"===D?"Chưa c\xf3 kh\xf3a học đ\xe3 xuất bản":"Chưa c\xf3 bản nh\xe1p n\xe0o"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"all"===D?"H\xe3y tạo kh\xf3a học đầu ti\xean của bạn":"Thử thay đổi bộ lọc để xem kh\xf3a học kh\xe1c"}),"all"===D?t.jsx(n.default,{href:"/instructor/courses/create",children:t.jsx(d.z,{children:"Tạo kh\xf3a học mới"})}):t.jsx(d.z,{onClick:()=>w("all"),children:"Xem tất cả kh\xf3a học"})]})]}),t.jsx("div",{className:"lg:col-span-1",children:(0,t.jsxs)(x.Zb,{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Hoạt động gần đ\xe2y"}),j.length>0?t.jsx("div",{className:"space-y-4",children:j.slice(0,10).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[t.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:t.jsx("span",{className:"text-sm",children:"enrollment"===e.type?"\uD83D\uDC65":"completion"===e.type?"✅":"⭐"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-900",children:[t.jsx("span",{className:"font-medium",children:e.studentName}),"enrollment"===e.type&&" đ\xe3 đăng k\xfd ","completion"===e.type&&" đ\xe3 ho\xe0n th\xe0nh ","review"===e.type&&" đ\xe3 đ\xe1nh gi\xe1 ",t.jsx("span",{className:"font-medium",children:e.courseName})]}),t.jsx("p",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleDateString("vi-VN")})]})]},s))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx("div",{className:"text-4xl mb-2",children:"\uD83D\uDCCA"}),t.jsx("p",{className:"text-gray-500 text-sm",children:"Chưa c\xf3 hoạt động n\xe0o"})]})]})})]})]})]}):null}},7022:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var t=a(10326),r=a(77109),l=a(35047);a(17577);var i=a(90434),n=a(16545),c=a(99837);function d({children:e}){let{data:s,status:a}=(0,r.useSession)(),d=(0,l.useRouter)(),x=(0,l.usePathname)();if("loading"===a)return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:t.jsx(n.gb,{size:"lg"})});if(!s)return null;let m=s.user?.role||"student";return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx("header",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,t.jsxs)(i.default,{href:"/",className:"flex items-center",children:[t.jsx("div",{className:"text-2xl font-bold text-primary",children:"WebTA"}),t.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"LMS"})]}),(0,t.jsxs)("nav",{className:"hidden md:flex space-x-1",children:[t.jsx(i.default,{href:"/dashboard/student",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard/student"===x?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard Học vi\xean"}),("instructor"===m||"admin"===m)&&t.jsx(i.default,{href:"/dashboard/instructor",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard/instructor"===x?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard Giảng vi\xean"}),t.jsx(i.default,{href:"/courses",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",children:"Kh\xf3a học"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"hidden sm:block text-sm text-gray-600",children:["Xin ch\xe0o, ",s.user?.name||s.user?.email]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(i.default,{href:"/profile",className:"text-sm text-gray-600 hover:text-gray-900 px-2 py-1 rounded transition-colors",children:"Hồ sơ"}),t.jsx(c.z,{variant:"outline",size:"sm",onClick:()=>d.push("/api/auth/signout"),className:"text-sm",children:"Đăng xuất"})]})]})]})})}),t.jsx("main",{className:"flex-1",children:e})]})}},16545:(e,s,a)=>{"use strict";a.d(s,{gb:()=>n});var t=a(10326),r=a(79360),l=a(51223);let i=(0,r.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function n({variant:e,size:s,className:a,text:r}){return t.jsx("div",{className:(0,l.cn)("flex items-center justify-center",a),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[t.jsx("div",{className:(0,l.cn)(i({variant:e,size:s}))}),r&&t.jsx("p",{className:"text-sm text-gray-600",children:r})]})})}},46869:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\dashboard\instructor\page.tsx#default`)},22834:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\dashboard\layout.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[276,105,226,826,631],()=>a(31285));module.exports=t})();