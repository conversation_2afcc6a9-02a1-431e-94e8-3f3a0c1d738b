import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createPaymentIntent, convertToStripeAmount, handleStripeError } from '@/lib/stripe'
import connectDB from '@/lib/mongodb'
import Course from '@/models/Course'
import User from '@/models/User'
import Payment, { PaymentStatus, PaymentMethod, PaymentType } from '@/models/Payment'
import { z } from 'zod'
import mongoose from 'mongoose'

// Validation schema
const createIntentSchema = z.object({
  courseId: z.string().min(1, 'Course ID là bắt buộc'),
  currency: z.string().optional().default('VND')
})

// POST /api/payment/create-intent - Tạo payment intent cho course
export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Parse and validate request body
    const body = await request.json()
    const { courseId, currency } = createIntentSchema.parse(body)
    
    // Validate course ID
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
      return NextResponse.json({
        success: false,
        error: 'Course ID không hợp lệ'
      }, { status: 400 })
    }
    
    // Find course
    const course = await Course.findById(courseId)
    if (!course) {
      return NextResponse.json({
        success: false,
        error: 'Không tìm thấy khóa học'
      }, { status: 404 })
    }
    
    // Check if course is published and has a price
    if (course.status !== 'published') {
      return NextResponse.json({
        success: false,
        error: 'Khóa học chưa được xuất bản'
      }, { status: 400 })
    }
    
    if (course.pricing.basePrice <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Khóa học này miễn phí, không cần thanh toán'
      }, { status: 400 })
    }
    
    // Check if user is already enrolled
    const { default: Enrollment } = await import('@/models/Enrollment')
    const existingEnrollment = await Enrollment.findOne({
      userId: session.user.id,
      courseId
    })
    
    if (existingEnrollment && existingEnrollment.status === 'active') {
      return NextResponse.json({
        success: false,
        error: 'Bạn đã đăng ký khóa học này rồi'
      }, { status: 400 })
    }
    
    // Get user info
    const user = await User.findById(session.user.id)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Không tìm thấy thông tin người dùng'
      }, { status: 404 })
    }
    
    // Convert amount to Stripe format
    const stripeAmount = convertToStripeAmount(course.pricing.basePrice, currency)
    
    // Create payment record
    const payment = new Payment({
      userId: session.user.id,
      courseId,
      amount: course.pricing.basePrice,
      originalAmount: course.pricing.basePrice,
      currency: currency.toUpperCase(),
      status: PaymentStatus.PENDING,
      method: PaymentMethod.STRIPE,
      type: PaymentType.COURSE_PURCHASE,
      metadata: {
        userAgent: request.headers.get('user-agent') || '',
        ipAddress: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        courseName: course.title,
        userEmail: user.email
      }
    })
    
    await payment.save()
    
    // Create Stripe payment intent
    const paymentIntent = await createPaymentIntent({
      amount: stripeAmount,
      currency: currency.toLowerCase(),
      courseId,
      userId: session.user.id,
      metadata: {
        paymentId: payment._id.toString(),
        courseName: course.title,
        userEmail: user.email,
        userName: `${user.profile.firstName} ${user.profile.lastName}`
      }
    })
    
    // Update payment with Stripe payment intent ID
    payment.externalId = paymentIntent.id
    payment.details.stripePaymentIntentId = paymentIntent.id
    await payment.save()
    
    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        paymentId: payment._id,
        amount: course.pricing.basePrice,
        currency: currency.toUpperCase(),
        course: {
          id: course._id,
          title: course.title,
          thumbnail: course.thumbnail
        }
      },
      message: 'Payment intent đã được tạo thành công'
    })
    
  } catch (error) {
    console.error('Create payment intent error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: error.errors
      }, { status: 400 })
    }
    
    // Handle Stripe errors
    if (error.type && error.type.startsWith('Stripe')) {
      const stripeError = handleStripeError(error)
      return NextResponse.json({
        success: false,
        error: stripeError.message,
        type: stripeError.type
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi tạo payment intent'
    }, { status: 500 })
  }
}
