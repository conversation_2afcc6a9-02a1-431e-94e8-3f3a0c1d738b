(()=>{var e={};e.id=590,e.ids=[590],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},64290:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>h,routeModule:()=>g,tree:()=>c}),t(10402),t(39285),t(35866);var n=t(23191),s=t(88716),a=t(37922),i=t.n(a),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c=["",{children:["auth",{children:["error",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,10402)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\auth\\error\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],h=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\auth\\error\\page.tsx"],u="/auth/error/page",d={require:t,loadChunk:()=>Promise.resolve()},g=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/auth/error/page",pathname:"/auth/error",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},32446:(e,r,t)=>{Promise.resolve().then(t.bind(t,7295))},7295:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(10326),s=t(35047),a=t(90434),i=t(99837),l=t(8555);function o(){let e=(0,s.useSearchParams)().get("error"),r=(e=>{switch(e){case"Configuration":return{title:"Lỗi cấu h\xecnh",message:"C\xf3 lỗi trong cấu h\xecnh hệ thống. Vui l\xf2ng li\xean hệ quản trị vi\xean.",variant:"error"};case"AccessDenied":return{title:"Truy cập bị từ chối",message:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o t\xe0i nguy\xean n\xe0y.",variant:"warning"};case"Verification":return{title:"Lỗi x\xe1c thực",message:"Li\xean kết x\xe1c thực kh\xf4ng hợp lệ hoặc đ\xe3 hết hạn. Vui l\xf2ng thử lại.",variant:"error"};case"Default":return{title:"Lỗi đăng nhập",message:"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập. Vui l\xf2ng thử lại.",variant:"error"};case"EmailNotVerified":return{title:"Email chưa được x\xe1c thực",message:"Vui l\xf2ng kiểm tra email v\xe0 nhấp v\xe0o li\xean kết x\xe1c thực trước khi đăng nhập.",variant:"warning"};case"AccountLocked":return{title:"T\xe0i khoản bị kh\xf3a",message:"T\xe0i khoản của bạn đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần. Vui l\xf2ng thử lại sau.",variant:"error"};case"OAuthSignin":return{title:"Lỗi đăng nhập OAuth",message:"Kh\xf4ng thể đăng nhập bằng t\xe0i khoản mạng x\xe3 hội. Vui l\xf2ng thử lại.",variant:"error"};case"OAuthCallback":return{title:"Lỗi OAuth Callback",message:"C\xf3 lỗi xảy ra khi xử l\xfd th\xf4ng tin từ nh\xe0 cung cấp OAuth.",variant:"error"};case"OAuthCreateAccount":return{title:"Kh\xf4ng thể tạo t\xe0i khoản",message:"Kh\xf4ng thể tạo t\xe0i khoản từ th\xf4ng tin OAuth. Email c\xf3 thể đ\xe3 được sử dụng.",variant:"error"};case"EmailCreateAccount":return{title:"Kh\xf4ng thể tạo t\xe0i khoản",message:"Kh\xf4ng thể tạo t\xe0i khoản với email n\xe0y.",variant:"error"};case"Callback":return{title:"Lỗi Callback",message:"C\xf3 lỗi xảy ra trong qu\xe1 tr\xecnh xử l\xfd callback.",variant:"error"};case"OAuthAccountNotLinked":return{title:"T\xe0i khoản chưa được li\xean kết",message:"Email n\xe0y đ\xe3 được sử dụng với phương thức đăng nhập kh\xe1c. Vui l\xf2ng đăng nhập bằng phương thức ban đầu.",variant:"warning"};case"SessionRequired":return{title:"Y\xeau cầu đăng nhập",message:"Bạn cần đăng nhập để truy cập trang n\xe0y.",variant:"info"};default:return{title:"Lỗi kh\xf4ng x\xe1c định",message:"Đ\xe3 xảy ra lỗi kh\xf4ng mong muốn. Vui l\xf2ng thử lại sau.",variant:"error"}}})(e);return n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100",children:n.jsx("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),n.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:r.title})]}),(0,n.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg",children:[n.jsx(l.g7,{variant:r.variant,message:r.message}),(0,n.jsxs)("div",{className:"mt-6 space-y-4",children:[n.jsx(a.default,{href:"/auth/signin",children:n.jsx(i.z,{className:"w-full",children:"Thử đăng nhập lại"})}),n.jsx(a.default,{href:"/",children:n.jsx(i.z,{variant:"outline",className:"w-full",children:"Về trang chủ"})}),"EmailNotVerified"===e&&n.jsx(a.default,{href:"/auth/resend-verification",children:n.jsx(i.z,{variant:"secondary",className:"w-full",children:"Gửi lại email x\xe1c thực"})})]}),n.jsx("div",{className:"mt-6 text-center",children:(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Nếu vấn đề vẫn tiếp tục, vui l\xf2ng"," ",n.jsx(a.default,{href:"/contact",className:"font-medium text-primary hover:text-primary/80",children:"li\xean hệ hỗ trợ"})]})})]}),!1]})})}},8555:(e,r,t)=>{"use strict";t.d(r,{g7:()=>d});var n=t(10326),s=t(17577),a=t(79360),i=t(51223);let l=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),o=s.forwardRef(({className:e,variant:r,...t},s)=>n.jsx("div",{ref:s,role:"alert",className:(0,i.cn)(l({variant:r}),e),...t}));o.displayName="Alert";let c=s.forwardRef(({className:e,...r},t)=>n.jsx("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));c.displayName="AlertTitle";let h=s.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...r}));h.displayName="AlertDescription";let u={success:n.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:n.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:n.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:n.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function d({title:e,message:r,variant:t="info",onClose:s}){return(0,n.jsxs)(o,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[t],className:"relative",children:[u[t],(0,n.jsxs)("div",{className:"flex-1",children:[e&&n.jsx(c,{children:e}),n.jsx(h,{children:r})]}),s&&n.jsx("button",{onClick:s,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:n.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},10402:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\auth\error\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[276,105,826],()=>t(64290));module.exports=n})();