/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FETUlOJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV2ViVEElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FETUlOJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV2ViVEElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQURNSU4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNXZWJUQSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FETUlOJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV2ViVEElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FETUlOJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV2ViVEElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUEwSDtBQUMxSDtBQUNBLDBKQUF1STtBQUN2STtBQUNBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8/ZWU1MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFETUlOXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcV2ViVEFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQURNSU5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxXZWJUQVxcXFxzcmNcXFxcYXBwXFxcXHByb3ZpZGVycy50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBRE1JTlxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXFdlYlRBXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxIZWFkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CADMIN%5C%5COneDrive%5C%5CDesktop%5C%5CWebTA%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Toast */ \"(ssr)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVpRDtBQUNJO0FBUTlDLFNBQVNFLFVBQVUsRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQWtCO0lBQzdELHFCQUNFLDhEQUFDSiw0REFBZUE7UUFBQ0ksU0FBU0E7a0JBQ3hCLDRFQUFDSCwrREFBYUE7c0JBQ1hFOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vc3JjL2FwcC9wcm92aWRlcnMudHN4PzkzMjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFRvYXN0UHJvdmlkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvVG9hc3QnXG5pbXBvcnQgdHlwZSB7IFNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgc2Vzc2lvbjogU2Vzc2lvbiB8IG51bGxcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuLCBzZXNzaW9uIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIDxUb2FzdFByb3ZpZGVyPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1RvYXN0UHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJUb2FzdFByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJzZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Header() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Prevent hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const navigation = [\n        {\n            name: \"Trang chủ\",\n            href: \"/\"\n        },\n        {\n            name: \"Kh\\xf3a học\",\n            href: \"/courses\"\n        },\n        {\n            name: \"Về ch\\xfang t\\xf4i\",\n            href: \"/about\"\n        },\n        {\n            name: \"Li\\xean hệ\",\n            href: \"/contact\"\n        }\n    ];\n    const userNavigation = session ? [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            name: \"Hồ sơ\",\n            href: \"/profile\"\n        },\n        {\n            name: \"C\\xe0i đặt\",\n            href: \"/settings\"\n        }\n    ] : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary\",\n                                        children: \"WebTA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm text-gray-600\",\n                                        children: \"LMS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium transition-colors\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: !mounted || status === \"loading\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-pulse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-20 bg-gray-200 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this) : session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center\",\n                                                children: session.user.name?.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: session.user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\",\n                                        children: [\n                                            userNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: item.href,\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                className: \"my-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)(),\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"Đăng xuất\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            children: \"Đăng nhập\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/signup\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            size: \"sm\",\n                                            children: \"Đăng k\\xfd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 hover:text-primary p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"md:hidden\", isMenuOpen ? \"block\" : \"hidden\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 border-t\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: \"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)),\n                            session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    userNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: \"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: item.name\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)();\n                                            setIsMenuOpen(false);\n                                        },\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md\",\n                                        children: \"Đăng xuất\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Đăng nhập\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/signup\",\n                                        className: \"block px-3 py-2 text-base font-medium text-primary hover:bg-gray-50 rounded-md\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Đăng k\\xfd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 40,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast auto */ \n\n\n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Toast provider\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            id,\n            duration: 5000,\n            ...toast\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove toast after duration\n        if (newToast.duration && newToast.duration > 0) {\n            setTimeout(()=>{\n                removeToast(id);\n            }, newToast.duration);\n        }\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const clearToasts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setToasts([]);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            clearToasts\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use toast\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\n// Toast container component\nfunction ToastContainer() {\n    const { toasts } = useToast();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted || toasts.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast\n            }, toast.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this), document.body);\n}\n// Individual toast item\nfunction ToastItem({ toast }) {\n    const { removeToast } = useToast();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        // Trigger animation\n        const timer = setTimeout(()=>setIsVisible(true), 10);\n        return ()=>clearTimeout(timer);\n    }, []);\n    const handleClose = ()=>{\n        setIsVisible(false);\n        setTimeout(()=>removeToast(toast.id), 150);\n    };\n    const variantStyles = {\n        default: \"bg-white border-gray-200\",\n        success: \"bg-green-50 border-green-200\",\n        error: \"bg-red-50 border-red-200\",\n        warning: \"bg-yellow-50 border-yellow-200\",\n        info: \"bg-blue-50 border-blue-200\"\n    };\n    const iconStyles = {\n        default: \"text-gray-400\",\n        success: \"text-green-400\",\n        error: \"text-red-400\",\n        warning: \"text-yellow-400\",\n        info: \"text-blue-400\"\n    };\n    const icons = {\n        default: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this),\n        success: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this),\n        error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this),\n        warning: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this),\n        info: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    };\n    const variant = toast.variant || \"default\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border transition-all duration-300 transform\", variantStyles[variant], isVisible ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex-shrink-0\", iconStyles[variant]),\n                        children: icons[variant]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 w-0 flex-1\",\n                        children: [\n                            toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: toast.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-gray-500\", toast.title ? \"mt-1\" : \"\"),\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toast.action.onClick,\n                                    className: \"text-sm font-medium text-primary hover:text-primary/80\",\n                                    children: toast.action.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Đ\\xf3ng\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n// Convenience functions\nconst toast = {\n    success: (message, options)=>({\n            message,\n            variant: \"success\",\n            ...options\n        }),\n    error: (message, options)=>({\n            message,\n            variant: \"error\",\n            ...options\n        }),\n    warning: (message, options)=>({\n            message,\n            variant: \"warning\",\n            ...options\n        }),\n    info: (message, options)=>({\n            message,\n            variant: \"info\",\n            ...options\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function để merge Tailwind CSS classes\n * Sử dụng clsx để handle conditional classes và twMerge để resolve conflicts\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency theo định dạng Việt Nam\n */ function formatCurrency(amount, currency = \"VND\") {\n    return new Intl.NumberFormat(\"vi-VN\", {\n        style: \"currency\",\n        currency: currency\n    }).format(amount);\n}\n/**\n * Validate email address\n */ function validateEmail(email) {\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    return emailRegex.test(email);\n}\n/**\n * Generate slug từ string\n */ function generateSlug(text) {\n    return text.toLowerCase().normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g, \"\") // Remove diacritics\n    .replace(/[^a-z0-9\\s-]/g, \"\") // Remove special characters\n    .trim().replace(/\\s+/g, \"-\") // Replace spaces with hyphens\n    .replace(/-+/g, \"-\") // Replace multiple hyphens with single\n    ;\n}\n/**\n * Truncate text với ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + \"...\";\n}\n/**\n * Format duration từ seconds sang readable format\n */ function formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = seconds % 60;\n    if (hours > 0) {\n        return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n    }\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6ba466d574d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8xMjRhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNmJhNDY2ZDU3NGQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"WebTA LMS - Hệ thống Quản l\\xfd Học tập\",\n    description: \"Hệ thống LMS to\\xe0n diện cho việc b\\xe1n v\\xe0 quản l\\xfd kh\\xf3a học ngoại ngữ với t\\xednh năng đ\\xe1nh gi\\xe1 tự động bằng AI\",\n    keywords: [\n        \"LMS\",\n        \"học tập\",\n        \"ngoại ngữ\",\n        \"AI\",\n        \"đ\\xe1nh gi\\xe1\"\n    ],\n    authors: [\n        {\n            name: \"WebTA Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nasync function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"vi\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                session: null,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-6\",\n                        children: \"Ch\\xe0o mừng đến với WebTA LMS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\",\n                        children: \"Hệ thống quản l\\xfd học tập to\\xe0n diện với t\\xednh năng đ\\xe1nh gi\\xe1 tự động bằng AI cho 4 kỹ năng ng\\xf4n ngữ: Nghe, N\\xf3i, Đọc, Viết\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-600 text-3xl mb-4\",\n                                        children: \"\\uD83C\\uDFA7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"Nghe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"Đ\\xe1nh gi\\xe1 khả năng nghe hiểu với AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-600 text-3xl mb-4\",\n                                        children: \"\\uD83D\\uDDE3️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"N\\xf3i\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"Ph\\xe2n t\\xedch ph\\xe1t \\xe2m v\\xe0 độ tr\\xf4i chảy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-purple-600 text-3xl mb-4\",\n                                        children: \"\\uD83D\\uDCD6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"Đọc\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"Kiểm tra hiểu đọc v\\xe0 tốc độ đọc\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-6 rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-orange-600 text-3xl mb-4\",\n                                        children: \"✍️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"Viết\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"Đ\\xe1nh gi\\xe1 ngữ ph\\xe1p v\\xe0 từ vựng\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-8 rounded-lg shadow-lg max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Phase 0: Project Setup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500 mr-2\",\n                                                    children: \"✅\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Next.js 14+ với TypeScript\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-500 mr-2\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"MongoDB connection setup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mr-2\",\n                                                    children: \"⏳\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Authentication system\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mr-2\",\n                                                    children: \"⏳\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Development tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        product: [\n            {\n                name: \"Kh\\xf3a học\",\n                href: \"/courses\"\n            },\n            {\n                name: \"T\\xednh năng\",\n                href: \"/features\"\n            },\n            {\n                name: \"Gi\\xe1 cả\",\n                href: \"/pricing\"\n            },\n            {\n                name: \"Đ\\xe1nh gi\\xe1 AI\",\n                href: \"/ai-assessment\"\n            }\n        ],\n        company: [\n            {\n                name: \"Về ch\\xfang t\\xf4i\",\n                href: \"/about\"\n            },\n            {\n                name: \"Blog\",\n                href: \"/blog\"\n            },\n            {\n                name: \"Tuyển dụng\",\n                href: \"/careers\"\n            },\n            {\n                name: \"Li\\xean hệ\",\n                href: \"/contact\"\n            }\n        ],\n        support: [\n            {\n                name: \"Trung t\\xe2m trợ gi\\xfap\",\n                href: \"/help\"\n            },\n            {\n                name: \"Hướng dẫn\",\n                href: \"/guides\"\n            },\n            {\n                name: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                name: \"Hỗ trợ kỹ thuật\",\n                href: \"/support\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Điều khoản sử dụng\",\n                href: \"/terms\"\n            },\n            {\n                name: \"Ch\\xednh s\\xe1ch bảo mật\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"Ch\\xednh s\\xe1ch cookie\",\n                href: \"/cookies\"\n            },\n            {\n                name: \"Bảo mật\",\n                href: \"/security\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Facebook\",\n            href: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Twitter\",\n            href: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"YouTube\",\n            href: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"WebTA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-gray-400\",\n                                            children: \"LMS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mb-4\",\n                                    children: \"Hệ thống quản l\\xfd học tập to\\xe0n diện với t\\xednh năng đ\\xe1nh gi\\xe1 tự động bằng AI cho 4 kỹ năng ng\\xf4n ngữ.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: socialLinks.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: item.href,\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.icon\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: \"Sản phẩm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.product.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: \"C\\xf4ng ty\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: \"Hỗ trợ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.support.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: \"Ph\\xe1p l\\xfd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: footerLinks.legal.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" WebTA LMS. Tất cả quyền được bảo lưu.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"Được ph\\xe1t triển với ❤️ tại Việt Nam\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\components\layout\Header.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();