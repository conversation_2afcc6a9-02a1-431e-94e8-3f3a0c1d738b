(()=>{var e={};e.id=98,e.ids=[98],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},516:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),s(22061),s(39285),s(35866);var t=s(23191),a=s(88716),i=s(37922),n=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let d=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22061)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\auth\\signin\\page.tsx"],u="/auth/signin/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},22586:(e,r,s)=>{Promise.resolve().then(s.bind(s,37186))},37186:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(10326),a=s(17577),i=s(77109),n=s(35047),l=s(90434),o=s(99837),d=s(89175),c=s(158),u=s(8555),m=s(16545);function x(){let e=(0,n.useRouter)(),r=(0,n.useSearchParams)(),s=r.get("callbackUrl")||"/",x=r.get("error"),[h,p]=(0,a.useState)({email:"",password:""}),[g,f]=(0,a.useState)({}),[b,v]=(0,a.useState)(!1),j=e=>{let{name:r,value:s}=e.target;p(e=>({...e,[r]:s})),g[r]&&f(e=>({...e,[r]:""}))},y=()=>{let e={};return h.email?/\S+@\S+\.\S+/.test(h.email)||(e.email="Email kh\xf4ng hợp lệ"):e.email="Email l\xe0 bắt buộc",h.password||(e.password="Mật khẩu l\xe0 bắt buộc"),f(e),0===Object.keys(e).length},w=async r=>{if(r.preventDefault(),y()){v(!0);try{let r=await (0,i.signIn)("credentials",{email:h.email,password:h.password,redirect:!1});r?.error?f({general:r.error}):(await (0,i.getSession)(),e.push(s),e.refresh())}catch(e){f({general:"Đ\xe3 xảy ra lỗi kh\xf4ng mong muốn"})}finally{v(!1)}}};return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary",children:t.jsx("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),t.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Đăng nhập v\xe0o t\xe0i khoản"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Hoặc"," ",t.jsx(l.default,{href:"/auth/signup",className:"font-medium text-primary hover:text-primary/80",children:"tạo t\xe0i khoản mới"})]})]}),(0,t.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg",children:[x&&t.jsx("div",{className:"mb-6",children:t.jsx(u.g7,{variant:"error",message:(e=>{switch(e){case"CredentialsSignin":return"Email hoặc mật khẩu kh\xf4ng đ\xfang";case"EmailNotVerified":return"Vui l\xf2ng x\xe1c thực email trước khi đăng nhập";case"AccountLocked":return"T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần";default:return"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập"}})(x)})}),g.general&&t.jsx("div",{className:"mb-6",children:t.jsx(u.g7,{variant:"error",message:g.general})}),(0,t.jsxs)(c.l0,{onSubmit:w,children:[(0,t.jsxs)(c.Wi,{children:[t.jsx(c.lX,{htmlFor:"email",required:!0,children:"Email"}),t.jsx(d.I,{id:"email",name:"email",type:"email",autoComplete:"email",value:h.email,onChange:j,error:!!g.email,placeholder:"Nhập email của bạn"}),t.jsx(c.Xq,{message:g.email})]}),(0,t.jsxs)(c.Wi,{children:[t.jsx(c.lX,{htmlFor:"password",required:!0,children:"Mật khẩu"}),t.jsx(d.I,{id:"password",name:"password",type:"password",autoComplete:"current-password",value:h.password,onChange:j,error:!!g.password,placeholder:"Nhập mật khẩu"}),t.jsx(c.Xq,{message:g.password})]}),t.jsx("div",{className:"flex items-center justify-between",children:t.jsx("div",{className:"text-sm",children:t.jsx(l.default,{href:"/auth/forgot-password",className:"font-medium text-primary hover:text-primary/80",children:"Qu\xean mật khẩu?"})})}),t.jsx(o.z,{type:"submit",className:"w-full",disabled:b,children:b?t.jsx(m.gb,{size:"sm"}):"Đăng nhập"})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-0 flex items-center",children:t.jsx("div",{className:"w-full border-t border-gray-300"})}),t.jsx("div",{className:"relative flex justify-center text-sm",children:t.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Hoặc tiếp tục với"})})]}),t.jsx("div",{className:"mt-6",children:(0,t.jsxs)(o.z,{type:"button",variant:"outline",className:"w-full",onClick:()=>{(0,i.signIn)("google",{callbackUrl:s})},disabled:b,children:[(0,t.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[t.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),t.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),t.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),t.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})})]})]})]})})}},8555:(e,r,s)=>{"use strict";s.d(r,{g7:()=>m});var t=s(10326),a=s(17577),i=s(79360),n=s(51223);let l=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:r,...s},a)=>t.jsx("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:r}),e),...s}));o.displayName="Alert";let d=a.forwardRef(({className:e,...r},s)=>t.jsx("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...r}));c.displayName="AlertDescription";let u={success:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function m({title:e,message:r,variant:s="info",onClose:a}){return(0,t.jsxs)(o,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[s],className:"relative",children:[u[s],(0,t.jsxs)("div",{className:"flex-1",children:[e&&t.jsx(d,{children:e}),t.jsx(c,{children:r})]}),a&&t.jsx("button",{onClick:a,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},158:(e,r,s)=>{"use strict";s.d(r,{Ee:()=>u,Wi:()=>l,Xq:()=>d,l0:()=>n,lX:()=>o,yv:()=>c});var t=s(10326),a=s(17577),i=s(51223);function n({className:e,children:r,...s}){return t.jsx("form",{className:(0,i.cn)("space-y-6",e),...s,children:r})}function l({children:e,className:r}){return t.jsx("div",{className:(0,i.cn)("space-y-2",r),children:e})}function o({className:e,children:r,required:s,...a}){return(0,t.jsxs)("label",{className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...a,children:[r,s&&t.jsx("span",{className:"text-red-500 ml-1",children:"*"})]})}function d({message:e,className:r}){return e?t.jsx("p",{className:(0,i.cn)("text-sm text-red-600",r),children:e}):null}function c({children:e,className:r}){return t.jsx("p",{className:(0,i.cn)("text-sm text-gray-500",r),children:e})}function u({name:e,options:r,value:s,onChange:a,error:n,className:l}){return t.jsx("div",{className:(0,i.cn)("space-y-2",l),children:r.map(r=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"radio",id:`${e}-${r.value}`,name:e,value:r.value,checked:s===r.value,onChange:e=>a?.(e.target.value),disabled:r.disabled,className:(0,i.cn)("h-4 w-4 text-primary focus:ring-primary focus:ring-2",n&&"border-red-500")}),t.jsx("label",{htmlFor:`${e}-${r.value}`,className:(0,i.cn)("text-sm font-medium leading-none",r.disabled&&"opacity-50 cursor-not-allowed"),children:r.label})]},r.value))})}a.forwardRef(({className:e,error:r,...s},a)=>t.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",e),ref:a,...s})).displayName="Textarea",a.forwardRef(({className:e,error:r,placeholder:s,children:a,...n},l)=>(0,t.jsxs)("select",{className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",e),ref:l,...n,children:[s&&t.jsx("option",{value:"",disabled:!0,children:s}),a]})).displayName="Select",a.forwardRef(({className:e,label:r,error:s,id:a,...n},l)=>{let o=a||`checkbox-${Math.random().toString(36).substr(2,9)}`;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:o,className:(0,i.cn)("h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary focus:ring-2",s&&"border-red-500",e),ref:l,...n}),r&&t.jsx("label",{htmlFor:o,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r})]})}).displayName="Checkbox"},89175:(e,r,s)=>{"use strict";s.d(r,{I:()=>n});var t=s(10326),a=s(17577),i=s(51223);let n=a.forwardRef(({className:e,type:r,...s},a)=>t.jsx("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));n.displayName="Input"},16545:(e,r,s)=>{"use strict";s.d(r,{gb:()=>l});var t=s(10326),a=s(79360),i=s(51223);let n=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function l({variant:e,size:r,className:s,text:a}){return t.jsx("div",{className:(0,i.cn)("flex items-center justify-center",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[t.jsx("div",{className:(0,i.cn)(n({variant:e,size:r}))}),a&&t.jsx("p",{className:"text-sm text-gray-600",children:a})]})})}},22061:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\auth\signin\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,105,826],()=>s(516));module.exports=t})();