'use client'

import { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from './Button'
import { Loading } from './Loading'
import { AlertMessage } from './Alert'
import { useToast } from './Toast'

interface FileUploadProps {
  accept?: string
  multiple?: boolean
  maxSize?: number // in bytes
  folder?: string
  onUploadComplete?: (files: UploadedFile[]) => void
  onUploadError?: (error: string) => void
  className?: string
  disabled?: boolean
  children?: React.ReactNode
}

interface UploadedFile {
  public_id: string
  secure_url: string
  url: string
  format: string
  resource_type: string
  bytes: number
  width?: number
  height?: number
  duration?: number
  thumbnailUrl?: string
  fileType: string
  originalName: string
}

interface UploadProgress {
  file: File
  progress: number
  status: 'uploading' | 'completed' | 'error'
  result?: UploadedFile
  error?: string
}

export function FileUpload({
  accept = '*/*',
  multiple = false,
  maxSize = 10 * 1024 * 1024, // 10MB default
  folder = 'general',
  onUploadComplete,
  onUploadError,
  className = '',
  disabled = false,
  children
}: FileUploadProps) {
  const [uploads, setUploads] = useState<UploadProgress[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { addToast } = useToast()

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024))
      return {
        valid: false,
        error: `File "${file.name}" quá lớn. Kích thước tối đa là ${maxSizeMB}MB`
      }
    }

    // Check file type if accept is specified
    if (accept !== '*/*') {
      const acceptedTypes = accept.split(',').map(type => type.trim())
      const fileType = file.type
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

      const isAccepted = acceptedTypes.some(type => {
        if (type === fileType) return true
        if (type.endsWith('/*') && fileType.startsWith(type.slice(0, -1))) return true
        if (type === fileExtension) return true
        return false
      })

      if (!isAccepted) {
        return {
          valid: false,
          error: `File "${file.name}" không được hỗ trợ. Chỉ chấp nhận: ${accept}`
        }
      }
    }

    return { valid: true }
  }

  const uploadFile = async (file: File): Promise<UploadedFile> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('folder', folder)
    formData.append('tags', 'user-upload')
    formData.append('context', JSON.stringify({
      original_name: file.name,
      upload_timestamp: new Date().toISOString()
    }))

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.error || 'Upload failed')
    }

    return data.data
  }

  const handleFiles = useCallback(async (files: FileList) => {
    if (disabled) return

    const fileArray = Array.from(files)
    const validFiles: File[] = []
    const errors: string[] = []

    // Validate all files first
    fileArray.forEach(file => {
      const validation = validateFile(file)
      if (validation.valid) {
        validFiles.push(file)
      } else {
        errors.push(validation.error!)
      }
    })

    // Show validation errors
    if (errors.length > 0) {
      errors.forEach(error => {
        addToast({
          message: error,
          variant: 'error'
        })
      })
      if (onUploadError) {
        onUploadError(errors.join('; '))
      }
    }

    if (validFiles.length === 0) return

    // Initialize upload progress for valid files
    const newUploads: UploadProgress[] = validFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading'
    }))

    setUploads(prev => [...prev, ...newUploads])

    // Upload files
    const uploadPromises = validFiles.map(async (file, index) => {
      try {
        const result = await uploadFile(file)
        
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { ...upload, progress: 100, status: 'completed', result }
            : upload
        ))

        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { ...upload, status: 'error', error: errorMessage }
            : upload
        ))

        addToast({
          message: `Lỗi upload "${file.name}": ${errorMessage}`,
          variant: 'error'
        })

        throw error
      }
    })

    try {
      const results = await Promise.allSettled(uploadPromises)
      const successfulUploads = results
        .filter((result): result is PromiseFulfilledResult<UploadedFile> => 
          result.status === 'fulfilled'
        )
        .map(result => result.value)

      if (successfulUploads.length > 0) {
        addToast({
          message: `Upload thành công ${successfulUploads.length} file`,
          variant: 'success'
        })

        if (onUploadComplete) {
          onUploadComplete(successfulUploads)
        }
      }
    } catch (error) {
      console.error('Upload error:', error)
    }
  }, [disabled, maxSize, accept, folder, addToast, onUploadComplete, onUploadError])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFiles(files)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = e.dataTransfer.files
    if (files && files.length > 0) {
      handleFiles(files)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const removeUpload = (file: File) => {
    setUploads(prev => prev.filter(upload => upload.file !== file))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (file: File) => {
    const type = file.type.split('/')[0]
    switch (type) {
      case 'image': return '🖼️'
      case 'video': return '🎥'
      case 'audio': return '🎵'
      default: return '📄'
    }
  }

  return (
    <div className={className}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Upload Area */}
      <div
        onClick={openFileDialog}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragOver 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {children || (
          <div>
            <div className="text-4xl mb-4">📁</div>
            <p className="text-lg font-medium text-gray-900 mb-2">
              {isDragOver ? 'Thả file vào đây' : 'Chọn file để upload'}
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Hoặc kéo thả file vào đây
            </p>
            <Button disabled={disabled}>
              Chọn file
            </Button>
            <p className="text-xs text-gray-400 mt-2">
              Kích thước tối đa: {formatFileSize(maxSize)}
            </p>
          </div>
        )}
      </div>

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="font-medium text-gray-900">Tiến trình upload:</h4>
          {uploads.map((upload, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span>{getFileIcon(upload.file)}</span>
                  <span className="text-sm font-medium truncate">
                    {upload.file.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    ({formatFileSize(upload.file.size)})
                  </span>
                </div>
                <button
                  onClick={() => removeUpload(upload.file)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              {upload.status === 'uploading' && (
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${upload.progress}%` }}
                    />
                  </div>
                  <Loading size="sm" />
                </div>
              )}

              {upload.status === 'completed' && upload.result && (
                <div className="flex items-center gap-2 text-green-600">
                  <span>✅</span>
                  <span className="text-sm">Upload thành công</span>
                  <a 
                    href={upload.result.secure_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:underline"
                  >
                    Xem file
                  </a>
                </div>
              )}

              {upload.status === 'error' && (
                <div className="text-red-600 text-sm">
                  ❌ {upload.error}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default FileUpload
