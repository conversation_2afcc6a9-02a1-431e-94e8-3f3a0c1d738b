#!/usr/bin/env node

/**
 * Performance Optimization Audit Script for WebTA LMS
 * Analyzes performance optimization opportunities across the application
 */

const fs = require('fs')
const path = require('path')

console.log('⚡ Performance Optimization Audit - WebTA LMS')
console.log('=' .repeat(50))

// Audit results
let totalChecks = 0
let passedChecks = 0
const issues = []
const recommendations = []

function check(name, condition, details = '', recommendation = '') {
  totalChecks++
  const passed = condition
  if (passed) passedChecks++
  
  const status = passed ? '✅ PASS' : '⚠️  ISSUE'
  console.log(`${status} ${name}`)
  if (details) console.log(`   ${details}`)
  
  if (!passed) {
    issues.push({ name, details, recommendation })
    if (recommendation) {
      recommendations.push(recommendation)
    }
  }
}

// 1. Image Optimization
console.log('\n🖼️ 1. Image Optimization')
console.log('-'.repeat(40))

const pageFiles = [
  'src/app/page.tsx',
  'src/app/courses/page.tsx',
  'src/app/courses/[slug]/page.tsx',
  'src/app/dashboard/student/page.tsx',
  'src/app/dashboard/instructor/page.tsx'
]

let pagesWithNextImage = 0
let pagesWithImageOptimization = 0
let pagesWithLazyLoading = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('next/image') || content.includes('Image')) {
      pagesWithNextImage++
    }
    
    if (content.includes('priority') || content.includes('loading="lazy"')) {
      pagesWithImageOptimization++
    }
    
    if (content.includes('loading="lazy"') || content.includes('loading={')) {
      pagesWithLazyLoading++
    }
  }
})

check(
  'Next.js Image component usage',
  pagesWithNextImage >= 3,
  `${pagesWithNextImage} pages use Next.js Image component`,
  'Use Next.js Image component for automatic optimization'
)

check(
  'Image loading optimization',
  pagesWithImageOptimization >= 2,
  `${pagesWithImageOptimization} pages implement image loading optimization`,
  'Add priority/lazy loading attributes to images'
)

// 2. Code Splitting and Dynamic Imports
console.log('\n📦 2. Code Splitting and Dynamic Imports')
console.log('-'.repeat(40))

let filesWithDynamicImports = 0
let filesWithLazyComponents = 0

const componentFiles = [
  'src/components/ui/Modal.tsx',
  'src/components/payment/PaymentForm.tsx',
  'src/app/dashboard/student/page.tsx',
  'src/app/dashboard/instructor/page.tsx'
]

componentFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('dynamic(') || content.includes('lazy(')) {
      filesWithDynamicImports++
    }
    
    if (content.includes('Suspense') || content.includes('loading')) {
      filesWithLazyComponents++
    }
  }
})

check(
  'Dynamic imports implementation',
  filesWithDynamicImports >= 1,
  `${filesWithDynamicImports} files use dynamic imports`,
  'Implement dynamic imports for heavy components'
)

check(
  'Lazy loading components',
  filesWithLazyComponents >= 2,
  `${filesWithLazyComponents} files implement lazy loading`,
  'Add Suspense boundaries for better loading experience'
)

// 3. Bundle Size Optimization
console.log('\n📊 3. Bundle Size Optimization')
console.log('-'.repeat(40))

// Check package.json for bundle analysis tools
const packageJsonPath = path.join(__dirname, '..', 'package.json')
let hasBundleAnalyzer = false
let hasTreeShaking = false

if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  
  hasBundleAnalyzer = packageJson.devDependencies && 
    (packageJson.devDependencies['@next/bundle-analyzer'] || 
     packageJson.devDependencies['webpack-bundle-analyzer'])
  
  // Check for tree-shaking friendly imports
  hasTreeShaking = packageJson.dependencies && 
    Object.keys(packageJson.dependencies).some(dep => 
      dep.includes('lodash-es') || dep.includes('date-fns')
    )
}

check(
  'Bundle analyzer setup',
  hasBundleAnalyzer,
  'Bundle analyzer tool is configured',
  'Add @next/bundle-analyzer for bundle size monitoring'
)

// Check for tree-shaking patterns in code
let filesWithTreeShaking = 0
pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for named imports instead of default imports
    if (content.includes('import {') && !content.includes('import * as')) {
      filesWithTreeShaking++
    }
  }
})

check(
  'Tree-shaking friendly imports',
  filesWithTreeShaking >= pageFiles.length * 0.8,
  `${filesWithTreeShaking}/${pageFiles.length} files use named imports`,
  'Use named imports instead of default imports for better tree-shaking'
)

// 4. Caching Strategies
console.log('\n💾 4. Caching Strategies')
console.log('-'.repeat(40))

// Check for caching implementations
let hasApiCaching = false
let hasStaticGeneration = false
let hasRevalidation = false

// Check API routes for caching
const apiFiles = [
  'src/app/api/courses/route.ts',
  'src/app/api/dashboard/student/route.ts',
  'src/app/api/dashboard/instructor/route.ts'
]

apiFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('cache') || content.includes('revalidate')) {
      hasApiCaching = true
    }
  }
})

// Check for static generation
pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('generateStaticParams') || content.includes('getStaticProps')) {
      hasStaticGeneration = true
    }
    
    if (content.includes('revalidate') || content.includes('ISR')) {
      hasRevalidation = true
    }
  }
})

check(
  'API response caching',
  hasApiCaching,
  'API routes implement caching strategies',
  'Add caching headers and revalidation to API routes'
)

check(
  'Static generation usage',
  hasStaticGeneration,
  'Pages use static generation where appropriate',
  'Implement static generation for static content'
)

// 5. Database Query Optimization
console.log('\n🗄️ 5. Database Query Optimization')
console.log('-'.repeat(40))

let hasQueryOptimization = false
let hasIndexing = false
let hasLeanQueries = false

// Check API files for query optimization
apiFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('.lean()') || content.includes('.select(')) {
      hasLeanQueries = true
    }
    
    if (content.includes('.populate(') && content.includes('select:')) {
      hasQueryOptimization = true
    }
  }
})

// Check models for indexing
const modelFiles = [
  'src/models/Course.ts',
  'src/models/User.ts',
  'src/models/Enrollment.ts',
  'src/models/Payment.ts'
]

modelFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('index:') || content.includes('createIndex')) {
      hasIndexing = true
    }
  }
})

check(
  'Database query optimization',
  hasQueryOptimization,
  'Database queries use selective population',
  'Optimize database queries with selective field population'
)

check(
  'Database indexing',
  hasIndexing,
  'Database models have proper indexing',
  'Add database indexes for frequently queried fields'
)

check(
  'Lean queries usage',
  hasLeanQueries,
  'Database queries use lean() for better performance',
  'Use .lean() for read-only queries to improve performance'
)

// 6. Loading States and UX
console.log('\n⏳ 6. Loading States and UX')
console.log('-'.repeat(40))

let pagesWithLoadingStates = 0
let pagesWithErrorBoundaries = 0
let pagesWithSkeleton = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('loading') || content.includes('Loading')) {
      pagesWithLoadingStates++
    }
    
    if (content.includes('error') || content.includes('Error')) {
      pagesWithErrorBoundaries++
    }
    
    if (content.includes('skeleton') || content.includes('Skeleton')) {
      pagesWithSkeleton++
    }
  }
})

check(
  'Loading states implementation',
  pagesWithLoadingStates >= pageFiles.length * 0.8,
  `${pagesWithLoadingStates}/${pageFiles.length} pages implement loading states`,
  'Add loading states to all async operations'
)

check(
  'Error handling',
  pagesWithErrorBoundaries >= pageFiles.length * 0.6,
  `${pagesWithErrorBoundaries}/${pageFiles.length} pages handle errors`,
  'Implement comprehensive error handling'
)

check(
  'Skeleton loading screens',
  pagesWithSkeleton >= 1,
  `${pagesWithSkeleton} pages use skeleton loading`,
  'Add skeleton screens for better perceived performance'
)

// 7. Asset Optimization
console.log('\n🎨 7. Asset Optimization')
console.log('-'.repeat(40))

// Check for font optimization
const layoutPath = 'src/app/layout.tsx'
let hasFontOptimization = false
let hasPreloading = false

if (fs.existsSync(path.join(__dirname, '..', layoutPath))) {
  const content = fs.readFileSync(path.join(__dirname, '..', layoutPath), 'utf8')
  
  if (content.includes('next/font') || content.includes('Google Fonts')) {
    hasFontOptimization = true
  }
  
  if (content.includes('preload') || content.includes('display=')) {
    hasPreloading = true
  }
}

check(
  'Font optimization',
  hasFontOptimization,
  'Fonts are optimized with Next.js font optimization',
  'Use Next.js font optimization for better performance'
)

// Check for CSS optimization
const hasCSSOpt = fs.existsSync(path.join(__dirname, '..', 'tailwind.config.ts'))

check(
  'CSS optimization setup',
  hasCSSOpt,
  'Tailwind CSS is configured for optimization',
  'Configure CSS purging and optimization'
)

// Summary
console.log('\n' + '='.repeat(50))
console.log('⚡ PERFORMANCE OPTIMIZATION AUDIT SUMMARY')
console.log('='.repeat(50))

const passRate = Math.round((passedChecks / totalChecks) * 100)
console.log(`Total Checks: ${totalChecks}`)
console.log(`Passed: ${passedChecks}`)
console.log(`Issues Found: ${totalChecks - passedChecks}`)
console.log(`Performance Score: ${passRate}%`)

if (passRate >= 90) {
  console.log('\n🎉 EXCELLENT! Performance optimization is very comprehensive!')
} else if (passRate >= 80) {
  console.log('\n✅ GOOD! Performance optimization is well implemented!')
} else if (passRate >= 70) {
  console.log('\n⚠️  FAIR! Some performance optimizations needed!')
} else {
  console.log('\n❌ POOR! Significant performance optimization required!')
}

// Issues and Recommendations
if (issues.length > 0) {
  console.log('\n⚠️  Issues Found:')
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.name}`)
    if (issue.details) console.log(`   ${issue.details}`)
    if (issue.recommendation) console.log(`   💡 ${issue.recommendation}`)
  })
}

if (recommendations.length > 0) {
  console.log('\n💡 Key Recommendations:')
  const uniqueRecommendations = [...new Set(recommendations)]
  uniqueRecommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`)
  })
}

console.log('\n📋 Performance Testing Checklist:')
console.log('1. Run Lighthouse audit on all major pages')
console.log('2. Test loading times on slow 3G connection')
console.log('3. Monitor Core Web Vitals (LCP, FID, CLS)')
console.log('4. Test with large datasets in dashboard')
console.log('5. Verify image loading performance')
console.log('6. Check bundle size with webpack-bundle-analyzer')

console.log('\n🚀 Performance Optimization Audit Complete!')

// Exit with appropriate code
process.exit(issues.length > 0 ? 1 : 0)
