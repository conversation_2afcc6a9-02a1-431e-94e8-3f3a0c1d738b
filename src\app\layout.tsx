import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { getServerSession } from 'next-auth'
import { SessionProvider } from 'next-auth/react'
import { authOptions } from '@/lib/auth'
import { ToastProvider } from '@/components/ui/Toast'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'WebTA LMS - Hệ thống Quản lý Học tập',
  description: 'Hệ thống LMS toàn diện cho việc bán và quản lý khóa học ngoại ngữ với tính năng đánh giá tự động bằng AI',
  keywords: ['LMS', 'học tập', 'ngoại ngữ', 'AI', 'đánh giá'],
  authors: [{ name: 'WebTA Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)

  return (
    <html lang="vi">
      <body className={inter.className}>
        <SessionProvider session={session}>
          <ToastProvider>
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-1">
                {children}
              </main>
              <Footer />
            </div>
          </ToastProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
