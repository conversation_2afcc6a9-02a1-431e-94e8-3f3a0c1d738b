(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[908],{9234:function(e,t,s){Promise.resolve().then(s.bind(s,7576))},166:function(e,t,s){"use strict";s.d(t,{default:function(){return n.a}});var r=s(5775),n=s.n(r)},5775:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=s(7043);s(7437),s(2265);let n=r._(s(5602));function a(e,t){var s;let r={loading:e=>{let{error:t,isLoading:s,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let a={...r,...t};return(0,n.default)({...a,modules:null==(s=a.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1523:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let r=s(8993);function n(e){let{reason:t,children:s}=e;if("undefined"==typeof window)throw new r.BailoutToCSRError(t);return s}},5602:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=s(7437),n=s(2265),a=s(1523),l=s(49);function i(e){return{default:e&&"default"in e?e.default:e}}let c={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},o=function(e){let t={...c,...e},s=(0,n.lazy)(()=>t.loader().then(i)),o=t.loading;function d(e){let i=o?(0,r.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,c=t.ssr?(0,r.jsxs)(r.Fragment,{children:["undefined"==typeof window?(0,r.jsx)(l.PreloadCss,{moduleIds:t.modules}):null,(0,r.jsx)(s,{...e})]}):(0,r.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(n.Suspense,{fallback:i,children:c})}return d.displayName="LoadableComponent",d}},49:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return a}});let r=s(7437),n=s(544);function a(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let s=(0,n.getExpectedRequestStore)("next/dynamic css"),a=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files.filter(e=>e.endsWith(".css"));a.push(...t)}}return 0===a.length?null:(0,r.jsx)(r.Fragment,{children:a.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:s.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},7576:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return j}});var r=s(7437),n=s(2265),a=s(605),l=s(9376),i=s(3145),c=s(7648),o=s(6334),d=s(757),u=s(8711),x=s(1215),m=s(4887),h=s(535),f=s(3448);let g=(0,h.j)("relative bg-white rounded-lg shadow-xl transform transition-all",{variants:{variant:{default:"bg-white",destructive:"bg-white border-l-4 border-red-500",success:"bg-white border-l-4 border-green-500",warning:"bg-white border-l-4 border-yellow-500",info:"bg-white border-l-4 border-blue-500"},size:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl"}},defaultVariants:{variant:"default",size:"md"}});function p(e){let{isOpen:t,onClose:s,title:a,children:l,variant:i,size:c,className:o,closeOnOverlayClick:d=!0,showCloseButton:u=!0}=e,[x,h]=n.useState(!1);if(n.useEffect(()=>(h(!0),()=>h(!1)),[]),n.useEffect(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),n.useEffect(()=>{let e=e=>{"Escape"===e.key&&s()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,s]),!x||!t)return null;let p=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:d?s:void 0,"aria-hidden":"true"}),(0,r.jsxs)("div",{className:(0,f.cn)(g({variant:i,size:c}),"w-full",o),role:"dialog","aria-modal":"true","aria-labelledby":a?"modal-title":void 0,children:[(a||u)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[a&&(0,r.jsx)("h2",{id:"modal-title",className:"text-lg font-semibold text-gray-900",children:a}),u&&(0,r.jsx)("button",{onClick:s,className:"p-1 rounded-full hover:bg-gray-100 transition-colors","aria-label":"Đ\xf3ng modal",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-6",children:l})]})]});return(0,m.createPortal)(p,document.body)}var b=s(9356);let v=(0,s(166).default)(()=>s.e(636).then(s.bind(s,9636)),{loadableGenerated:{webpack:()=>[9636]},loading:()=>(0,r.jsx)(x.gb,{text:"Đang tải form thanh to\xe1n..."}),ssr:!1});function j(e){var t,s;let{params:m}=e,{data:h}=(0,a.useSession)(),f=(0,l.useRouter)(),{addToast:g}=(0,b.pm)(),[j,y]=(0,n.useState)(null),[N,w]=(0,n.useState)(!0),[k,C]=(0,n.useState)(null),[D,L]=(0,n.useState)(!1),[P,z]=(0,n.useState)(!1),[M,S]=(0,n.useState)("overview");(0,n.useEffect)(()=>{let e=async()=>{try{w(!0),C(null);let e=await fetch("/api/courses/".concat(m.slug)),t=await e.json();t.success?y(t.data):C(t.error||"Kh\xf4ng t\xecm thấy kh\xf3a học")}catch(e){C("Lỗi kết nối server")}finally{w(!1)}};m.slug&&e()},[m.slug]);let _=async(e,t)=>{if(!h){f.push("/auth/signin");return}try{L(!0);let s=await fetch("/api/courses/".concat(null==j?void 0:j._id,"/enroll"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e,...t})}),r=await s.json();r.success?(g({message:"Đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng!",variant:"success"}),window.location.reload()):g({message:r.error||"Lỗi khi đăng k\xfd kh\xf3a học",variant:"error"})}catch(e){g({message:"Lỗi kết nối server",variant:"error"})}finally{L(!1),z(!1)}},E=e=>{let t=Math.floor(e/60),s=e%60;return t>0?"".concat(t,"h ").concat(s,"m"):"".concat(s,"m")};return N?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(x.gb,{size:"lg"})}):k||!j?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDE1E"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Kh\xf4ng t\xecm thấy kh\xf3a học"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:k}),(0,r.jsx)(c.default,{href:"/courses",children:(0,r.jsx)(o.z,{children:"Về danh s\xe1ch kh\xf3a học"})})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white shadow-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(u.C,{variant:"secondary",children:j.category.name}),(0,r.jsx)(u.C,{variant:"outline",children:j.level}),(0,r.jsx)(u.C,{variant:"outline",children:j.language})]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:j.title}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:j.shortDescription}),(0,r.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDC68‍\uD83C\uDFEB"}),(0,r.jsxs)("span",{children:[j.instructor.profile.firstName," ",j.instructor.profile.lastName]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDC65"}),(0,r.jsxs)("span",{children:[j.stats.totalStudents," học vi\xean"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDCDA"}),(0,r.jsxs)("span",{children:[j.stats.totalLessons," b\xe0i học"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:"⏱️"}),(0,r.jsx)("span",{children:E(j.stats.totalDuration)})]})]}),j.stats.totalRatings>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-yellow-400",children:"⭐"}),(0,r.jsx)("span",{className:"font-semibold ml-1",children:j.stats.averageRating.toFixed(1)})]}),(0,r.jsxs)("span",{className:"text-gray-500",children:["(",j.stats.totalRatings," đ\xe1nh gi\xe1)"]})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)(d.Zb,{className:"sticky top-4",children:[(0,r.jsx)("div",{className:"aspect-video bg-gray-200 rounded-t-lg relative overflow-hidden",children:j.thumbnail?(0,r.jsx)(i.default,{src:j.thumbnail,alt:j.title,fill:!0,className:"object-cover",priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full text-gray-400 text-4xl",children:"\uD83D\uDCDA"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:(t=j.pricing.basePrice,s=j.pricing.currency,0===t?"Miễn ph\xed":new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"===s?"VND":"USD"}).format(t))}),j.pricing.basePrice>0&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Thanh to\xe1n một lần"})]}),j.enrollment?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-green-800 mb-2",children:[(0,r.jsx)("span",{children:"✅"}),(0,r.jsx)("span",{className:"font-semibold",children:"Đ\xe3 đăng k\xfd"})]}),(0,r.jsxs)("div",{className:"text-sm text-green-700",children:["Tiến độ: ",j.enrollment.progress.completionPercentage,"% (",j.enrollment.progress.completedLessons,"/",j.enrollment.progress.totalLessons," b\xe0i học)"]})]}),(0,r.jsx)(o.z,{className:"w-full",onClick:()=>f.push("/learn/".concat(j.slug)),children:"Tiếp tục học"})]}):j.canEnroll?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(o.z,{className:"w-full",onClick:()=>{0===j.pricing.basePrice?_("free"):z(!0)},disabled:D,children:D?(0,r.jsx)(x.gb,{size:"sm"}):"Đăng k\xfd ngay"}),j.pricing.basePrice>0&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("button",{onClick:()=>z(!0),className:"text-sm text-primary hover:underline",children:"C\xf3 m\xe3 k\xedch hoạt?"})})]}):(0,r.jsx)("div",{className:"text-center text-gray-500",children:"Kh\xf3a học kh\xf4ng khả dụng"}),j.canEdit&&(0,r.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,r.jsx)(c.default,{href:"/instructor/courses/".concat(j.slug,"/edit"),children:(0,r.jsx)(o.z,{variant:"outline",className:"w-full",children:"Chỉnh sửa kh\xf3a học"})})})]})]})})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"flex space-x-8 px-6",children:[{id:"overview",label:"Tổng quan"},{id:"curriculum",label:"Chương tr\xecnh học"},{id:"instructor",label:"Giảng vi\xean"},{id:"reviews",label:"Đ\xe1nh gi\xe1"}].map(e=>(0,r.jsx)("button",{onClick:()=>S(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(M===e.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.label},e.id))})}),(0,r.jsxs)("div",{className:"p-6",children:["overview"===M&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"M\xf4 tả kh\xf3a học"}),(0,r.jsx)("div",{className:"prose max-w-none",children:(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:j.content.description})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Mục ti\xeau học tập"}),(0,r.jsx)("ul",{className:"space-y-2",children:j.content.objectives.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start gap-3",children:[(0,r.jsx)("span",{className:"text-green-500 mt-1",children:"✓"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},t))})]}),j.content.prerequisites.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Y\xeau cầu trước khi học"}),(0,r.jsx)("ul",{className:"space-y-2",children:j.content.prerequisites.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start gap-3",children:[(0,r.jsx)("span",{className:"text-blue-500 mt-1",children:"•"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},t))})]})]}),"curriculum"===M&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:"Chương tr\xecnh học"}),j.content.syllabus.map((e,t)=>(0,r.jsxs)(d.Zb,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("h4",{className:"font-semibold text-lg",children:["Tuần ",e.week,": ",e.title]}),(0,r.jsx)(u.C,{variant:"outline",children:E(e.duration)})]}),(0,r.jsx)("ul",{className:"space-y-1",children:e.topics.map((e,t)=>(0,r.jsxs)("li",{className:"text-gray-600 text-sm",children:["• ",e]},t))})]},t)),j.lessons.length>0&&(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsx)("h4",{className:"font-semibold text-lg mb-4",children:"Danh s\xe1ch b\xe0i học"}),(0,r.jsx)("div",{className:"space-y-2",children:j.lessons.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border ".concat(e.canAccess?"bg-white":"bg-gray-50"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500 w-8",children:e.order}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium ".concat(e.canAccess?"text-gray-900":"text-gray-500"),children:e.title}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.C,{variant:"outline",size:"sm",children:e.type}),e.settings.isPreview&&(0,r.jsx)(u.C,{variant:"secondary",size:"sm",children:"Xem trước"}),!e.canAccess&&(0,r.jsx)("span",{className:"text-gray-400",children:"\uD83D\uDD12"})]})]},e._id))})]})]}),"instructor"===M&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center",children:j.instructor.profile.avatar?(0,r.jsx)(i.default,{src:j.instructor.profile.avatar,alt:"".concat(j.instructor.profile.firstName," ").concat(j.instructor.profile.lastName),width:64,height:64,className:"rounded-full"}):(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold",children:[j.instructor.profile.firstName," ",j.instructor.profile.lastName]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Giảng vi\xean"})]})]}),j.instructor.profile.bio&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"Giới thiệu"}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:j.instructor.profile.bio})]})]}),"reviews"===M&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"⭐"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Đ\xe1nh gi\xe1 sẽ sớm c\xf3 mặt"}),(0,r.jsx)("p",{className:"text-gray-600",children:"T\xednh năng đ\xe1nh gi\xe1 đang được ph\xe1t triển"})]})})]})]})}),(0,r.jsx)(p,{isOpen:P,onClose:()=>z(!1),title:"Đăng k\xfd kh\xf3a học",size:"lg",children:(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)(x.gb,{text:"Đang tải form thanh to\xe1n..."}),children:(0,r.jsx)(v,{courseId:j._id,courseTitle:j.title,amount:j.pricing.basePrice,currency:j.pricing.currency,onSuccess:e=>{g({message:"Đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng!",variant:"success"}),z(!1),window.location.reload()},onError:e=>{g({message:e,variant:"error"})},onCancel:()=>z(!1)})})})]})}},8711:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var r=s(7437);s(2265);var n=s(535),a=s(3448);let l=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...n}=e;return(0,r.jsx)("div",{className:(0,a.cn)(l({variant:s}),t),...n})}},6334:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(7437),n=s(2265),a=s(535),l=s(3448);let i=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:s,variant:n,size:a,asChild:c=!1,...o}=e;return(0,r.jsx)("button",{className:(0,l.cn)(i({variant:n,size:a,className:s})),ref:t,...o})});c.displayName="Button"},757:function(e,t,s){"use strict";s.d(t,{Zb:function(){return c}});var r=s(7437),n=s(2265),a=s(535),l=s(3448);let i=(0,a.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),c=n.forwardRef((e,t)=>{let{className:s,variant:n,size:a,...c}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)(i({variant:n,size:a}),s),...c})});c.displayName="Card",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...n})}).displayName="CardHeader",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})}).displayName="CardTitle",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...n})}).displayName="CardDescription",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...n})}).displayName="CardContent",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},1215:function(e,t,s){"use strict";s.d(t,{gb:function(){return i}});var r=s(7437),n=s(535),a=s(3448);let l=(0,n.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function i(e){let{variant:t,size:s,className:n,text:i}=e;return(0,r.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",n),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:(0,a.cn)(l({variant:t,size:s}))}),i&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i})]})})}},9356:function(e,t,s){"use strict";s.d(t,{VW:function(){return c},pm:function(){return o}});var r=s(7437),n=s(2265),a=s(4887),l=s(3448);let i=(0,n.createContext)(void 0);function c(e){let{children:t}=e,[s,a]=(0,n.useState)([]),l=(0,n.useCallback)(e=>{let t=Math.random().toString(36).substr(2,9),s={id:t,duration:5e3,...e};a(e=>[...e,s]),s.duration&&s.duration>0&&setTimeout(()=>{c(t)},s.duration)},[]),c=(0,n.useCallback)(e=>{a(t=>t.filter(t=>t.id!==e))},[]),o=(0,n.useCallback)(()=>{a([])},[]);return(0,r.jsxs)(i.Provider,{value:{toasts:s,addToast:l,removeToast:c,clearToasts:o},children:[t,(0,r.jsx)(d,{})]})}function o(){let e=(0,n.useContext)(i);if(!e)throw Error("useToast must be used within a ToastProvider");return e}function d(){let{toasts:e}=o(),[t,s]=(0,n.useState)(!1);return(n.useEffect(()=>{s(!0)},[]),t&&0!==e.length)?(0,a.createPortal)((0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(e=>(0,r.jsx)(u,{toast:e},e.id))}),document.body):null}function u(e){let{toast:t}=e,{removeToast:s}=o(),[a,i]=(0,n.useState)(!1);n.useEffect(()=>{let e=setTimeout(()=>i(!0),10);return()=>clearTimeout(e)},[]);let c={default:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),success:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})},d=t.variant||"default";return(0,r.jsx)("div",{className:(0,l.cn)("max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border transition-all duration-300 transform",{default:"bg-white border-gray-200",success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[d],a?"translate-x-0 opacity-100":"translate-x-full opacity-0"),children:(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:(0,l.cn)("flex-shrink-0",{default:"text-gray-400",success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[d]),children:c[d]}),(0,r.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[t.title&&(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.title}),(0,r.jsx)("p",{className:(0,l.cn)("text-sm text-gray-500",t.title?"mt-1":""),children:t.message}),t.action&&(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsx)("button",{onClick:t.action.onClick,className:"text-sm font-medium text-primary hover:text-primary/80",children:t.action.label})})]}),(0,r.jsx)("div",{className:"ml-4 flex-shrink-0 flex",children:(0,r.jsxs)("button",{onClick:()=>{i(!1),setTimeout(()=>s(t.id),150)},className:"rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("span",{className:"sr-only",children:"Đ\xf3ng"}),(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})})]})})})}},3448:function(e,t,s){"use strict";s.d(t,{cn:function(){return a}});var r=s(1994),n=s(3335);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.m6)((0,r.W)(t))}}},function(e){e.O(0,[851,648,605,785,971,117,744],function(){return e(e.s=9234)}),_N_E=e.O()}]);