import mongoose, { Document, Schema } from 'mongoose'
import bcrypt from 'bcryptjs'

// Enum definitions
export enum UserRole {
  STUDENT = 'student',
  INSTRUCTOR = 'instructor',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification'
}

export enum LanguageLevel {
  BEGINNER = 'beginner',
  ELEMENTARY = 'elementary',
  INTERMEDIATE = 'intermediate',
  UPPER_INTERMEDIATE = 'upper_intermediate',
  ADVANCED = 'advanced',
  PROFICIENT = 'proficient'
}

// Interface definitions
export interface IUserProfile {
  firstName: string
  lastName: string
  dateOfBirth?: Date
  phoneNumber?: string
  address?: {
    street?: string
    city?: string
    state?: string
    country?: string
    zipCode?: string
  }
  avatar?: string
  bio?: string
  languagePreferences: {
    native: string[]
    learning: string[]
    currentLevel?: LanguageLevel
  }
  timezone?: string
}

export interface IUserPreferences {
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends'
    showProgress: boolean
    showAchievements: boolean
  }
  learning: {
    dailyGoal?: number // minutes per day
    reminderTime?: string // HH:MM format
    preferredDifficulty?: 'easy' | 'medium' | 'hard'
  }
}

export interface IUser extends Document {
  email: string
  password: string
  role: UserRole
  status: UserStatus
  profile: IUserProfile
  preferences: IUserPreferences
  emailVerified: boolean
  emailVerificationToken?: string
  passwordResetToken?: string
  passwordResetExpires?: Date
  lastLogin?: Date
  loginAttempts: number
  lockUntil?: Date
  createdAt: Date
  updatedAt: Date
  
  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>
  generatePasswordResetToken(): string
  generateEmailVerificationToken(): string
  isLocked(): boolean
  incrementLoginAttempts(): Promise<void>
}

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: [true, 'Email là bắt buộc'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email không hợp lệ']
  },
  password: {
    type: String,
    required: [true, 'Mật khẩu là bắt buộc'],
    minlength: [8, 'Mật khẩu phải có ít nhất 8 ký tự'],
    select: false // Không trả về password khi query
  },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.STUDENT
  },
  status: {
    type: String,
    enum: Object.values(UserStatus),
    default: UserStatus.PENDING_VERIFICATION
  },
  profile: {
    firstName: {
      type: String,
      required: [true, 'Tên là bắt buộc'],
      trim: true,
      maxlength: [50, 'Tên không được vượt quá 50 ký tự']
    },
    lastName: {
      type: String,
      required: [true, 'Họ là bắt buộc'],
      trim: true,
      maxlength: [50, 'Họ không được vượt quá 50 ký tự']
    },
    dateOfBirth: Date,
    phoneNumber: {
      type: String,
      match: [/^[+]?[\d\s\-\(\)]+$/, 'Số điện thoại không hợp lệ']
    },
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    },
    avatar: String,
    bio: {
      type: String,
      maxlength: [500, 'Bio không được vượt quá 500 ký tự']
    },
    languagePreferences: {
      native: [String],
      learning: [String],
      currentLevel: {
        type: String,
        enum: Object.values(LanguageLevel)
      }
    },
    timezone: String
  },
  preferences: {
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: false }
    },
    privacy: {
      profileVisibility: {
        type: String,
        enum: ['public', 'private', 'friends'],
        default: 'public'
      },
      showProgress: { type: Boolean, default: true },
      showAchievements: { type: Boolean, default: true }
    },
    learning: {
      dailyGoal: { type: Number, min: 5, max: 480 }, // 5 minutes to 8 hours
      reminderTime: String,
      preferredDifficulty: {
        type: String,
        enum: ['easy', 'medium', 'hard'],
        default: 'medium'
      }
    }
  },
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLogin: Date,
  loginAttempts: { type: Number, default: 0 },
  lockUntil: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
UserSchema.index({ email: 1 })
UserSchema.index({ 'profile.firstName': 1, 'profile.lastName': 1 })
UserSchema.index({ role: 1, status: 1 })
UserSchema.index({ createdAt: -1 })

// Virtual fields
UserSchema.virtual('profile.fullName').get(function() {
  return `${this.profile.firstName} ${this.profile.lastName}`
})

UserSchema.virtual('isAccountLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now())
})

// Pre-save middleware
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next()
  
  try {
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error: any) {
    next(error)
  }
})

// Instance methods
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password)
}

UserSchema.methods.generatePasswordResetToken = function(): string {
  const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  this.passwordResetToken = resetToken
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
  return resetToken
}

UserSchema.methods.generateEmailVerificationToken = function(): string {
  const verificationToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  this.emailVerificationToken = verificationToken
  return verificationToken
}

UserSchema.methods.isLocked = function(): boolean {
  return !!(this.lockUntil && this.lockUntil > Date.now())
}

UserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    })
  }
  
  const updates: any = { $inc: { loginAttempts: 1 } }
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 } // 2 hours
  }
  
  return this.updateOne(updates)
}

// Export model
export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema)
