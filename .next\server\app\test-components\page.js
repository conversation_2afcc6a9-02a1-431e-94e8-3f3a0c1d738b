(()=>{var e={};e.id=263,e.ids=[263],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44648:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(72202),s(39285),s(35866);var a=s(23191),r=s(88716),n=s(37922),l=s.n(n),i=s(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=["",{children:["test-components",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,72202)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\test-components\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\test-components\\page.tsx"],m="/test-components/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/test-components/page",pathname:"/test-components",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},69910:(e,t,s)=>{Promise.resolve().then(s.bind(s,37161))},33265:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var a=s(43353),r=s.n(a)},43353:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let a=s(91174);s(10326),s(17577);let r=a._(s(77028));function n(e,t){var s;let a={loading:e=>{let{error:t,isLoading:s,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let n={...a,...t};return(0,r.default)({...n,modules:null==(s=n.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},933:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}});let a=s(94129);function r(e){let{reason:t,children:s}=e;throw new a.BailoutToCSRError(t)}},77028:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let a=s(10326),r=s(17577),n=s(933),l=s(46618);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},s=(0,r.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,a.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.PreloadCss,{moduleIds:t.modules}),(0,a.jsx)(s,{...e})]}):(0,a.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(r.Suspense,{fallback:i,children:o})}return c.displayName="LoadableComponent",c}},46618:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return n}});let a=s(10326),r=s(54580);function n(e){let{moduleIds:t}=e,s=(0,r.getExpectedRequestStore)("next/dynamic css"),n=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files.filter(e=>e.endsWith(".css"));n.push(...t)}}return 0===n.length?null:(0,a.jsx)(a.Fragment,{children:n.map(e=>(0,a.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:s.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},37161:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(10326),r=s(17577),n=s(33265),l=s(47375),i=s(99837),o=s(16545);let d=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\test-components\\page.tsx -> @/components/ui/RichTextEditor"]},loading:()=>a.jsx(o.gb,{text:"Đang tải Rich Text Editor..."}),ssr:!1}),c=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\test-components\\page.tsx -> @/components/ui/VideoPlayer"]},loading:()=>a.jsx(o.gb,{text:"Đang tải Video Player..."}),ssr:!1}),m=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\test-components\\page.tsx -> @/components/ui/DataTable"]},loading:()=>a.jsx(o.gb,{text:"Đang tải Data Table..."}),ssr:!1}),u=[{id:1,name:"Nguyễn Văn A",email:"<EMAIL>",role:"student",status:"active",createdAt:"2024-01-15",score:85},{id:2,name:"Trần Thị B",email:"<EMAIL>",role:"instructor",status:"active",createdAt:"2024-01-10",score:92},{id:3,name:"L\xea Văn C",email:"<EMAIL>",role:"student",status:"inactive",createdAt:"2024-01-20",score:78},{id:4,name:"Phạm Thị D",email:"<EMAIL>",role:"admin",status:"active",createdAt:"2024-01-05",score:95},{id:5,name:"Ho\xe0ng Văn E",email:"<EMAIL>",role:"student",status:"active",createdAt:"2024-01-25",score:88}];function p(){let[e,t]=(0,r.useState)("<p>Đ\xe2y l\xe0 nội dung mẫu cho <strong>Rich Text Editor</strong>. Bạn c\xf3 thể <em>định dạng</em> văn bản, th\xeam <u>gạch ch\xe2n</u>, tạo danh s\xe1ch v\xe0 nhiều hơn nữa!</p>");return a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container-7xl space-y-12",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h1",{className:"heading-1 mb-4",children:"Advanced UI Components Demo"}),a.jsx("p",{className:"body-large text-gray-600",children:"Showcase c\xe1c components n\xe2ng cao của WebTA LMS"})]}),(0,a.jsxs)(l.Zb,{className:"card-padding-lg",children:[a.jsx("h2",{className:"heading-2 mb-6",children:"Rich Text Editor"}),a.jsx("p",{className:"body-medium text-gray-600 mb-4",children:"Editor WYSIWYG với đầy đủ t\xednh năng định dạng văn bản, hỗ trợ HTML v\xe0 c\xe1c shortcut phổ biến."}),a.jsx(r.Suspense,{fallback:a.jsx(o.gb,{text:"Đang tải Rich Text Editor..."}),children:a.jsx(d,{value:e,onChange:t,placeholder:"Nhập nội dung của bạn tại đ\xe2y...",minHeight:300,className:"mb-4"})}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded-lg",children:[a.jsx("h3",{className:"font-semibold mb-2",children:"HTML Output:"}),a.jsx("pre",{className:"text-xs text-gray-600 whitespace-pre-wrap",children:e})]})]}),(0,a.jsxs)(l.Zb,{className:"card-padding-lg",children:[a.jsx("h2",{className:"heading-2 mb-6",children:"Video Player"}),a.jsx("p",{className:"body-medium text-gray-600 mb-4",children:"Video player t\xf9y chỉnh với controls đầy đủ, hỗ trợ fullscreen, speed control v\xe0 progress tracking."}),a.jsx(r.Suspense,{fallback:a.jsx(o.gb,{text:"Đang tải Video Player..."}),children:a.jsx(c,{src:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",poster:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg",title:"Big Buck Bunny - Sample Video",className:"aspect-video max-w-4xl mx-auto",onTimeUpdate:(e,t)=>{console.log(`Video progress: ${e}/${t}`)},onPlay:()=>console.log("Video started playing"),onPause:()=>console.log("Video paused"),onEnded:()=>console.log("Video ended")})}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[a.jsx("p",{children:a.jsx("strong",{children:"Features:"})}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[a.jsx("li",{children:"Custom controls với play/pause, volume, progress bar"}),a.jsx("li",{children:"Fullscreen support"}),a.jsx("li",{children:"Playback speed control (0.5x - 2x)"}),a.jsx("li",{children:"Keyboard shortcuts (Space: play/pause, F: fullscreen)"}),a.jsx("li",{children:"Progress tracking v\xe0 event callbacks"}),a.jsx("li",{children:"Responsive design"})]})]})]}),(0,a.jsxs)(l.Zb,{className:"card-padding-lg",children:[a.jsx("h2",{className:"heading-2 mb-6",children:"Data Table"}),a.jsx("p",{className:"body-medium text-gray-600 mb-4",children:"Bảng dữ liệu với t\xednh năng sorting, filtering, pagination v\xe0 custom rendering."}),a.jsx(r.Suspense,{fallback:a.jsx(o.gb,{text:"Đang tải Data Table..."}),children:a.jsx(m,{data:u,columns:[{key:"id",title:"ID",sortable:!0,width:80,align:"center"},{key:"name",title:"Họ v\xe0 t\xean",sortable:!0,filterable:!0,render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm mr-3",children:e.charAt(0)}),a.jsx("span",{className:"font-medium",children:e})]})},{key:"email",title:"Email",sortable:!0,filterable:!0,render:e=>a.jsx("a",{href:`mailto:${e}`,className:"text-blue-600 hover:underline",children:e})},{key:"role",title:"Vai tr\xf2",sortable:!0,filterable:!0,render:e=>a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{student:"bg-blue-100 text-blue-800",instructor:"bg-green-100 text-green-800",admin:"bg-purple-100 text-purple-800"}[e]}`,children:{student:"Học vi\xean",instructor:"Giảng vi\xean",admin:"Quản trị"}[e]})},{key:"status",title:"Trạng th\xe1i",sortable:!0,filterable:!0,render:e=>a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"active"===e?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===e?"Hoạt động":"Kh\xf4ng hoạt động"})},{key:"score",title:"Điểm số",sortable:!0,align:"center",render:e=>a.jsx("div",{className:"flex items-center justify-center",children:a.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold ${e>=90?"bg-green-500":e>=80?"bg-blue-500":e>=70?"bg-yellow-500":"bg-red-500"}`,children:e})})},{key:"createdAt",title:"Ng\xe0y tạo",sortable:!0,render:e=>new Date(e).toLocaleDateString("vi-VN")}],pageSize:3,showPagination:!0,showSearch:!0,searchPlaceholder:"T\xecm kiếm người d\xf9ng...",onRowClick:e=>{alert(`Clicked on: ${e.name} (${e.email})`)},rowClassName:e=>"inactive"===e.status?"opacity-60":"",className:"mt-4"})}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[a.jsx("p",{children:a.jsx("strong",{children:"Features:"})}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[a.jsx("li",{children:"Sorting theo column (click v\xe0o header)"}),a.jsx("li",{children:"Global search v\xe0 column-specific filtering"}),a.jsx("li",{children:"Pagination với navigation"}),a.jsx("li",{children:"Custom cell rendering"}),a.jsx("li",{children:"Row click events"}),a.jsx("li",{children:"Responsive design"}),a.jsx("li",{children:"Loading states v\xe0 empty states"})]})]})]}),(0,a.jsxs)(l.Zb,{className:"card-padding-lg",children:[a.jsx("h2",{className:"heading-2 mb-6",children:"Usage Examples"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"heading-3 mb-3",children:"Rich Text Editor"}),a.jsx("pre",{className:"bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto",children:`import RichTextEditor from '@/components/ui/RichTextEditor'

function MyComponent() {
  const [content, setContent] = useState('')
  
  return (
    <RichTextEditor
      value={content}
      onChange={setContent}
      placeholder="Nhập nội dung..."
      minHeight={200}
    />
  )
}`})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"heading-3 mb-3",children:"Video Player"}),a.jsx("pre",{className:"bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto",children:`import VideoPlayer from '@/components/ui/VideoPlayer'

function MyComponent() {
  return (
    <VideoPlayer
      src="/path/to/video.mp4"
      poster="/path/to/poster.jpg"
      title="Video Title"
      onTimeUpdate={(current, duration) => {
        // Track progress
      }}
    />
  )
}`})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"heading-3 mb-3",children:"Data Table"}),a.jsx("pre",{className:"bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto",children:`import DataTable from '@/components/ui/DataTable'

const columns = [
  { key: 'name', title: 'Name', sortable: true },
  { key: 'email', title: 'Email', filterable: true },
  { 
    key: 'status', 
    title: 'Status',
    render: (value) => <Badge>{value}</Badge>
  }
]

function MyComponent() {
  return (
    <DataTable
      data={data}
      columns={columns}
      showPagination={true}
      showSearch={true}
    />
  )
}`})]})]})]}),a.jsx("div",{className:"text-center",children:a.jsx(i.z,{onClick:()=>window.history.back(),children:"← Quay lại"})})]})})}},47375:(e,t,s)=>{"use strict";s.d(t,{Zb:()=>o});var a=s(10326),r=s(17577),n=s(79360),l=s(51223);let i=(0,n.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),o=r.forwardRef(({className:e,variant:t,size:s,...r},n)=>a.jsx("div",{ref:n,className:(0,l.cn)(i({variant:t,size:s}),e),...r}));o.displayName="Card",r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.cn)("p-6 pt-0",e),...t})).displayName="CardContent",r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},16545:(e,t,s)=>{"use strict";s.d(t,{gb:()=>i});var a=s(10326),r=s(79360),n=s(51223);let l=(0,r.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function i({variant:e,size:t,className:s,text:r}){return a.jsx("div",{className:(0,n.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[a.jsx("div",{className:(0,n.cn)(l({variant:e,size:t}))}),r&&a.jsx("p",{className:"text-sm text-gray-600",children:r})]})})}},72202:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\test-components\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,105,826],()=>s(44648));module.exports=a})();