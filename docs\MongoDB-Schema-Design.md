# MongoDB Schema Design - <PERSON><PERSON> thống LMS

## Tổng quan Database Architecture

### Database: `lms_database`
- **Collections chính**: 12 collections
- **Indexing strategy**: Compound indexes cho performance
- **Relationships**: Reference-based với selective embedding
- **Sharding**: <PERSON><PERSON>n bị cho horizontal scaling

---

## 1. USERS COLLECTION

### Collection: `users`

```javascript
{
  _id: ObjectId,
  email: String, // required, unique, lowercase
  password: String, // hashed với bcrypt
  profile: {
    fullname: String, // combined first and last name
    avatar: String, // URL to image
    phone: String,
    dateOfBirth: Date,
    gender: String, // enum: ['male', 'female', 'other']
    address: {
      street: String,
      city: String,
      country: String,
      zipCode: String
    },
    language: String, // default: 'vi'
    timezone: String // default: 'Asia/Ho_Chi_Minh'
  },
  role: String, // enum: ['student', 'teacher', 'admin',]
  roleSpecific: {
    // Cho student
    studentInfo: {
      level: String, // enum: ['beginner', 'intermediate', 'advanced']
      targetLanguage: String,
      studyGoals: [String],
      parentEmail: String, // optional cho học viên nhỏ tuổi
      schoolName: String
    },
    // Cho teacher
    teacherInfo: {
      specializations: [String],
      experience: Number, // years
      certifications: [String],
      bio: String,
      hourlyRate: Number
    },
    // Cho admin
    adminInfo: {
      permissions: [String],
      department: String,
      managedRegions: [String]
    }
  },
  subscription: {
    plan: String, // enum: ['free', 'basic', 'premium', 'enterprise']
    status: String, // enum: ['active', 'inactive', 'suspended', 'cancelled']
    startDate: Date,
    endDate: Date,
    autoRenew: Boolean,
    paymentMethod: String
  },
  preferences: {
    notifications: {
      email: Boolean, // default: true
      push: Boolean, // default: true
      sms: Boolean, // default: false
      reminders: Boolean // default: true
    },
    privacy: {
      profileVisible: Boolean, // default: true
      showInLeaderboard: Boolean, // default: true
      allowMessages: Boolean // default: true
    }
  },
  status: String, // enum: ['active', 'inactive', 'suspended', 'deleted']
  emailVerified: Boolean, // default: false
  lastLogin: Date,
  loginAttempts: Number, // default: 0
  lockUntil: Date, // account lock timestamp
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Users Collection
```javascript
// Unique indexes
db.users.createIndex({ "email": 1 }, { unique: true })

// Compound indexes cho performance
db.users.createIndex({ "role": 1, "status": 1 })
db.users.createIndex({ "subscription.plan": 1, "subscription.status": 1 })
db.users.createIndex({ "createdAt": -1 })
db.users.createIndex({ "lastLogin": -1 })

// Text search index
db.users.createIndex({ 
  "profile.firstName": "text", 
  "profile.lastName": "text",
  "email": "text" 
})
```

---

## 2. COURSES COLLECTION

### Collection: `courses`

```javascript
{
  _id: ObjectId,
  title: String, // required
  slug: String, // unique, URL-friendly
  description: String,
  shortDescription: String, // for cards/previews
  thumbnail: String, // URL to course image
  category: String, // enum: ['english', 'japanese', 'korean', 'chinese']
  level: String, // enum: ['beginner', 'intermediate', 'advanced']
  language: String, // course language, default: 'vi'
  
  instructor: {
    userId: ObjectId, // reference to users collection
    name: String, // denormalized for performance
    avatar: String // denormalized for performance
  },
  
  pricing: {
    type: String, // enum: ['free', 'paid', 'subscription']
    price: Number, // in VND
    originalPrice: Number, // for discount display
    currency: String, // default: 'VND'
    discountPercentage: Number
  },
  
  content: {
    totalLessons: Number,
    totalDuration: Number, // in minutes
    totalQuizzes: Number,
    totalAssignments: Number,
    modules: [
      {
        _id: ObjectId,
        title: String,
        description: String,
        order: Number,
        lessons: [
          {
            _id: ObjectId,
            title: String,
            type: String, // enum: ['video', 'audio', 'text', 'quiz', 'assignment']
            duration: Number, // in minutes
            order: Number,
            content: {
              // For video lessons
              videoUrl: String,
              subtitles: [
                {
                  language: String,
                  url: String
                }
              ],
              // For audio lessons
              audioUrl: String,
              transcript: String,
              // For text lessons
              htmlContent: String,
              attachments: [String], // URLs to files
              // For quizzes
              quizId: ObjectId, // reference to quizzes collection
              // For assignments
              assignmentId: ObjectId // reference to assignments collection
            },
            prerequisites: [ObjectId], // lesson IDs
            isPreview: Boolean, // free preview lesson
            resources: [
              {
                title: String,
                url: String,
                type: String // enum: ['pdf', 'doc', 'link', 'image']
              }
            ]
          }
        ]
      }
    ]
  },
  
  skills: {
    listening: Number, // percentage focus 0-100
    speaking: Number,
    reading: Number,
    writing: Number
  },
  
  requirements: [String], // course prerequisites
  outcomes: [String], // what students will learn
  
  settings: {
    isPublished: Boolean, // default: false
    allowComments: Boolean, // default: true
    allowRatings: Boolean, // default: true
    certificateEnabled: Boolean, // default: false
    maxStudents: Number, // enrollment limit
    enrollmentDeadline: Date
  },
  
  stats: {
    totalEnrollments: Number, // default: 0
    averageRating: Number, // default: 0
    totalRatings: Number, // default: 0
    completionRate: Number, // default: 0
    totalRevenue: Number // default: 0
  },
  
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String]
  },
  
  createdAt: Date, // default: new Date()
  updatedAt: Date, // default: new Date()
  publishedAt: Date
}
```

### Indexes cho Courses Collection
```javascript
// Unique indexes
db.courses.createIndex({ "slug": 1 }, { unique: true })

// Compound indexes
db.courses.createIndex({ "category": 1, "level": 1, "settings.isPublished": 1 })
db.courses.createIndex({ "instructor.userId": 1, "settings.isPublished": 1 })
db.courses.createIndex({ "pricing.type": 1, "pricing.price": 1 })
db.courses.createIndex({ "stats.averageRating": -1, "stats.totalRatings": -1 })
db.courses.createIndex({ "createdAt": -1 })

// Text search index
db.courses.createIndex({ 
  "title": "text", 
  "description": "text",
  "shortDescription": "text" 
})
```

---

## 3. ENROLLMENTS COLLECTION

### Collection: `enrollments`

```javascript
{
  _id: ObjectId,
  userId: ObjectId, // reference to users
  courseId: ObjectId, // reference to courses
  
  enrollmentInfo: {
    enrolledAt: Date, // default: new Date()
    activationCode: String, // mã kích hoạt đã sử dụng
    paymentId: ObjectId, // reference to payments collection
    source: String, // enum: ['direct', 'activation_code', 'gift', 'promotion']
    expiresAt: Date // for time-limited access
  },
  
  progress: {
    status: String, // enum: ['enrolled', 'in_progress', 'completed', 'dropped']
    completionPercentage: Number, // default: 0
    currentModule: Number, // default: 0
    currentLesson: Number, // default: 0
    totalTimeSpent: Number, // in minutes
    lastAccessedAt: Date,
    completedAt: Date,
    
    lessonsCompleted: [
      {
        lessonId: ObjectId,
        completedAt: Date,
        timeSpent: Number, // in minutes
        score: Number // if applicable
      }
    ],
    
    modulesCompleted: [
      {
        moduleId: ObjectId,
        completedAt: Date,
        score: Number
      }
    ]
  },
  
  performance: {
    overallScore: Number, // average of all assessments
    skillScores: {
      listening: Number,
      speaking: Number,
      reading: Number,
      writing: Number
    },
    
    assessmentHistory: [
      {
        assessmentId: ObjectId,
        type: String, // enum: ['quiz', 'assignment', 'final_exam']
        score: Number,
        maxScore: Number,
        completedAt: Date,
        timeSpent: Number,
        attempts: Number
      }
    ],
    
    strengths: [String], // identified strong areas
    weaknesses: [String], // areas needing improvement
    recommendations: [String] // AI-generated recommendations
  },
  
  certificate: {
    issued: Boolean, // default: false
    issuedAt: Date,
    certificateId: String, // unique certificate number
    certificateUrl: String // URL to certificate PDF
  },
  
  notes: String, // student's personal notes
  bookmarks: [ObjectId], // lesson IDs
  
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Enrollments Collection
```javascript
// Compound indexes
db.enrollments.createIndex({ "userId": 1, "courseId": 1 }, { unique: true })
db.enrollments.createIndex({ "userId": 1, "progress.status": 1 })
db.enrollments.createIndex({ "courseId": 1, "progress.status": 1 })
db.enrollments.createIndex({ "enrollmentInfo.enrolledAt": -1 })
db.enrollments.createIndex({ "progress.lastAccessedAt": -1 })
```

---

## 4. ASSESSMENTS COLLECTION

### Collection: `assessments`

```javascript
{
  _id: ObjectId,
  title: String, // required
  type: String, // enum: ['listening', 'speaking', 'reading', 'writing', 'mixed']
  courseId: ObjectId, // reference to courses, null for standalone assessments
  lessonId: ObjectId, // reference to specific lesson, optional
  
  configuration: {
    level: String, // enum: ['beginner', 'intermediate', 'advanced']
    duration: Number, // in minutes
    maxAttempts: Number, // default: 3
    passingScore: Number, // percentage
    randomizeQuestions: Boolean, // default: true
    showResults: String, // enum: ['immediate', 'after_submission', 'manual']
    allowReview: Boolean // default: true
  },
  
  questions: [
    {
      _id: ObjectId,
      type: String, // enum: ['multiple_choice', 'true_false', 'fill_blank', 'essay', 'speaking', 'listening_comprehension']
      skill: String, // enum: ['listening', 'speaking', 'reading', 'writing']
      difficulty: String, // enum: ['easy', 'medium', 'hard']
      points: Number,
      order: Number,
      
      content: {
        // Common fields
        question: String,
        instructions: String,
        
        // For listening questions
        audioUrl: String,
        audioTranscript: String, // for admin reference
        
        // For reading questions
        passage: String,
        
        // For multiple choice
        options: [
          {
            id: String,
            text: String,
            isCorrect: Boolean
          }
        ],
        
        // For fill in the blanks
        blanks: [
          {
            position: Number,
            correctAnswers: [String], // multiple acceptable answers
            caseSensitive: Boolean
          }
        ],
        
        // For essay/speaking
        rubric: {
          criteria: [
            {
              name: String, // e.g., "Grammar", "Vocabulary", "Fluency"
              description: String,
              maxPoints: Number
            }
          ],
          aiScoringEnabled: Boolean
        },
        
        // For speaking questions
        recordingTimeLimit: Number, // in seconds
        preparationTime: Number, // in seconds
        
        // Media attachments
        images: [String], // URLs
        videos: [String] // URLs
      },
      
      aiScoring: {
        enabled: Boolean,
        model: String, // 'openai' or 'google'
        prompt: String, // custom scoring prompt
        criteria: [String] // specific criteria for AI to evaluate
      }
    }
  ],
  
  settings: {
    isActive: Boolean, // default: true
    availableFrom: Date,
    availableUntil: Date,
    requiresProctor: Boolean, // default: false
    allowCalculator: Boolean, // default: false
    allowDictionary: Boolean // default: false
  },
  
  createdBy: ObjectId, // reference to users (teacher/admin)
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

---

## 5. ASSESSMENT_RESULTS COLLECTION

### Collection: `assessment_results`

```javascript
{
  _id: ObjectId,
  userId: ObjectId, // reference to users
  assessmentId: ObjectId, // reference to assessments
  enrollmentId: ObjectId, // reference to enrollments, optional
  
  attempt: {
    attemptNumber: Number,
    startedAt: Date,
    submittedAt: Date,
    timeSpent: Number, // in seconds
    ipAddress: String,
    userAgent: String
  },
  
  responses: [
    {
      questionId: ObjectId,
      response: {
        // For multiple choice
        selectedOption: String,
        
        // For fill in blanks
        answers: [String],
        
        // For essay
        text: String,
        wordCount: Number,
        
        // For speaking
        audioUrl: String,
        transcription: String, // AI-generated
        duration: Number, // in seconds
        
        // For listening
        comprehensionAnswers: [String]
      },
      
      scoring: {
        points: Number,
        maxPoints: Number,
        isCorrect: Boolean,
        
        // AI scoring details
        aiScore: {
          overall: Number,
          criteria: [
            {
              name: String,
              score: Number,
              maxScore: Number,
              feedback: String
            }
          ],
          confidence: Number, // AI confidence level 0-1
          model: String,
          processedAt: Date
        },
        
        // Manual scoring (if applicable)
        manualScore: {
          points: Number,
          feedback: String,
          scoredBy: ObjectId, // reference to users (teacher)
          scoredAt: Date
        }
      },
      
      timeSpent: Number // in seconds
    }
  ],
  
  results: {
    totalScore: Number,
    maxScore: Number,
    percentage: Number,
    grade: String, // enum: ['A', 'B', 'C', 'D', 'F']
    passed: Boolean,
    
    skillBreakdown: {
      listening: {
        score: Number,
        maxScore: Number,
        percentage: Number
      },
      speaking: {
        score: Number,
        maxScore: Number,
        percentage: Number,
        pronunciation: Number, // 0-100
        fluency: Number, // 0-100
        grammar: Number, // 0-100
        vocabulary: Number // 0-100
      },
      reading: {
        score: Number,
        maxScore: Number,
        percentage: Number,
        comprehension: Number,
        speed: Number // words per minute
      },
      writing: {
        score: Number,
        maxScore: Number,
        percentage: Number,
        grammar: Number,
        vocabulary: Number,
        coherence: Number,
        taskResponse: Number
      }
    },
    
    feedback: {
      overall: String,
      strengths: [String],
      improvements: [String],
      recommendations: [String],
      nextSteps: [String]
    }
  },
  
  status: String, // enum: ['in_progress', 'submitted', 'graded', 'reviewed']
  
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Assessment Results Collection
```javascript
// Compound indexes
db.assessment_results.createIndex({ "userId": 1, "assessmentId": 1, "attempt.attemptNumber": 1 })
db.assessment_results.createIndex({ "userId": 1, "createdAt": -1 })
db.assessment_results.createIndex({ "assessmentId": 1, "status": 1 })
db.assessment_results.createIndex({ "enrollmentId": 1, "createdAt": -1 })
```

---

## 6. ACTIVATION_CODES COLLECTION

### Collection: `activation_codes`

```javascript
{
  _id: ObjectId,
  code: String, // unique activation code
  type: String, // enum: ['course', 'subscription', 'bundle']
  
  // What this code grants access to
  grants: {
    courseIds: [ObjectId], // reference to courses
    subscriptionPlan: String, // enum: ['basic', 'premium', 'enterprise']
    subscriptionDuration: Number, // in days
    bundleId: ObjectId, // reference to course bundles
    accessDuration: Number // in days, null for lifetime
  },
  
  // Code configuration
  configuration: {
    batchId: String, // for bulk generation tracking
    generatedBy: ObjectId, // reference to users (admin)
    maxUses: Number, // default: 1
    currentUses: Number, // default: 0
    validFrom: Date,
    validUntil: Date,
    region: String, // geographic restriction
    userType: String // enum: ['any', 'new_only', 'existing_only']
  },
  
  // Usage tracking
  usage: [
    {
      userId: ObjectId,
      usedAt: Date,
      ipAddress: String,
      userAgent: String
    }
  ],
  
  // Sales tracking
  sales: {
    distributorId: ObjectId, // reference to users (distributor)
    originalPrice: Number,
    salePrice: Number,
    commission: Number,
    soldAt: Date,
    soldTo: String // customer info if not registered user
  },
  
  status: String, // enum: ['active', 'used', 'expired', 'disabled']
  
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Activation Codes Collection
```javascript
// Unique indexes
db.activation_codes.createIndex({ "code": 1 }, { unique: true })

// Compound indexes
db.activation_codes.createIndex({ "status": 1, "configuration.validUntil": 1 })
db.activation_codes.createIndex({ "configuration.batchId": 1 })
db.activation_codes.createIndex({ "sales.distributorId": 1, "createdAt": -1 })
db.activation_codes.createIndex({ "type": 1, "status": 1 })
```

---

## 7. GAMIFICATION COLLECTION

### Collection: `gamification`

```javascript
{
  _id: ObjectId,
  userId: ObjectId, // reference to users
  
  points: {
    total: Number, // default: 0
    available: Number, // points available to spend
    lifetime: Number, // total points ever earned
    
    history: [
      {
        points: Number, // positive for earned, negative for spent
        reason: String,
        description: String,
        sourceType: String, // enum: ['lesson', 'quiz', 'assignment', 'streak', 'achievement', 'purchase']
        sourceId: ObjectId, // reference to source document
        earnedAt: Date
      }
    ]
  },
  
  badges: [
    {
      badgeId: String, // unique badge identifier
      name: String,
      description: String,
      icon: String, // URL to badge image
      category: String, // enum: ['achievement', 'skill', 'streak', 'social']
      rarity: String, // enum: ['common', 'rare', 'epic', 'legendary']
      earnedAt: Date,
      progress: Number // for progressive badges
    }
  ],
  
  achievements: [
    {
      achievementId: String,
      name: String,
      description: String,
      type: String, // enum: ['course_completion', 'skill_mastery', 'streak', 'social']
      tier: Number, // 1, 2, 3 for bronze, silver, gold
      unlockedAt: Date,
      progress: {
        current: Number,
        target: Number,
        percentage: Number
      }
    }
  ],
  
  streaks: {
    current: {
      days: Number, // default: 0
      startDate: Date,
      lastActivityDate: Date
    },
    longest: {
      days: Number, // default: 0
      startDate: Date,
      endDate: Date
    },
    history: [
      {
        days: Number,
        startDate: Date,
        endDate: Date
      }
    ]
  },
  
  leaderboards: {
    global: {
      rank: Number,
      score: Number,
      lastUpdated: Date
    },
    course: [
      {
        courseId: ObjectId,
        rank: Number,
        score: Number,
        lastUpdated: Date
      }
    ],
    skill: [
      {
        skill: String, // enum: ['listening', 'speaking', 'reading', 'writing']
        rank: Number,
        score: Number,
        lastUpdated: Date
      }
    ]
  },
  
  challenges: [
    {
      challengeId: ObjectId,
      name: String,
      description: String,
      type: String, // enum: ['daily', 'weekly', 'monthly', 'special']
      status: String, // enum: ['active', 'completed', 'failed', 'expired']
      progress: {
        current: Number,
        target: Number,
        percentage: Number
      },
      reward: {
        points: Number,
        badges: [String],
        items: [String]
      },
      startDate: Date,
      endDate: Date,
      completedAt: Date
    }
  ],
  
  preferences: {
    showInLeaderboard: Boolean, // default: true
    receiveChallenge: Boolean, // default: true
    publicProfile: Boolean // default: true
  },
  
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Gamification Collection
```javascript
// Unique indexes
db.gamification.createIndex({ "userId": 1 }, { unique: true })

// Compound indexes for leaderboards
db.gamification.createIndex({ "leaderboards.global.score": -1 })
db.gamification.createIndex({ "points.total": -1 })
db.gamification.createIndex({ "streaks.current.days": -1 })
```

---

## PERFORMANCE OPTIMIZATION

### Aggregation Pipeline Examples

#### 1. Leaderboard Query
```javascript
// Global leaderboard
db.gamification.aggregate([
  { $match: { "preferences.showInLeaderboard": true } },
  { $lookup: {
      from: "users",
      localField: "userId",
      foreignField: "_id",
      as: "user"
  }},
  { $unwind: "$user" },
  { $project: {
      userId: 1,
      userName: "$user.profile.firstName",
      avatar: "$user.profile.avatar",
      totalPoints: "$points.total",
      currentStreak: "$streaks.current.days"
  }},
  { $sort: { totalPoints: -1 } },
  { $limit: 100 }
])
```

#### 2. Course Analytics
```javascript
// Course performance analytics
db.enrollments.aggregate([
  { $match: { courseId: ObjectId("course_id") } },
  { $group: {
      _id: "$courseId",
      totalEnrollments: { $sum: 1 },
      completedCount: { $sum: { $cond: [{ $eq: ["$progress.status", "completed"] }, 1, 0] } },
      averageProgress: { $avg: "$progress.completionPercentage" },
      averageScore: { $avg: "$performance.overallScore" }
  }},
  { $project: {
      totalEnrollments: 1,
      completedCount: 1,
      completionRate: { $multiply: [{ $divide: ["$completedCount", "$totalEnrollments"] }, 100] },
      averageProgress: { $round: ["$averageProgress", 2] },
      averageScore: { $round: ["$averageScore", 2] }
  }}
])
```

### Data Archiving Strategy

```javascript
// Archive old assessment results (older than 2 years)
const archiveDate = new Date();
archiveDate.setFullYear(archiveDate.getFullYear() - 2);

// Move to archive collection
db.assessment_results.aggregate([
  { $match: { createdAt: { $lt: archiveDate } } },
  { $out: "assessment_results_archive" }
])

// Remove from main collection
db.assessment_results.deleteMany({ createdAt: { $lt: archiveDate } })
```

---

## SECURITY CONSIDERATIONS

### Field-level Security
```javascript
// Sensitive fields that should be encrypted
const encryptedFields = [
  'users.password',
  'users.profile.phone',
  'activation_codes.code',
  'assessment_results.responses.audioUrl'
]

// PII fields requiring special handling
const piiFields = [
  'users.email',
  'users.profile.phone',
  'users.profile.address'
]
```

### Access Control Rules
```javascript
// Role-based field access
const fieldAccess = {
  student: {
    read: ['own_profile', 'enrolled_courses', 'own_results'],
    write: ['own_profile_limited']
  },
  teacher: {
    read: ['student_results', 'assigned_courses', 'class_analytics'],
    write: ['course_content', 'assessments', 'grades']
  },
  admin: {
    read: ['all_users', 'all_courses', 'system_analytics'],
    write: ['user_management', 'course_management', 'system_settings']
  }
}
```

---

## 8. PAYMENTS COLLECTION

### Collection: `payments`

```javascript
{
  _id: ObjectId,
  userId: ObjectId, // reference to users

  transaction: {
    transactionId: String, // unique transaction ID
    paymentMethod: String, // enum: ['stripe', 'paypal', 'bank_transfer', 'activation_code']
    gateway: String, // payment gateway used
    gatewayTransactionId: String, // gateway's transaction ID
    currency: String, // default: 'VND'
    exchangeRate: Number // if different from base currency
  },

  amount: {
    subtotal: Number,
    tax: Number,
    discount: Number,
    total: Number,
    refunded: Number // default: 0
  },

  items: [
    {
      type: String, // enum: ['course', 'subscription', 'bundle']
      itemId: ObjectId, // reference to course/subscription/bundle
      name: String,
      price: Number,
      quantity: Number, // default: 1
      discount: Number // default: 0
    }
  ],

  billing: {
    firstName: String,
    lastName: String,
    email: String,
    phone: String,
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    },
    company: String, // optional
    taxId: String // optional
  },

  status: String, // enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded']

  timeline: [
    {
      status: String,
      timestamp: Date,
      note: String,
      processedBy: ObjectId // reference to users (admin)
    }
  ],

  invoice: {
    invoiceNumber: String,
    invoiceUrl: String, // URL to PDF invoice
    issuedAt: Date
  },

  refund: {
    reason: String,
    amount: Number,
    processedAt: Date,
    processedBy: ObjectId, // reference to users (admin)
    refundId: String // gateway refund ID
  },

  metadata: {
    ipAddress: String,
    userAgent: String,
    source: String, // enum: ['web', 'mobile', 'admin']
    campaignId: String, // marketing campaign tracking
    affiliateId: ObjectId // reference to affiliate user
  },

  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Payments Collection
```javascript
// Unique indexes
db.payments.createIndex({ "transaction.transactionId": 1 }, { unique: true })

// Compound indexes
db.payments.createIndex({ "userId": 1, "status": 1, "createdAt": -1 })
db.payments.createIndex({ "status": 1, "createdAt": -1 })
db.payments.createIndex({ "transaction.paymentMethod": 1, "status": 1 })
db.payments.createIndex({ "createdAt": -1 })
```

---

## 9. NOTIFICATIONS COLLECTION

### Collection: `notifications`

```javascript
{
  _id: ObjectId,
  userId: ObjectId, // reference to users

  type: String, // enum: ['system', 'course', 'assessment', 'payment', 'social', 'reminder']
  category: String, // enum: ['info', 'success', 'warning', 'error']
  priority: String, // enum: ['low', 'medium', 'high', 'urgent']

  content: {
    title: String, // required
    message: String, // required
    actionText: String, // optional button text
    actionUrl: String, // optional action URL
    imageUrl: String, // optional notification image

    // Rich content for in-app notifications
    richContent: {
      html: String,
      data: Object // additional structured data
    }
  },

  channels: {
    inApp: {
      enabled: Boolean, // default: true
      read: Boolean, // default: false
      readAt: Date
    },
    email: {
      enabled: Boolean,
      sent: Boolean, // default: false
      sentAt: Date,
      emailId: String, // email service provider ID
      opened: Boolean, // default: false
      openedAt: Date
    },
    push: {
      enabled: Boolean,
      sent: Boolean, // default: false
      sentAt: Date,
      pushId: String, // push service ID
      clicked: Boolean, // default: false
      clickedAt: Date
    },
    sms: {
      enabled: Boolean,
      sent: Boolean, // default: false
      sentAt: Date,
      smsId: String, // SMS service ID
      delivered: Boolean // default: false
    }
  },

  targeting: {
    userIds: [ObjectId], // specific users
    roles: [String], // user roles
    courses: [ObjectId], // users enrolled in specific courses
    segments: [String], // user segments
    conditions: Object // complex targeting conditions
  },

  scheduling: {
    sendAt: Date, // scheduled send time
    timezone: String,
    recurring: {
      enabled: Boolean,
      pattern: String, // cron expression
      endDate: Date
    }
  },

  tracking: {
    impressions: Number, // default: 0
    clicks: Number, // default: 0
    conversions: Number, // default: 0

    events: [
      {
        event: String, // enum: ['sent', 'delivered', 'opened', 'clicked', 'converted']
        timestamp: Date,
        metadata: Object
      }
    ]
  },

  status: String, // enum: ['draft', 'scheduled', 'sending', 'sent', 'failed']

  createdBy: ObjectId, // reference to users (admin/system)
  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Notifications Collection
```javascript
// Compound indexes
db.notifications.createIndex({ "userId": 1, "channels.inApp.read": 1, "createdAt": -1 })
db.notifications.createIndex({ "status": 1, "scheduling.sendAt": 1 })
db.notifications.createIndex({ "type": 1, "createdAt": -1 })
db.notifications.createIndex({ "createdAt": -1 })
```

---

## 10. ANALYTICS COLLECTION

### Collection: `analytics`

```javascript
{
  _id: ObjectId,

  // Event tracking
  event: {
    name: String, // required - event name
    category: String, // enum: ['user', 'course', 'assessment', 'payment', 'system']
    action: String, // specific action taken
    label: String, // additional context
    value: Number // numeric value if applicable
  },

  // User context
  user: {
    userId: ObjectId, // reference to users, null for anonymous
    sessionId: String, // session identifier
    isAuthenticated: Boolean,
    role: String
  },

  // Page/screen context
  page: {
    url: String,
    title: String,
    referrer: String,
    path: String,
    queryParams: Object
  },

  // Device/browser context
  device: {
    userAgent: String,
    browser: String,
    browserVersion: String,
    os: String,
    osVersion: String,
    deviceType: String, // enum: ['desktop', 'tablet', 'mobile']
    screenResolution: String,
    language: String
  },

  // Geographic context
  location: {
    ipAddress: String,
    country: String,
    region: String,
    city: String,
    timezone: String,
    coordinates: {
      lat: Number,
      lng: Number
    }
  },

  // Course-specific context
  course: {
    courseId: ObjectId,
    lessonId: ObjectId,
    moduleId: ObjectId,
    progress: Number // completion percentage
  },

  // Assessment-specific context
  assessment: {
    assessmentId: ObjectId,
    questionId: ObjectId,
    skill: String, // enum: ['listening', 'speaking', 'reading', 'writing']
    score: Number,
    timeSpent: Number
  },

  // Custom properties
  properties: Object, // flexible object for additional data

  // Performance metrics
  performance: {
    loadTime: Number, // page load time in ms
    renderTime: Number, // time to render in ms
    apiResponseTime: Number // API response time in ms
  },

  timestamp: Date, // default: new Date()

  // Data processing flags
  processed: Boolean, // default: false
  processedAt: Date
}
```

### Indexes cho Analytics Collection
```javascript
// Time-based indexes for performance
db.analytics.createIndex({ "timestamp": -1 })
db.analytics.createIndex({ "event.category": 1, "timestamp": -1 })
db.analytics.createIndex({ "user.userId": 1, "timestamp": -1 })

// Event-specific indexes
db.analytics.createIndex({ "event.name": 1, "event.category": 1 })
db.analytics.createIndex({ "course.courseId": 1, "timestamp": -1 })
db.analytics.createIndex({ "assessment.assessmentId": 1, "timestamp": -1 })

// Processing index
db.analytics.createIndex({ "processed": 1, "timestamp": 1 })
```

---

## 11. SYSTEM_LOGS COLLECTION

### Collection: `system_logs`

```javascript
{
  _id: ObjectId,

  level: String, // enum: ['debug', 'info', 'warn', 'error', 'fatal']
  service: String, // service/module name
  component: String, // specific component

  message: String, // log message

  context: {
    userId: ObjectId, // if user-related
    sessionId: String,
    requestId: String, // unique request identifier
    correlationId: String, // for tracing across services

    // Request context
    method: String, // HTTP method
    url: String,
    statusCode: Number,
    responseTime: Number, // in milliseconds

    // Error context
    error: {
      name: String,
      message: String,
      stack: String,
      code: String
    },

    // Additional metadata
    metadata: Object
  },

  tags: [String], // for categorization and filtering

  timestamp: Date, // default: new Date()

  // Retention policy
  expiresAt: Date // TTL for automatic cleanup
}
```

### Indexes cho System Logs Collection
```javascript
// TTL index for automatic cleanup
db.system_logs.createIndex({ "expiresAt": 1 }, { expireAfterSeconds: 0 })

// Query indexes
db.system_logs.createIndex({ "level": 1, "timestamp": -1 })
db.system_logs.createIndex({ "service": 1, "timestamp": -1 })
db.system_logs.createIndex({ "context.userId": 1, "timestamp": -1 })
db.system_logs.createIndex({ "timestamp": -1 })
```

---

## 12. SETTINGS COLLECTION

### Collection: `settings`

```javascript
{
  _id: ObjectId,

  scope: String, // enum: ['global', 'user', 'course', 'organization']
  scopeId: ObjectId, // reference to specific scope (userId, courseId, etc.)

  category: String, // enum: ['system', 'ui', 'notifications', 'security', 'integrations']

  settings: {
    // System settings
    system: {
      maintenanceMode: Boolean,
      allowRegistration: Boolean,
      defaultLanguage: String,
      defaultTimezone: String,
      maxFileUploadSize: Number, // in MB
      sessionTimeout: Number, // in minutes

      // AI settings
      ai: {
        provider: String, // enum: ['openai', 'google']
        model: String,
        apiKey: String, // encrypted
        maxTokens: Number,
        temperature: Number
      },

      // Email settings
      email: {
        provider: String, // enum: ['sendgrid', 'mailgun', 'ses']
        apiKey: String, // encrypted
        fromEmail: String,
        fromName: String,
        templates: Object
      },

      // Storage settings
      storage: {
        provider: String, // enum: ['aws', 'cloudinary', 'local']
        bucket: String,
        region: String,
        accessKey: String, // encrypted
        secretKey: String // encrypted
      }
    },

    // UI/UX settings
    ui: {
      theme: String, // enum: ['light', 'dark', 'auto']
      language: String,
      dateFormat: String,
      timeFormat: String,
      currency: String,

      // Dashboard customization
      dashboard: {
        widgets: [String],
        layout: String
      }
    },

    // Notification preferences
    notifications: {
      email: {
        courseUpdates: Boolean,
        assessmentResults: Boolean,
        reminders: Boolean,
        marketing: Boolean
      },
      push: {
        enabled: Boolean,
        courseUpdates: Boolean,
        reminders: Boolean
      },
      sms: {
        enabled: Boolean,
        reminders: Boolean,
        security: Boolean
      }
    },

    // Security settings
    security: {
      twoFactorAuth: Boolean,
      passwordPolicy: {
        minLength: Number,
        requireUppercase: Boolean,
        requireLowercase: Boolean,
        requireNumbers: Boolean,
        requireSymbols: Boolean
      },
      sessionSecurity: {
        maxSessions: Number,
        ipWhitelist: [String]
      }
    },

    // Integration settings
    integrations: {
      googleAnalytics: {
        enabled: Boolean,
        trackingId: String
      },
      facebook: {
        enabled: Boolean,
        appId: String,
        appSecret: String // encrypted
      },
      zoom: {
        enabled: Boolean,
        apiKey: String, // encrypted
        apiSecret: String // encrypted
      }
    }
  },

  version: Number, // for settings versioning

  createdBy: ObjectId, // reference to users
  updatedBy: ObjectId, // reference to users

  createdAt: Date, // default: new Date()
  updatedAt: Date // default: new Date()
}
```

### Indexes cho Settings Collection
```javascript
// Unique compound index
db.settings.createIndex({ "scope": 1, "scopeId": 1, "category": 1 }, { unique: true })

// Query indexes
db.settings.createIndex({ "scope": 1, "category": 1 })
db.settings.createIndex({ "updatedAt": -1 })
```

---

## RELATIONSHIPS OVERVIEW

### Relationship Diagram
```
Users (1) ←→ (M) Enrollments (M) ←→ (1) Courses
Users (1) ←→ (M) Assessment_Results (M) ←→ (1) Assessments
Users (1) ←→ (1) Gamification
Users (1) ←→ (M) Payments
Users (1) ←→ (M) Activation_Codes (usage)
Users (1) ←→ (M) Notifications
Courses (1) ←→ (M) Assessments
Enrollments (1) ←→ (M) Assessment_Results
```

### Reference vs Embedded Strategy

#### References (ObjectId) - Sử dụng khi:
- Data có thể thay đổi thường xuyên
- Data được truy cập độc lập
- Cần tránh document size limit (16MB)
- Quan hệ many-to-many

#### Embedded Documents - Sử dụng khi:
- Data ít thay đổi
- Data luôn được truy cập cùng parent
- Quan hệ one-to-few
- Cần atomic updates

---

## DATABASE INITIALIZATION SCRIPT

```javascript
// Create database and collections with validation
use lms_database

// Users collection with validation
db.createCollection("users", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["email", "password", "profile", "role"],
      properties: {
        email: {
          bsonType: "string",
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        },
        role: {
          enum: ["student", "teacher", "admin", "super_admin"]
        },
        status: {
          enum: ["active", "inactive", "suspended", "deleted"]
        }
      }
    }
  }
})

// Create all indexes
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "role": 1, "status": 1 })
db.users.createIndex({ "subscription.plan": 1, "subscription.status": 1 })

// Courses collection
db.createCollection("courses")
db.courses.createIndex({ "slug": 1 }, { unique: true })
db.courses.createIndex({ "category": 1, "level": 1, "settings.isPublished": 1 })

// Continue for all collections...
```

---

## BACKUP & MAINTENANCE STRATEGY

### Daily Backups
```bash
# Full database backup
mongodump --uri="mongodb://localhost:27017/lms_database" --out="/backups/daily/$(date +%Y%m%d)"

# Incremental backup for large collections
mongodump --uri="mongodb://localhost:27017/lms_database" --collection="analytics" --query='{"timestamp":{"$gte":{"$date":"2024-01-01T00:00:00.000Z"}}}'
```

### Index Maintenance
```javascript
// Rebuild indexes monthly
db.runCommand({reIndex: "users"})
db.runCommand({reIndex: "enrollments"})
db.runCommand({reIndex: "assessment_results"})

// Check index usage
db.users.aggregate([{$indexStats:{}}])
```

### Data Cleanup
```javascript
// Clean up old analytics data (older than 1 year)
const oneYearAgo = new Date();
oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

db.analytics.deleteMany({
  timestamp: { $lt: oneYearAgo },
  processed: true
})

// Archive old system logs
db.system_logs.deleteMany({
  timestamp: { $lt: oneYearAgo },
  level: { $in: ["debug", "info"] }
})
```

---

## KẾT LUẬN

Database schema này được thiết kế để:

✅ **Scalability**: Hỗ trợ millions users và courses
✅ **Performance**: Optimized indexes cho các query phổ biến
✅ **Flexibility**: Schema linh hoạt cho future requirements
✅ **Security**: Field-level security và data encryption
✅ **Analytics**: Comprehensive tracking và reporting
✅ **Maintenance**: Automated cleanup và archiving strategies

**Các bước triển khai tiếp theo:**
1. Setup MongoDB cluster với replica sets
2. Implement data validation rules
3. Create database initialization scripts
4. Setup monitoring và alerting
5. Implement backup automation
6. Performance testing với sample data
