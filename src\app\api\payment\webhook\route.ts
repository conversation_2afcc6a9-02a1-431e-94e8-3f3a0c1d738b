import { NextRequest, NextResponse } from 'next/server'
import { verifyWebhookSignature, handleStripeError } from '@/lib/stripe'
import connectDB from '@/lib/mongodb'
import Payment, { PaymentStatus } from '@/models/Payment'
import Enrollment, { EnrollmentStatus, EnrollmentType } from '@/models/Enrollment'
import Course from '@/models/Course'
import Stripe from 'stripe'

// POST /api/payment/webhook - Handle Stripe webhooks
export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')
    
    if (!signature) {
      return NextResponse.json({
        success: false,
        error: 'Missing Stripe signature'
      }, { status: 400 })
    }
    
    // Verify webhook signature
    let event: Stripe.Event
    try {
      event = verifyWebhookSignature(body, signature)
    } catch (error) {
      console.error('Webhook signature verification failed:', error)
      return NextResponse.json({
        success: false,
        error: 'Invalid signature'
      }, { status: 400 })
    }
    
    console.log('Received webhook event:', event.type)
    
    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent)
        break
        
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
        break
        
      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent)
        break
        
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object as Stripe.Dispute)
        break
        
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }
    
    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully'
    })
    
  } catch (error) {
    console.error('Webhook processing error:', error)
    
    if (error.type && error.type.startsWith('Stripe')) {
      const stripeError = handleStripeError(error)
      return NextResponse.json({
        success: false,
        error: stripeError.message
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Webhook processing failed'
    }, { status: 500 })
  }
}

// Handle successful payment
async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing successful payment:', paymentIntent.id)
    
    // Find payment record
    const payment = await Payment.findOne({
      'details.stripePaymentIntentId': paymentIntent.id
    })
    
    if (!payment) {
      console.error('Payment record not found for payment intent:', paymentIntent.id)
      return
    }
    
    // Update payment status
    payment.status = PaymentStatus.COMPLETED
    payment.paidAt = new Date()
    payment.details.stripeChargeId = paymentIntent.latest_charge as string
    await payment.save()
    
    // Create or update enrollment
    let enrollment = await Enrollment.findOne({
      userId: payment.userId,
      courseId: payment.courseId
    })
    
    if (!enrollment) {
      enrollment = new Enrollment({
        userId: payment.userId,
        courseId: payment.courseId,
        status: EnrollmentStatus.ACTIVE,
        type: EnrollmentType.PAID,
        payment: {
          amount: payment.amount,
          currency: payment.currency,
          method: 'stripe',
          transactionId: paymentIntent.id,
          paidAt: new Date()
        }
      })
    } else {
      enrollment.status = EnrollmentStatus.ACTIVE
      enrollment.type = EnrollmentType.PAID
      enrollment.payment = {
        amount: payment.amount,
        currency: payment.currency,
        method: 'stripe',
        transactionId: paymentIntent.id,
        paidAt: new Date()
      }
    }
    
    // Initialize progress
    await enrollment.updateProgress()
    await enrollment.save()
    
    // Update course stats
    await Course.findByIdAndUpdate(payment.courseId, {
      $inc: { 'stats.totalStudents': 1 },
      'stats.lastUpdated': new Date()
    })
    
    console.log('Payment processed successfully:', {
      paymentId: payment._id,
      enrollmentId: enrollment._id,
      userId: payment.userId,
      courseId: payment.courseId
    })
    
  } catch (error) {
    console.error('Error processing successful payment:', error)
    throw error
  }
}

// Handle failed payment
async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing failed payment:', paymentIntent.id)
    
    // Find payment record
    const payment = await Payment.findOne({
      'details.stripePaymentIntentId': paymentIntent.id
    })
    
    if (!payment) {
      console.error('Payment record not found for payment intent:', paymentIntent.id)
      return
    }
    
    // Update payment status
    payment.status = PaymentStatus.FAILED
    payment.failedAt = new Date()
    
    // Add failure reason if available
    if (paymentIntent.last_payment_error) {
      payment.metadata.failureReason = paymentIntent.last_payment_error.message || 'Unknown error'
      payment.metadata.failureCode = paymentIntent.last_payment_error.code || 'unknown'
    }
    
    await payment.save()
    
    console.log('Failed payment processed:', {
      paymentId: payment._id,
      reason: payment.metadata.failureReason
    })
    
  } catch (error) {
    console.error('Error processing failed payment:', error)
    throw error
  }
}

// Handle canceled payment
async function handlePaymentCanceled(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing canceled payment:', paymentIntent.id)
    
    // Find payment record
    const payment = await Payment.findOne({
      'details.stripePaymentIntentId': paymentIntent.id
    })
    
    if (!payment) {
      console.error('Payment record not found for payment intent:', paymentIntent.id)
      return
    }
    
    // Update payment status
    payment.status = PaymentStatus.CANCELLED
    payment.cancelledAt = new Date()
    await payment.save()
    
    console.log('Canceled payment processed:', {
      paymentId: payment._id
    })
    
  } catch (error) {
    console.error('Error processing canceled payment:', error)
    throw error
  }
}

// Handle charge dispute
async function handleChargeDispute(dispute: Stripe.Dispute) {
  try {
    console.log('Processing charge dispute:', dispute.id)
    
    // Find payment record by charge ID
    const payment = await Payment.findOne({
      'details.stripeChargeId': dispute.charge
    })
    
    if (!payment) {
      console.error('Payment record not found for charge:', dispute.charge)
      return
    }
    
    // Add dispute information to payment metadata
    payment.metadata.disputeId = dispute.id
    payment.metadata.disputeReason = dispute.reason
    payment.metadata.disputeStatus = dispute.status
    payment.metadata.disputeAmount = dispute.amount.toString()
    
    await payment.save()
    
    // TODO: Implement dispute handling logic
    // - Notify administrators
    // - Suspend enrollment if needed
    // - Create dispute tracking record
    
    console.log('Dispute processed:', {
      paymentId: payment._id,
      disputeId: dispute.id,
      reason: dispute.reason
    })
    
  } catch (error) {
    console.error('Error processing dispute:', error)
    throw error
  }
}
