(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[663],{9563:function(e,r,t){Promise.resolve().then(t.bind(t,7123))},9376:function(e,r,t){"use strict";var s=t(5475);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},7123:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return u}});var s=t(7437),n=t(605),a=t(9376),i=t(2265),d=t(7648),o=t(1215),c=t(6334);function u(e){var r,t,u;let{children:l}=e,{data:m,status:h}=(0,n.useSession)(),f=(0,a.useRouter)(),x=(0,a.usePathname)();if((0,i.useEffect)(()=>{"unauthenticated"===h&&f.push("/auth/signin")},[h,f]),"loading"===h)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)(o.gb,{size:"lg"})});if(!m)return null;let g=(null===(r=m.user)||void 0===r?void 0:r.role)||"student";return(0,i.useEffect)(()=>{"/dashboard"===x&&("instructor"===g||"admin"===g?f.push("/dashboard/instructor"):f.push("/dashboard/student"))},[x,g,f]),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,s.jsxs)(d.default,{href:"/",className:"flex items-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary",children:"WebTA"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"LMS"})]}),(0,s.jsxs)("nav",{className:"hidden md:flex space-x-1",children:[(0,s.jsx)(d.default,{href:"/dashboard/student",className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat("/dashboard/student"===x?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:"Dashboard Học vi\xean"}),("instructor"===g||"admin"===g)&&(0,s.jsx)(d.default,{href:"/dashboard/instructor",className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat("/dashboard/instructor"===x?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:"Dashboard Giảng vi\xean"}),(0,s.jsx)(d.default,{href:"/courses",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",children:"Kh\xf3a học"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"hidden sm:block text-sm text-gray-600",children:["Xin ch\xe0o, ",(null===(t=m.user)||void 0===t?void 0:t.name)||(null===(u=m.user)||void 0===u?void 0:u.email)]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.default,{href:"/profile",className:"text-sm text-gray-600 hover:text-gray-900 px-2 py-1 rounded transition-colors",children:"Hồ sơ"}),(0,s.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>f.push("/api/auth/signout"),className:"text-sm",children:"Đăng xuất"})]})]})]})})}),(0,s.jsx)("main",{className:"flex-1",children:l})]})}},6334:function(e,r,t){"use strict";t.d(r,{z:function(){return o}});var s=t(7437),n=t(2265),a=t(535),i=t(3448);let d=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,r)=>{let{className:t,variant:n,size:a,asChild:o=!1,...c}=e;return(0,s.jsx)("button",{className:(0,i.cn)(d({variant:n,size:a,className:t})),ref:r,...c})});o.displayName="Button"},1215:function(e,r,t){"use strict";t.d(r,{gb:function(){return d}});var s=t(7437),n=t(535),a=t(3448);let i=(0,n.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function d(e){let{variant:r,size:t,className:n,text:d}=e;return(0,s.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",n),children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)("div",{className:(0,a.cn)(i({variant:r,size:t}))}),d&&(0,s.jsx)("p",{className:"text-sm text-gray-600",children:d})]})})}},3448:function(e,r,t){"use strict";t.d(r,{cn:function(){return a}});var s=t(1994),n=t(3335);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,s.W)(r))}}},function(e){e.O(0,[851,648,605,971,117,744],function(){return e(e.s=9563)}),_N_E=e.O()}]);