(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[263],{717:function(e,t,n){Promise.resolve().then(n.bind(n,511))},166:function(e,t,n){"use strict";n.d(t,{default:function(){return a.a}});var r=n(5775),a=n.n(r)},5775:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let r=n(7043);n(7437),n(2265);let a=r._(n(5602));function s(e,t){var n;let r={loading:e=>{let{error:t,isLoading:n,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let s={...r,...t};return(0,a.default)({...s,modules:null==(n=s.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1523:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let r=n(8993);function a(e){let{reason:t,children:n}=e;if("undefined"==typeof window)throw new r.BailoutToCSRError(t);return n}},5602:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let r=n(7437),a=n(2265),s=n(1523),l=n(49);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},n=(0,a.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,r.jsxs)(r.Fragment,{children:["undefined"==typeof window?(0,r.jsx)(l.PreloadCss,{moduleIds:t.modules}):null,(0,r.jsx)(n,{...e})]}):(0,r.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(a.Suspense,{fallback:i,children:o})}return c.displayName="LoadableComponent",c}},49:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return s}});let r=n(7437),a=n(544);function s(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let n=(0,a.getExpectedRequestStore)("next/dynamic css"),s=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files.filter(e=>e.endsWith(".css"));s.push(...t)}}return 0===s.length?null:(0,r.jsx)(r.Fragment,{children:s.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:n.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},511:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return g}});var r=n(7437),a=n(2265),s=n(166),l=n(757),i=n(6334),o=n(1215);let d=(0,s.default)(()=>n.e(279).then(n.bind(n,9279)),{loadableGenerated:{webpack:()=>[9279]},loading:()=>(0,r.jsx)(o.gb,{text:"Đang tải Rich Text Editor..."}),ssr:!1}),c=(0,s.default)(()=>n.e(67).then(n.bind(n,5067)),{loadableGenerated:{webpack:()=>[5067]},loading:()=>(0,r.jsx)(o.gb,{text:"Đang tải Video Player..."}),ssr:!1}),u=(0,s.default)(()=>n.e(735).then(n.bind(n,9735)),{loadableGenerated:{webpack:()=>[9735]},loading:()=>(0,r.jsx)(o.gb,{text:"Đang tải Data Table..."}),ssr:!1}),m=[{id:1,name:"Nguyễn Văn A",email:"<EMAIL>",role:"student",status:"active",createdAt:"2024-01-15",score:85},{id:2,name:"Trần Thị B",email:"<EMAIL>",role:"instructor",status:"active",createdAt:"2024-01-10",score:92},{id:3,name:"L\xea Văn C",email:"<EMAIL>",role:"student",status:"inactive",createdAt:"2024-01-20",score:78},{id:4,name:"Phạm Thị D",email:"<EMAIL>",role:"admin",status:"active",createdAt:"2024-01-05",score:95},{id:5,name:"Ho\xe0ng Văn E",email:"<EMAIL>",role:"student",status:"active",createdAt:"2024-01-25",score:88}];function g(){let[e,t]=(0,a.useState)("<p>Đ\xe2y l\xe0 nội dung mẫu cho <strong>Rich Text Editor</strong>. Bạn c\xf3 thể <em>định dạng</em> văn bản, th\xeam <u>gạch ch\xe2n</u>, tạo danh s\xe1ch v\xe0 nhiều hơn nữa!</p>");return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container-7xl space-y-12",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"heading-1 mb-4",children:"Advanced UI Components Demo"}),(0,r.jsx)("p",{className:"body-large text-gray-600",children:"Showcase c\xe1c components n\xe2ng cao của WebTA LMS"})]}),(0,r.jsxs)(l.Zb,{className:"card-padding-lg",children:[(0,r.jsx)("h2",{className:"heading-2 mb-6",children:"Rich Text Editor"}),(0,r.jsx)("p",{className:"body-medium text-gray-600 mb-4",children:"Editor WYSIWYG với đầy đủ t\xednh năng định dạng văn bản, hỗ trợ HTML v\xe0 c\xe1c shortcut phổ biến."}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(o.gb,{text:"Đang tải Rich Text Editor..."}),children:(0,r.jsx)(d,{value:e,onChange:t,placeholder:"Nhập nội dung của bạn tại đ\xe2y...",minHeight:300,className:"mb-4"})}),(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"HTML Output:"}),(0,r.jsx)("pre",{className:"text-xs text-gray-600 whitespace-pre-wrap",children:e})]})]}),(0,r.jsxs)(l.Zb,{className:"card-padding-lg",children:[(0,r.jsx)("h2",{className:"heading-2 mb-6",children:"Video Player"}),(0,r.jsx)("p",{className:"body-medium text-gray-600 mb-4",children:"Video player t\xf9y chỉnh với controls đầy đủ, hỗ trợ fullscreen, speed control v\xe0 progress tracking."}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(o.gb,{text:"Đang tải Video Player..."}),children:(0,r.jsx)(c,{src:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",poster:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg",title:"Big Buck Bunny - Sample Video",className:"aspect-video max-w-4xl mx-auto",onTimeUpdate:(e,t)=>{console.log("Video progress: ".concat(e,"/").concat(t))},onPlay:()=>console.log("Video started playing"),onPause:()=>console.log("Video paused"),onEnded:()=>console.log("Video ended")})}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Features:"})}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"Custom controls với play/pause, volume, progress bar"}),(0,r.jsx)("li",{children:"Fullscreen support"}),(0,r.jsx)("li",{children:"Playback speed control (0.5x - 2x)"}),(0,r.jsx)("li",{children:"Keyboard shortcuts (Space: play/pause, F: fullscreen)"}),(0,r.jsx)("li",{children:"Progress tracking v\xe0 event callbacks"}),(0,r.jsx)("li",{children:"Responsive design"})]})]})]}),(0,r.jsxs)(l.Zb,{className:"card-padding-lg",children:[(0,r.jsx)("h2",{className:"heading-2 mb-6",children:"Data Table"}),(0,r.jsx)("p",{className:"body-medium text-gray-600 mb-4",children:"Bảng dữ liệu với t\xednh năng sorting, filtering, pagination v\xe0 custom rendering."}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(o.gb,{text:"Đang tải Data Table..."}),children:(0,r.jsx)(u,{data:m,columns:[{key:"id",title:"ID",sortable:!0,width:80,align:"center"},{key:"name",title:"Họ v\xe0 t\xean",sortable:!0,filterable:!0,render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm mr-3",children:e.charAt(0)}),(0,r.jsx)("span",{className:"font-medium",children:e})]})},{key:"email",title:"Email",sortable:!0,filterable:!0,render:e=>(0,r.jsx)("a",{href:"mailto:".concat(e),className:"text-blue-600 hover:underline",children:e})},{key:"role",title:"Vai tr\xf2",sortable:!0,filterable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat({student:"bg-blue-100 text-blue-800",instructor:"bg-green-100 text-green-800",admin:"bg-purple-100 text-purple-800"}[e]),children:{student:"Học vi\xean",instructor:"Giảng vi\xean",admin:"Quản trị"}[e]})},{key:"status",title:"Trạng th\xe1i",sortable:!0,filterable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("active"===e?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"active"===e?"Hoạt động":"Kh\xf4ng hoạt động"})},{key:"score",title:"Điểm số",sortable:!0,align:"center",render:e=>(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold ".concat(e>=90?"bg-green-500":e>=80?"bg-blue-500":e>=70?"bg-yellow-500":"bg-red-500"),children:e})})},{key:"createdAt",title:"Ng\xe0y tạo",sortable:!0,render:e=>new Date(e).toLocaleDateString("vi-VN")}],pageSize:3,showPagination:!0,showSearch:!0,searchPlaceholder:"T\xecm kiếm người d\xf9ng...",onRowClick:e=>{alert("Clicked on: ".concat(e.name," (").concat(e.email,")"))},rowClassName:e=>"inactive"===e.status?"opacity-60":"",className:"mt-4"})}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Features:"})}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"Sorting theo column (click v\xe0o header)"}),(0,r.jsx)("li",{children:"Global search v\xe0 column-specific filtering"}),(0,r.jsx)("li",{children:"Pagination với navigation"}),(0,r.jsx)("li",{children:"Custom cell rendering"}),(0,r.jsx)("li",{children:"Row click events"}),(0,r.jsx)("li",{children:"Responsive design"}),(0,r.jsx)("li",{children:"Loading states v\xe0 empty states"})]})]})]}),(0,r.jsxs)(l.Zb,{className:"card-padding-lg",children:[(0,r.jsx)("h2",{className:"heading-2 mb-6",children:"Usage Examples"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"heading-3 mb-3",children:"Rich Text Editor"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto",children:"import RichTextEditor from '@/components/ui/RichTextEditor'\n\nfunction MyComponent() {\n  const [content, setContent] = useState('')\n  \n  return (\n    <RichTextEditor\n      value={content}\n      onChange={setContent}\n      placeholder=\"Nhập nội dung...\"\n      minHeight={200}\n    />\n  )\n}"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"heading-3 mb-3",children:"Video Player"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto",children:'import VideoPlayer from \'@/components/ui/VideoPlayer\'\n\nfunction MyComponent() {\n  return (\n    <VideoPlayer\n      src="/path/to/video.mp4"\n      poster="/path/to/poster.jpg"\n      title="Video Title"\n      onTimeUpdate={(current, duration) => {\n        // Track progress\n      }}\n    />\n  )\n}'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"heading-3 mb-3",children:"Data Table"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto",children:"import DataTable from '@/components/ui/DataTable'\n\nconst columns = [\n  { key: 'name', title: 'Name', sortable: true },\n  { key: 'email', title: 'Email', filterable: true },\n  { \n    key: 'status', \n    title: 'Status',\n    render: (value) => <Badge>{value}</Badge>\n  }\n]\n\nfunction MyComponent() {\n  return (\n    <DataTable\n      data={data}\n      columns={columns}\n      showPagination={true}\n      showSearch={true}\n    />\n  )\n}"})]})]})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(i.z,{onClick:()=>window.history.back(),children:"← Quay lại"})})]})})}},6334:function(e,t,n){"use strict";n.d(t,{z:function(){return o}});var r=n(7437),a=n(2265),s=n(535),l=n(3448);let i=(0,s.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:n,variant:a,size:s,asChild:o=!1,...d}=e;return(0,r.jsx)("button",{className:(0,l.cn)(i({variant:a,size:s,className:n})),ref:t,...d})});o.displayName="Button"},757:function(e,t,n){"use strict";n.d(t,{Zb:function(){return o}});var r=n(7437),a=n(2265),s=n(535),l=n(3448);let i=(0,s.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),o=a.forwardRef((e,t)=>{let{className:n,variant:a,size:s,...o}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)(i({variant:a,size:s}),n),...o})});o.displayName="Card",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",n),...a})}).displayName="CardHeader",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",n),...a})}).displayName="CardTitle",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",n),...a})}).displayName="CardDescription",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",n),...a})}).displayName="CardContent",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",n),...a})}).displayName="CardFooter"},1215:function(e,t,n){"use strict";n.d(t,{gb:function(){return i}});var r=n(7437),a=n(535),s=n(3448);let l=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function i(e){let{variant:t,size:n,className:a,text:i}=e;return(0,r.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",a),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:(0,s.cn)(l({variant:t,size:n}))}),i&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i})]})})}},3448:function(e,t,n){"use strict";n.d(t,{cn:function(){return s}});var r=n(1994),a=n(3335);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,r.W)(t))}}},function(e){e.O(0,[851,971,117,744],function(){return e(e.s=717)}),_N_E=e.O()}]);