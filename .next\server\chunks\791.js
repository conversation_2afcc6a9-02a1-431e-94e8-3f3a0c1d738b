"use strict";exports.id=791,exports.ids=[791],exports.modules={14184:(e,t,n)=>{n.d(t,{ZP:()=>u});var i=n(11185),r=n.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let u=async function(){if(s.conn)return s.conn;s.promise||(s.promise=r().connect(a,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},46029:(e,t,n)=>{n.d(t,{BQ:()=>u,Gl:()=>a,ks:()=>o,po:()=>s});let i=new(n(48472)).Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2023-10-16",typescript:!0}),r={publishableKey:process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,currency:"vnd",country:"VN",webhookSecret:process.env.STRIPE_WEBHOOK_SECRET};async function a(e){return await i.paymentIntents.create({amount:e.amount,currency:e.currency||r.currency,automatic_payment_methods:{enabled:!0},metadata:{courseId:e.courseId,userId:e.userId,type:"course_purchase",...e.metadata}})}function s(e,t){return i.webhooks.constructEvent(e,t,r.webhookSecret)}function u(e,t="vnd"){return"vnd"===t.toLowerCase()?Math.round(e):Math.round(100*e)}function o(e){return"StripeCardError"===e.type?{message:"Thẻ của bạn bị từ chối. Vui l\xf2ng kiểm tra th\xf4ng tin thẻ.",type:"card_error",code:e.code}:"StripeRateLimitError"===e.type?{message:"Qu\xe1 nhiều y\xeau cầu. Vui l\xf2ng thử lại sau.",type:"rate_limit_error"}:"StripeInvalidRequestError"===e.type?{message:"Y\xeau cầu kh\xf4ng hợp lệ. Vui l\xf2ng thử lại.",type:"invalid_request_error",code:e.code}:"StripeAPIError"===e.type?{message:"Lỗi hệ thống thanh to\xe1n. Vui l\xf2ng thử lại sau.",type:"api_error"}:"StripeConnectionError"===e.type?{message:"Lỗi kết nối. Vui l\xf2ng kiểm tra mạng v\xe0 thử lại.",type:"connection_error"}:"StripeAuthenticationError"===e.type?{message:"Lỗi x\xe1c thực thanh to\xe1n.",type:"authentication_error"}:{message:"Đ\xe3 xảy ra lỗi kh\xf4ng x\xe1c định. Vui l\xf2ng thử lại.",type:"unknown_error"}}},89332:(e,t,n)=>{n.d(t,{D4:()=>i,ZP:()=>c,f:()=>r,ij:()=>a});var i,r,a,s=n(11185),u=n.n(s);(function(e){e.DRAFT="draft",e.PUBLISHED="published",e.ARCHIVED="archived",e.SUSPENDED="suspended"})(i||(i={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(r||(r={})),function(e){e.ENGLISH="english",e.VIETNAMESE="vietnamese",e.CHINESE="chinese",e.JAPANESE="japanese",e.KOREAN="korean",e.FRENCH="french",e.GERMAN="german",e.SPANISH="spanish"}(a||(a={}));let o=new s.Schema({title:{type:String,required:[!0,"Ti\xeau đề kh\xf3a học l\xe0 bắt buộc"],trim:!0,maxlength:[200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"]},slug:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},shortDescription:{type:String,required:[!0,"M\xf4 tả ngắn l\xe0 bắt buộc"],maxlength:[500,"M\xf4 tả ngắn kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},content:{description:{type:String,required:[!0,"M\xf4 tả chi tiết l\xe0 bắt buộc"]},objectives:[{type:String,required:!0}],prerequisites:[String],syllabus:[{week:{type:Number,required:!0},title:{type:String,required:!0},topics:[String],duration:{type:Number,required:!0}}]},instructor:{type:s.Schema.Types.ObjectId,ref:"User",required:[!0,"Giảng vi\xean l\xe0 bắt buộc"]},category:{type:s.Schema.Types.ObjectId,ref:"Category",required:[!0,"Danh mục l\xe0 bắt buộc"]},subcategory:{type:s.Schema.Types.ObjectId,ref:"Category"},language:{type:String,enum:Object.values(a),required:[!0,"Ng\xf4n ngữ kh\xf3a học l\xe0 bắt buộc"]},level:{type:String,enum:Object.values(r),required:[!0,"Cấp độ kh\xf3a học l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(i),default:"draft"},thumbnail:String,previewVideo:String,tags:[String],pricing:{basePrice:{type:Number,required:[!0,"Gi\xe1 kh\xf3a học l\xe0 bắt buộc"],min:[0,"Gi\xe1 kh\xf4ng được \xe2m"]},currency:{type:String,default:"VND"},discountPrice:{type:Number,min:[0,"Gi\xe1 giảm kh\xf4ng được \xe2m"]},discountValidUntil:Date,installmentOptions:{enabled:{type:Boolean,default:!1},plans:[{months:{type:Number,required:!0},monthlyAmount:{type:Number,required:!0}}]}},settings:{maxStudents:{type:Number,min:[1,"Số học vi\xean tối đa phải \xedt nhất l\xe0 1"]},allowComments:{type:Boolean,default:!0},allowRatings:{type:Boolean,default:!0},certificateEnabled:{type:Boolean,default:!0},downloadableResources:{type:Boolean,default:!0},mobileAccess:{type:Boolean,default:!0},lifetimeAccess:{type:Boolean,default:!0},accessDuration:{type:Number,min:[1,"Thời gian truy cập phải \xedt nhất 1 ng\xe0y"]}},stats:{totalStudents:{type:Number,default:0},totalLessons:{type:Number,default:0},totalDuration:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRatings:{type:Number,default:0},completionRate:{type:Number,default:0,min:0,max:100}},seo:{metaTitle:{type:String,maxlength:[60,"Meta title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},metaDescription:{type:String,maxlength:[160,"Meta description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},publishedAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({slug:1}),o.index({instructor:1}),o.index({category:1,subcategory:1}),o.index({language:1,level:1}),o.index({status:1,publishedAt:-1}),o.index({"stats.averageRating":-1}),o.index({"stats.totalStudents":-1}),o.index({tags:1}),o.index({"pricing.basePrice":1}),o.virtual("isOnSale").get(function(){return!!(this.pricing.discountPrice&&this.pricing.discountValidUntil&&this.pricing.discountValidUntil>new Date)}),o.virtual("currentPrice").get(function(){return this.isOnSale?this.pricing.discountPrice:this.pricing.basePrice}),o.pre("save",function(e){this.isModified("title")&&!this.slug&&(this.slug=this.generateSlug()),e()}),o.methods.generateSlug=function(){return this.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},o.methods.updateStats=async function(){console.log("Updating course stats...")},o.methods.isPublished=function(){return"published"===this.status&&!!this.publishedAt},o.methods.canEnroll=function(){return!!this.isPublished()&&(!this.settings.maxStudents||!(this.stats.totalStudents>=this.settings.maxStudents))};let c=u().models.Course||u().model("Course",o)},60321:(e,t,n)=>{n.d(t,{XL:()=>r,ZP:()=>l,bG:()=>i,uG:()=>a});var i,r,a,s,u=n(11185),o=n.n(u);(function(e){e.PENDING="pending",e.PROCESSING="processing",e.COMPLETED="completed",e.FAILED="failed",e.CANCELLED="cancelled",e.REFUNDED="refunded",e.PARTIALLY_REFUNDED="partially_refunded"})(i||(i={})),function(e){e.STRIPE="stripe",e.PAYPAL="paypal",e.BANK_TRANSFER="bank_transfer",e.ACTIVATION_CODE="activation_code",e.ADMIN_GRANT="admin_grant"}(r||(r={})),function(e){e.COURSE_PURCHASE="course_purchase",e.SUBSCRIPTION="subscription",e.ACTIVATION_CODE="activation_code",e.REFUND="refund"}(a||(a={})),function(e){e.VND="VND",e.USD="USD",e.EUR="EUR"}(s||(s={}));let c=new u.Schema({amount:{type:Number,required:!0,min:0},reason:{type:String,required:!0},refundedAt:{type:Date,default:Date.now},refundedBy:{type:u.Schema.Types.ObjectId,ref:"User",required:!0},externalRefundId:String,notes:String},{_id:!1}),d=new u.Schema({userId:{type:u.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID l\xe0 bắt buộc"]},courseId:{type:u.Schema.Types.ObjectId,ref:"Course"},enrollmentId:{type:u.Schema.Types.ObjectId,ref:"Enrollment"},amount:{type:Number,required:[!0,"Số tiền l\xe0 bắt buộc"],min:[0,"Số tiền kh\xf4ng được \xe2m"]},originalAmount:{type:Number,required:!0,min:[0,"Số tiền gốc kh\xf4ng được \xe2m"]},currency:{type:String,enum:Object.values(s),default:"VND"},status:{type:String,enum:Object.values(i),default:"pending"},method:{type:String,enum:Object.values(r),required:[!0,"Phương thức thanh to\xe1n l\xe0 bắt buộc"]},type:{type:String,enum:Object.values(a),required:[!0,"Loại thanh to\xe1n l\xe0 bắt buộc"]},externalId:String,details:{stripePaymentIntentId:String,stripeChargeId:String,stripeCustomerId:String,paypalOrderId:String,paypalPaymentId:String,bankTransferReference:String,bankAccount:String,activationCode:String,codeGeneratedBy:{type:u.Schema.Types.ObjectId,ref:"User"},receiptUrl:String,invoiceUrl:String,description:String},refunds:[c],totalRefunded:{type:Number,default:0,min:0},metadata:{userAgent:String,ipAddress:String,country:String,currency:String,exchangeRate:{type:Number,min:0},discountCode:String,discountAmount:{type:Number,min:0},taxAmount:{type:Number,min:0},processingFee:{type:Number,min:0}},paidAt:Date,failedAt:Date,cancelledAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});d.index({userId:1,status:1}),d.index({courseId:1,status:1}),d.index({status:1,createdAt:-1}),d.index({method:1,paidAt:-1}),d.index({externalId:1}),d.index({"details.stripePaymentIntentId":1}),d.index({"details.paypalOrderId":1}),d.index({"details.activationCode":1}),d.index({paidAt:-1}),d.virtual("isSuccessful").get(function(){return"completed"===this.status}),d.virtual("canRefund").get(function(){return"completed"===this.status&&this.totalRefunded<this.amount}),d.virtual("netAmount").get(function(){return this.amount-this.totalRefunded}),d.pre("save",function(e){this.totalRefunded=this.refunds.reduce((e,t)=>e+t.amount,0),this.totalRefunded>0&&(this.totalRefunded>=this.amount?this.status="refunded":this.status="partially_refunded"),e()}),d.methods.processPayment=async function(){if("pending"!==this.status)throw Error("Payment kh\xf4ng ở trạng th\xe1i pending");this.status="processing",await this.save();try{switch(this.method){case"stripe":await this.processStripePayment();break;case"paypal":await this.processPayPalPayment();break;case"activation_code":await this.processActivationCode();break;default:throw Error(`Phương thức thanh to\xe1n ${this.method} chưa được hỗ trợ`)}this.status="completed",this.paidAt=new Date}catch(e){throw this.status="failed",this.failedAt=new Date,e}finally{await this.save()}},d.methods.processStripePayment=async function(){console.log("Processing Stripe payment...")},d.methods.processPayPalPayment=async function(){console.log("Processing PayPal payment...")},d.methods.processActivationCode=async function(){console.log("Processing activation code...")},d.methods.refund=async function(e,t,n){if(!this.canRefund)throw Error("Payment kh\xf4ng thể refund");if(e>this.amount-this.totalRefunded)throw Error("Số tiền refund vượt qu\xe1 số tiền c\xf3 thể refund");let i={amount:e,reason:t,refundedAt:new Date,refundedBy:new(o()).Types.ObjectId(n),notes:`Refund ${e} ${this.currency} - ${t}`};this.refunds.push(i),await this.save()},d.methods.cancel=async function(){if("pending"!==this.status&&"processing"!==this.status)throw Error("Chỉ c\xf3 thể cancel payment ở trạng th\xe1i pending hoặc processing");this.status="cancelled",this.cancelledAt=new Date,await this.save()},d.methods.updateStatus=async function(e){switch(this.status=e,e){case"completed":this.paidAt=new Date;break;case"failed":this.failedAt=new Date;break;case"cancelled":this.cancelledAt=new Date}await this.save()},d.methods.generateInvoice=async function(){let e=`INV-${this._id}-${Date.now()}`;return this.details.invoiceUrl=`/api/invoices/${e}`,await this.save(),this.details.invoiceUrl},d.statics.getRevenueStats=async function(e,t){return(await this.aggregate([{$match:{status:"completed",paidAt:{$gte:e,$lte:t}}},{$group:{_id:null,totalRevenue:{$sum:"$amount"},totalRefunded:{$sum:"$totalRefunded"},totalPayments:{$sum:1},averageAmount:{$avg:"$amount"}}}]))[0]||{totalRevenue:0,totalRefunded:0,totalPayments:0,averageAmount:0}},d.statics.getMethodStats=async function(){return await this.aggregate([{$match:{status:"completed"}},{$group:{_id:"$method",count:{$sum:1},totalAmount:{$sum:"$amount"}}},{$sort:{totalAmount:-1}}])};let l=o().models.Payment||o().model("Payment",d)}};