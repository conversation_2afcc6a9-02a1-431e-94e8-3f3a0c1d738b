import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Admin routes - chỉ admin và super_admin mới được truy cập
    if (pathname.startsWith('/admin')) {
      if (!token || (token.role !== 'admin' && token.role !== 'super_admin')) {
        return NextResponse.redirect(new URL('/auth/signin', req.url))
      }
    }

    // Instructor routes - chỉ instructor, admin và super_admin mới được truy cập
    if (pathname.startsWith('/instructor')) {
      if (!token || !['instructor', 'admin', 'super_admin'].includes(token.role)) {
        return NextResponse.redirect(new URL('/auth/signin', req.url))
      }
    }

    // Student dashboard - chỉ student đư<PERSON><PERSON> truy cập
    if (pathname.startsWith('/dashboard') && !pathname.startsWith('/dashboard/admin') && !pathname.startsWith('/dashboard/instructor')) {
      if (!token || token.role !== 'student') {
        return NextResponse.redirect(new URL('/auth/signin', req.url))
      }
    }

    // API routes protection
    if (pathname.startsWith('/api/admin')) {
      if (!token || (token.role !== 'admin' && token.role !== 'super_admin')) {
        return NextResponse.json(
          { error: 'Không có quyền truy cập' },
          { status: 403 }
        )
      }
    }

    if (pathname.startsWith('/api/instructor')) {
      if (!token || !['instructor', 'admin', 'super_admin'].includes(token.role)) {
        return NextResponse.json(
          { error: 'Không có quyền truy cập' },
          { status: 403 }
        )
      }
    }

    // Check email verification for sensitive operations
    if (pathname.startsWith('/api/courses') && req.method !== 'GET') {
      if (!token?.emailVerified) {
        return NextResponse.json(
          { error: 'Vui lòng xác thực email trước khi thực hiện thao tác này' },
          { status: 403 }
        )
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Public routes - không cần authentication
        const publicRoutes = [
          '/',
          '/courses',
          '/about',
          '/contact',
          '/auth/signin',
          '/auth/signup',
          '/auth/error',
          '/auth/verify-request'
        ]

        // API public routes
        const publicApiRoutes = [
          '/api/auth',
          '/api/courses',
          '/api/categories'
        ]

        // Check if route is public
        if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + '/'))) {
          return true
        }

        // Check if API route is public (GET requests only)
        if (req.method === 'GET' && publicApiRoutes.some(route => pathname.startsWith(route))) {
          return true
        }

        // For protected routes, require authentication
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
