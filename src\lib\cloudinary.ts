import { v2 as cloudinary } from 'cloudinary'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

// File type configurations
export const ALLOWED_FILE_TYPES = {
  image: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  video: ['mp4', 'mov', 'avi', 'mkv', 'webm'],
  audio: ['mp3', 'wav', 'aac', 'm4a'],
  document: ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt']
}

export const MAX_FILE_SIZES = {
  image: 10 * 1024 * 1024, // 10MB
  video: 500 * 1024 * 1024, // 500MB
  audio: 50 * 1024 * 1024, // 50MB
  document: 20 * 1024 * 1024 // 20MB
}

// Upload options for different file types
export const UPLOAD_OPTIONS = {
  image: {
    resource_type: 'image' as const,
    folder: 'webta/images',
    transformation: [
      { quality: 'auto', fetch_format: 'auto' },
      { width: 1920, height: 1080, crop: 'limit' }
    ]
  },
  video: {
    resource_type: 'video' as const,
    folder: 'webta/videos',
    transformation: [
      { quality: 'auto' },
      { width: 1920, height: 1080, crop: 'limit' }
    ]
  },
  audio: {
    resource_type: 'video' as const, // Cloudinary treats audio as video
    folder: 'webta/audio'
  },
  document: {
    resource_type: 'raw' as const,
    folder: 'webta/documents'
  }
}

// Helper function to determine file type
export function getFileType(filename: string): keyof typeof ALLOWED_FILE_TYPES | null {
  const extension = filename.split('.').pop()?.toLowerCase()
  if (!extension) return null

  for (const [type, extensions] of Object.entries(ALLOWED_FILE_TYPES)) {
    if (extensions.includes(extension)) {
      return type as keyof typeof ALLOWED_FILE_TYPES
    }
  }
  return null
}

// Helper function to validate file
export function validateFile(file: File): { valid: boolean; error?: string; type?: string } {
  const fileType = getFileType(file.name)
  
  if (!fileType) {
    return {
      valid: false,
      error: 'Loại file không được hỗ trợ'
    }
  }

  const maxSize = MAX_FILE_SIZES[fileType]
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024))
    return {
      valid: false,
      error: `File quá lớn. Kích thước tối đa cho ${fileType} là ${maxSizeMB}MB`
    }
  }

  return {
    valid: true,
    type: fileType
  }
}

// Upload file to Cloudinary
export async function uploadFile(
  file: Buffer | string,
  filename: string,
  options?: {
    folder?: string
    public_id?: string
    tags?: string[]
    context?: Record<string, string>
  }
): Promise<{
  success: boolean
  data?: {
    public_id: string
    secure_url: string
    url: string
    format: string
    resource_type: string
    bytes: number
    width?: number
    height?: number
    duration?: number
  }
  error?: string
}> {
  try {
    const fileType = getFileType(filename)
    if (!fileType) {
      return {
        success: false,
        error: 'Loại file không được hỗ trợ'
      }
    }

    const uploadOptions = {
      ...UPLOAD_OPTIONS[fileType],
      ...options,
      public_id: options?.public_id || `${Date.now()}-${filename.split('.')[0]}`,
      tags: ['webta', ...(options?.tags || [])],
      context: {
        filename,
        uploaded_at: new Date().toISOString(),
        ...options?.context
      }
    }

    const result = await cloudinary.uploader.upload(
      typeof file === 'string' ? file : `data:application/octet-stream;base64,${file.toString('base64')}`,
      uploadOptions
    )

    return {
      success: true,
      data: {
        public_id: result.public_id,
        secure_url: result.secure_url,
        url: result.url,
        format: result.format,
        resource_type: result.resource_type,
        bytes: result.bytes,
        width: result.width,
        height: result.height,
        duration: result.duration
      }
    }
  } catch (error) {
    console.error('Cloudinary upload error:', error)
    return {
      success: false,
      error: 'Lỗi khi upload file'
    }
  }
}

// Delete file from Cloudinary
export async function deleteFile(publicId: string, resourceType: 'image' | 'video' | 'raw' = 'image'): Promise<{
  success: boolean
  error?: string
}> {
  try {
    await cloudinary.uploader.destroy(publicId, { resource_type: resourceType })
    return { success: true }
  } catch (error) {
    console.error('Cloudinary delete error:', error)
    return {
      success: false,
      error: 'Lỗi khi xóa file'
    }
  }
}

// Generate signed upload URL for direct uploads
export function generateSignedUploadUrl(options: {
  folder?: string
  tags?: string[]
  public_id?: string
  resource_type?: 'image' | 'video' | 'raw'
  transformation?: any[]
}): {
  url: string
  signature: string
  timestamp: number
  api_key: string
  cloud_name: string
} {
  const timestamp = Math.round(new Date().getTime() / 1000)
  
  const params = {
    timestamp,
    folder: options.folder || 'webta/uploads',
    tags: options.tags?.join(',') || 'webta',
    ...options
  }

  const signature = cloudinary.utils.api_sign_request(params, process.env.CLOUDINARY_API_SECRET!)

  return {
    url: `https://api.cloudinary.com/v1_1/${process.env.CLOUDINARY_CLOUD_NAME}/${options.resource_type || 'image'}/upload`,
    signature,
    timestamp,
    api_key: process.env.CLOUDINARY_API_KEY!,
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME!
  }
}

// Generate video thumbnail
export async function generateVideoThumbnail(videoPublicId: string): Promise<{
  success: boolean
  thumbnailUrl?: string
  error?: string
}> {
  try {
    const thumbnailUrl = cloudinary.url(videoPublicId, {
      resource_type: 'video',
      transformation: [
        { width: 640, height: 360, crop: 'fill' },
        { quality: 'auto', fetch_format: 'auto' },
        { start_offset: '10%' } // Take thumbnail at 10% of video duration
      ]
    })

    return {
      success: true,
      thumbnailUrl
    }
  } catch (error) {
    console.error('Error generating video thumbnail:', error)
    return {
      success: false,
      error: 'Lỗi khi tạo thumbnail video'
    }
  }
}

// Get file info from Cloudinary
export async function getFileInfo(publicId: string, resourceType: 'image' | 'video' | 'raw' = 'image'): Promise<{
  success: boolean
  data?: any
  error?: string
}> {
  try {
    const result = await cloudinary.api.resource(publicId, { resource_type: resourceType })
    return {
      success: true,
      data: result
    }
  } catch (error) {
    console.error('Error getting file info:', error)
    return {
      success: false,
      error: 'Lỗi khi lấy thông tin file'
    }
  }
}

export default cloudinary
