#!/usr/bin/env node

/**
 * Phase 0 Verification Script
 * Kiểm tra tất cả deliverables của Phase 0 đã được tạo đúng
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Đang kiểm tra Phase 0 deliverables...\n')

// <PERSON>h sách files cần kiểm tra
const requiredFiles = [
  // Project Configuration
  'package.json',
  'next.config.js',
  'tailwind.config.js',
  'postcss.config.js',
  'tsconfig.json',
  '.gitignore',
  'README.md',
  
  // Environment & Docker
  '.env.example',
  'docker-compose.yml',
  'Dockerfile',
  
  // Development Tools
  '.eslintrc.json',
  '.prettierrc',
  '.prettierignore',
  'jest.config.js',
  'jest.setup.js',
  
  // Source Code Structure
  'src/app/layout.tsx',
  'src/app/page.tsx',
  'src/app/globals.css',
  
  // Database & Models
  'src/lib/mongodb.ts',
  'src/models/User.ts',
  'src/models/Course.ts',
  
  // Authentication
  'src/lib/auth.ts',
  'src/app/api/auth/[...nextauth]/route.ts',
  'src/app/api/auth/register/route.ts',
  'src/middleware.ts',
  'src/app/auth/signin/page.tsx',
  'src/app/auth/signup/page.tsx',
  'src/app/auth/error/page.tsx',
  
  // UI Components
  'src/components/ui/Button.tsx',
  'src/components/ui/Input.tsx',
  'src/components/ui/Card.tsx',
  'src/components/ui/Badge.tsx',
  'src/components/ui/Loading.tsx',
  'src/components/ui/Alert.tsx',
  'src/components/ui/Modal.tsx',
  'src/components/ui/Form.tsx',
  'src/components/ui/Toast.tsx',
  
  // Layout Components
  'src/components/layout/Header.tsx',
  'src/components/layout/Footer.tsx',
  
  // Utilities
  'src/lib/utils.ts',
  
  // Tests
  'src/lib/__tests__/utils.test.ts',
  'src/components/ui/__tests__/Button.test.tsx',
  
  // Documentation
  'PHASE-0-COMPLETION-REPORT.md'
]

// Danh sách directories cần kiểm tra
const requiredDirectories = [
  'src',
  'src/app',
  'src/app/api',
  'src/app/api/auth',
  'src/app/auth',
  'src/components',
  'src/components/ui',
  'src/components/layout',
  'src/lib',
  'src/models',
  'scripts'
]

let allPassed = true
let totalChecks = 0
let passedChecks = 0

// Kiểm tra directories
console.log('📁 Kiểm tra cấu trúc thư mục:')
requiredDirectories.forEach(dir => {
  totalChecks++
  if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
    console.log(`  ✅ ${dir}`)
    passedChecks++
  } else {
    console.log(`  ❌ ${dir} - THIẾU`)
    allPassed = false
  }
})

console.log('\n📄 Kiểm tra files:')
requiredFiles.forEach(file => {
  totalChecks++
  if (fs.existsSync(file) && fs.statSync(file).isFile()) {
    console.log(`  ✅ ${file}`)
    passedChecks++
  } else {
    console.log(`  ❌ ${file} - THIẾU`)
    allPassed = false
  }
})

// Kiểm tra package.json dependencies
console.log('\n📦 Kiểm tra dependencies:')
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const requiredDeps = [
    'next',
    'react',
    'react-dom',
    'typescript',
    'mongoose',
    'next-auth',
    'tailwindcss',
    'zod'
  ]
  
  const requiredDevDeps = [
    'eslint',
    'prettier',
    'jest',
    '@testing-library/react',
    '@testing-library/jest-dom'
  ]
  
  requiredDeps.forEach(dep => {
    totalChecks++
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`  ✅ ${dep}`)
      passedChecks++
    } else {
      console.log(`  ❌ ${dep} - THIẾU trong dependencies`)
      allPassed = false
    }
  })
  
  requiredDevDeps.forEach(dep => {
    totalChecks++
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`  ✅ ${dep} (dev)`)
      passedChecks++
    } else {
      console.log(`  ❌ ${dep} - THIẾU trong devDependencies`)
      allPassed = false
    }
  })
}

// Kiểm tra scripts trong package.json
console.log('\n🔧 Kiểm tra scripts:')
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const requiredScripts = [
    'dev',
    'build',
    'start',
    'lint',
    'test',
    'type-check'
  ]
  
  requiredScripts.forEach(script => {
    totalChecks++
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`  ✅ npm run ${script}`)
      passedChecks++
    } else {
      console.log(`  ❌ npm run ${script} - THIẾU`)
      allPassed = false
    }
  })
}

// Kiểm tra TypeScript configuration
console.log('\n⚙️ Kiểm tra TypeScript config:')
if (fs.existsSync('tsconfig.json')) {
  totalChecks++
  try {
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'))
    if (tsConfig.compilerOptions && tsConfig.compilerOptions.paths && tsConfig.compilerOptions.paths['@/*']) {
      console.log('  ✅ Path mapping (@/*) configured')
      passedChecks++
    } else {
      console.log('  ❌ Path mapping (@/*) not configured')
      allPassed = false
    }
  } catch (error) {
    console.log('  ❌ tsconfig.json invalid JSON')
    allPassed = false
  }
}

// Kiểm tra environment variables
console.log('\n🌍 Kiểm tra environment setup:')
if (fs.existsSync('.env.example')) {
  totalChecks++
  const envExample = fs.readFileSync('.env.example', 'utf8')
  const requiredEnvVars = [
    'MONGODB_URI',
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET'
  ]
  
  let envVarsFound = 0
  requiredEnvVars.forEach(envVar => {
    if (envExample.includes(envVar)) {
      envVarsFound++
    }
  })
  
  if (envVarsFound === requiredEnvVars.length) {
    console.log('  ✅ All required environment variables in .env.example')
    passedChecks++
  } else {
    console.log(`  ❌ Missing environment variables (${envVarsFound}/${requiredEnvVars.length})`)
    allPassed = false
  }
}

// Tổng kết
console.log('\n' + '='.repeat(50))
console.log(`📊 KẾT QUẢ KIỂM TRA PHASE 0`)
console.log('='.repeat(50))
console.log(`Tổng số kiểm tra: ${totalChecks}`)
console.log(`Đã pass: ${passedChecks}`)
console.log(`Thất bại: ${totalChecks - passedChecks}`)
console.log(`Tỷ lệ hoàn thành: ${Math.round((passedChecks / totalChecks) * 100)}%`)

if (allPassed) {
  console.log('\n🎉 PHASE 0 HOÀN THÀNH THÀNH CÔNG!')
  console.log('✅ Tất cả deliverables đã được tạo đúng')
  console.log('🚀 Sẵn sàng chuyển sang Phase 1')
} else {
  console.log('\n⚠️  PHASE 0 CHƯA HOÀN THÀNH')
  console.log('❌ Vui lòng tạo các files/directories còn thiếu')
  console.log('📋 Xem PHASE-0-COMPLETION-REPORT.md để biết chi tiết')
}

console.log('\n📚 Hướng dẫn tiếp theo:')
console.log('1. Chạy: npm install')
console.log('2. Tạo file .env.local từ .env.example')
console.log('3. Chạy: npm run dev')
console.log('4. Truy cập: http://localhost:3000')
console.log('5. Test authentication tại /auth/signin')

process.exit(allPassed ? 0 : 1)
