"use strict";(()=>{var e={};e.id=194,e.ids=[194,544],e.modules={38013:e=>{e.exports=require("mongodb")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},85258:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>x,patchFetch:()=>D,requestAsyncStorage:()=>S,routeModule:()=>w,serverHooks:()=>N,staticGenerationAsyncStorage:()=>v});var r={};s.r(r),s.d(r,{DELETE:()=>b,GET:()=>E,POST:()=>y});var n=s(49303),i=s(88716),a=s(60670),o=s(87070),u=s(75571),c=s(95456),l=s(14184),d=s(89332),p=s(66820),h=s(9133),m=s(11185),g=s.n(m);let f=h.z.object({type:h.z.enum(["free","paid","activation_code"]),activationCode:h.z.string().optional(),paymentIntentId:h.z.string().optional()});async function y(e,{params:t}){try{await (0,l.ZP)();let{id:s}=t;if(!g().Types.ObjectId.isValid(s))return o.NextResponse.json({success:!1,error:"ID kh\xf3a học kh\xf4ng hợp lệ"},{status:400});let r=await (0,u.getServerSession)(c.L);if(!r?.user?.id)return o.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập để đăng k\xfd kh\xf3a học"},{status:401});let n=await e.json(),{type:i,activationCode:a,paymentIntentId:h}=f.parse(n),m=await d.ZP.findById(s);if(!m)return o.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy kh\xf3a học"},{status:404});if(m.status!==d.D4.PUBLISHED)return o.NextResponse.json({success:!1,error:"Kh\xf3a học chưa được xuất bản"},{status:400});let y=await p.default.findOne({userId:r.user.id,courseId:s});if(y){if(y.status===p.mh.ACTIVE)return o.NextResponse.json({success:!1,error:"Bạn đ\xe3 đăng k\xfd kh\xf3a học n\xe0y rồi"},{status:400});if(y.status===p.mh.SUSPENDED)return y.status=p.mh.ACTIVE,y.lastAccessedAt=new Date,await y.save(),o.NextResponse.json({success:!0,data:y,message:"Đ\xe3 k\xedch hoạt lại kh\xf3a học th\xe0nh c\xf4ng"})}let E={userId:r.user.id,courseId:s,status:p.mh.ACTIVE,type:p.xM.FREE,payment:{amount:0,currency:"VND",method:"free"}};switch(i){case"free":if(m.pricing.basePrice>0)return o.NextResponse.json({success:!1,error:"Kh\xf3a học n\xe0y kh\xf4ng miễn ph\xed"},{status:400});break;case"paid":if(!h)return o.NextResponse.json({success:!1,error:"Thiếu th\xf4ng tin thanh to\xe1n"},{status:400});E.type=p.xM.PAID,E.payment={amount:m.pricing.basePrice,currency:m.pricing.currency,method:"stripe",transactionId:h,paidAt:new Date};break;case"activation_code":if(!a)return o.NextResponse.json({success:!1,error:"Thiếu m\xe3 k\xedch hoạt"},{status:400});E.type=p.xM.ACTIVATION_CODE,E.payment={amount:0,currency:"VND",method:"activation_code",activationCode:a};break;default:return o.NextResponse.json({success:!1,error:"Loại đăng k\xfd kh\xf4ng hợp lệ"},{status:400})}let b=new p.default(E);return await b.updateProgress(),await b.save(),await d.ZP.findByIdAndUpdate(s,{$inc:{"stats.totalStudents":1},"stats.lastUpdated":new Date}),await b.populate("courseId","title slug thumbnail"),await b.populate("userId","profile.firstName profile.lastName"),o.NextResponse.json({success:!0,data:b,message:"Đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng"},{status:201})}catch(e){if(console.error("Error enrolling in course:",e),e instanceof h.z.ZodError)return o.NextResponse.json({success:!1,error:"Dữ liệu kh\xf4ng hợp lệ",details:e.errors},{status:400});return o.NextResponse.json({success:!1,error:"Lỗi server khi đăng k\xfd kh\xf3a học"},{status:500})}}async function E(e,{params:t}){try{await (0,l.ZP)();let{id:e}=t;if(!g().Types.ObjectId.isValid(e))return o.NextResponse.json({success:!1,error:"ID kh\xf3a học kh\xf4ng hợp lệ"},{status:400});let s=await (0,u.getServerSession)(c.L);if(!s?.user?.id)return o.NextResponse.json({success:!1,data:{isEnrolled:!1,canEnroll:!0,enrollment:null}});let r=await p.default.findOne({userId:s.user.id,courseId:e}).populate("courseId","title slug pricing").lean(),n=r&&r.status===p.mh.ACTIVE;return o.NextResponse.json({success:!0,data:{isEnrolled:n,canEnroll:!n,enrollment:n?r:null}})}catch(e){return console.error("Error checking enrollment status:",e),o.NextResponse.json({success:!1,error:"Lỗi server khi kiểm tra trạng th\xe1i đăng k\xfd"},{status:500})}}async function b(e,{params:t}){try{await (0,l.ZP)();let{id:e}=t;if(!g().Types.ObjectId.isValid(e))return o.NextResponse.json({success:!1,error:"ID kh\xf3a học kh\xf4ng hợp lệ"},{status:400});let s=await (0,u.getServerSession)(c.L);if(!s?.user?.id)return o.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});let r=await p.default.findOne({userId:s.user.id,courseId:e});if(!r)return o.NextResponse.json({success:!1,error:"Bạn chưa đăng k\xfd kh\xf3a học n\xe0y"},{status:404});if(r.status!==p.mh.ACTIVE)return o.NextResponse.json({success:!1,error:"Kh\xf3a học kh\xf4ng ở trạng th\xe1i hoạt động"},{status:400});let n=(Date.now()-r.createdAt.getTime())/864e5<=7&&r.progress.completionPercentage<20;if(r.type===p.xM.PAID&&!n)return o.NextResponse.json({success:!1,error:"Kh\xf4ng thể hủy kh\xf3a học đ\xe3 thanh to\xe1n sau 7 ng\xe0y hoặc khi đ\xe3 học qu\xe1 20%"},{status:400});return r.status=p.mh.REFUNDED,await r.save(),await d.ZP.findByIdAndUpdate(e,{$inc:{"stats.totalStudents":-1},"stats.lastUpdated":new Date}),o.NextResponse.json({success:!0,message:"Đ\xe3 hủy đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng"})}catch(e){return console.error("Error unenrolling from course:",e),o.NextResponse.json({success:!1,error:"Lỗi server khi hủy đăng k\xfd kh\xf3a học"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/courses/[id]/enroll/route",pathname:"/api/courses/[id]/enroll",filename:"route",bundlePath:"app/api/courses/[id]/enroll/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\courses\\[id]\\enroll\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:S,staticGenerationAsyncStorage:v,serverHooks:N}=w,x="/api/courses/[id]/enroll/route";function D(){return(0,a.patchFetch)({serverHooks:N,staticGenerationAsyncStorage:v})}},95456:(e,t,s)=>{s.d(t,{L:()=>l});var r=s(53797),n=s(77234),i=s(41017),a=s(38013),o=s(14184),u=s(93330);let c=new a.MongoClient(process.env.MONGODB_URI).connect(),l={adapter:(0,i.dJ)(c),secret:process.env.NEXTAUTH_SECRET,providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email v\xe0 mật khẩu l\xe0 bắt buộc");try{await (0,o.ZP)();let t=await u.ZP.findOne({email:e.email.toLowerCase()}).select("+password");if(!t)throw Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");if(t.isLocked())throw Error("T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần");if(t.status!==u.J0.ACTIVE)throw Error("T\xe0i khoản chưa được k\xedch hoạt");if(!await t.comparePassword(e.password))throw await t.incrementLoginAttempts(),Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");return t.loginAttempts>0&&await t.updateOne({$unset:{loginAttempts:1,lockUntil:1}}),await t.updateOne({lastLogin:new Date}),{id:t._id.toString(),email:t.email,name:t.profile.fullName,role:t.role,status:t.status,emailVerified:t.emailVerified,image:t.profile.avatar}}catch(e){throw Error(e.message||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập")}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:e,user:t,account:s})=>(s&&t&&(e.role=t.role,e.status=t.status,e.emailVerified=t.emailVerified),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.status=t.status,e.user.emailVerified=t.emailVerified),e),signIn:async({user:e,account:t,profile:s})=>t?.provider!=="credentials"||!0===e.emailVerified,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:s,isNewUser:r}){console.log(`User ${e.email} signed in with ${t?.provider}`)},async signOut({session:e,token:t}){console.log("User signed out")},async createUser({user:e}){console.log(`New user created: ${e.email}`)}},debug:!1}},14184:(e,t,s)=>{s.d(t,{ZP:()=>o});var r=s(11185),n=s.n(r);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(i,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},89332:(e,t,s)=>{s.d(t,{D4:()=>r,ZP:()=>c,f:()=>n,ij:()=>i});var r,n,i,a=s(11185),o=s.n(a);(function(e){e.DRAFT="draft",e.PUBLISHED="published",e.ARCHIVED="archived",e.SUSPENDED="suspended"})(r||(r={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(n||(n={})),function(e){e.ENGLISH="english",e.VIETNAMESE="vietnamese",e.CHINESE="chinese",e.JAPANESE="japanese",e.KOREAN="korean",e.FRENCH="french",e.GERMAN="german",e.SPANISH="spanish"}(i||(i={}));let u=new a.Schema({title:{type:String,required:[!0,"Ti\xeau đề kh\xf3a học l\xe0 bắt buộc"],trim:!0,maxlength:[200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"]},slug:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},shortDescription:{type:String,required:[!0,"M\xf4 tả ngắn l\xe0 bắt buộc"],maxlength:[500,"M\xf4 tả ngắn kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},content:{description:{type:String,required:[!0,"M\xf4 tả chi tiết l\xe0 bắt buộc"]},objectives:[{type:String,required:!0}],prerequisites:[String],syllabus:[{week:{type:Number,required:!0},title:{type:String,required:!0},topics:[String],duration:{type:Number,required:!0}}]},instructor:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"Giảng vi\xean l\xe0 bắt buộc"]},category:{type:a.Schema.Types.ObjectId,ref:"Category",required:[!0,"Danh mục l\xe0 bắt buộc"]},subcategory:{type:a.Schema.Types.ObjectId,ref:"Category"},language:{type:String,enum:Object.values(i),required:[!0,"Ng\xf4n ngữ kh\xf3a học l\xe0 bắt buộc"]},level:{type:String,enum:Object.values(n),required:[!0,"Cấp độ kh\xf3a học l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"draft"},thumbnail:String,previewVideo:String,tags:[String],pricing:{basePrice:{type:Number,required:[!0,"Gi\xe1 kh\xf3a học l\xe0 bắt buộc"],min:[0,"Gi\xe1 kh\xf4ng được \xe2m"]},currency:{type:String,default:"VND"},discountPrice:{type:Number,min:[0,"Gi\xe1 giảm kh\xf4ng được \xe2m"]},discountValidUntil:Date,installmentOptions:{enabled:{type:Boolean,default:!1},plans:[{months:{type:Number,required:!0},monthlyAmount:{type:Number,required:!0}}]}},settings:{maxStudents:{type:Number,min:[1,"Số học vi\xean tối đa phải \xedt nhất l\xe0 1"]},allowComments:{type:Boolean,default:!0},allowRatings:{type:Boolean,default:!0},certificateEnabled:{type:Boolean,default:!0},downloadableResources:{type:Boolean,default:!0},mobileAccess:{type:Boolean,default:!0},lifetimeAccess:{type:Boolean,default:!0},accessDuration:{type:Number,min:[1,"Thời gian truy cập phải \xedt nhất 1 ng\xe0y"]}},stats:{totalStudents:{type:Number,default:0},totalLessons:{type:Number,default:0},totalDuration:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRatings:{type:Number,default:0},completionRate:{type:Number,default:0,min:0,max:100}},seo:{metaTitle:{type:String,maxlength:[60,"Meta title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},metaDescription:{type:String,maxlength:[160,"Meta description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},publishedAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});u.index({slug:1}),u.index({instructor:1}),u.index({category:1,subcategory:1}),u.index({language:1,level:1}),u.index({status:1,publishedAt:-1}),u.index({"stats.averageRating":-1}),u.index({"stats.totalStudents":-1}),u.index({tags:1}),u.index({"pricing.basePrice":1}),u.virtual("isOnSale").get(function(){return!!(this.pricing.discountPrice&&this.pricing.discountValidUntil&&this.pricing.discountValidUntil>new Date)}),u.virtual("currentPrice").get(function(){return this.isOnSale?this.pricing.discountPrice:this.pricing.basePrice}),u.pre("save",function(e){this.isModified("title")&&!this.slug&&(this.slug=this.generateSlug()),e()}),u.methods.generateSlug=function(){return this.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},u.methods.updateStats=async function(){console.log("Updating course stats...")},u.methods.isPublished=function(){return"published"===this.status&&!!this.publishedAt},u.methods.canEnroll=function(){return!!this.isPublished()&&(!this.settings.maxStudents||!(this.stats.totalStudents>=this.settings.maxStudents))};let c=o().models.Course||o().model("Course",u)},66820:(e,t,s)=>{s.d(t,{default:()=>l,mh:()=>r,xM:()=>n});var r,n,i,a=s(11185),o=s.n(a);(function(e){e.ACTIVE="active",e.COMPLETED="completed",e.SUSPENDED="suspended",e.EXPIRED="expired",e.REFUNDED="refunded"})(r||(r={})),function(e){e.PAID="paid",e.FREE="free",e.ACTIVATION_CODE="activation_code",e.ADMIN_GRANTED="admin_granted"}(n||(n={})),function(e){e.NOT_STARTED="not_started",e.IN_PROGRESS="in_progress",e.COMPLETED="completed"}(i||(i={}));let u=new a.Schema({lessonId:{type:a.Schema.Types.ObjectId,ref:"Lesson",required:!0},status:{type:String,enum:Object.values(i),default:"not_started"},startedAt:Date,completedAt:Date,watchTime:{type:Number,default:0,min:0},lastPosition:{type:Number,default:0,min:0},attempts:{type:Number,default:0,min:0},score:{type:Number,min:0,max:100},notes:String},{_id:!1}),c=new a.Schema({userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID l\xe0 bắt buộc"]},courseId:{type:a.Schema.Types.ObjectId,ref:"Course",required:[!0,"Course ID l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"active"},type:{type:String,enum:Object.values(n),required:[!0,"Loại đăng k\xfd l\xe0 bắt buộc"]},progress:{status:{type:String,enum:Object.values(i),default:"not_started"},completedLessons:{type:Number,default:0,min:0},totalLessons:{type:Number,default:0,min:0},completionPercentage:{type:Number,default:0,min:0,max:100},totalWatchTime:{type:Number,default:0,min:0},lastAccessedAt:{type:Date,default:Date.now},estimatedCompletionDate:Date,lessons:[u]},payment:{amount:{type:Number,required:!0,min:0},currency:{type:String,default:"VND"},method:{type:String,enum:["stripe","paypal","activation_code","free"],required:!0},transactionId:String,activationCode:String,paidAt:Date,refundedAt:Date,refundAmount:{type:Number,min:0}},certificate:{issued:{type:Boolean,default:!1},issuedAt:Date,certificateId:String,downloadUrl:String,validUntil:Date},accessGrantedAt:{type:Date,default:Date.now},accessExpiresAt:Date,lastAccessedAt:{type:Date,default:Date.now},enrolledBy:{type:a.Schema.Types.ObjectId,ref:"User"},notes:String},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});c.index({userId:1,courseId:1},{unique:!0}),c.index({userId:1,status:1}),c.index({courseId:1,status:1}),c.index({status:1,accessExpiresAt:1}),c.index({"payment.method":1,"payment.paidAt":-1}),c.index({"progress.status":1}),c.index({"progress.completionPercentage":-1}),c.index({lastAccessedAt:-1}),c.virtual("isActive").get(function(){return"active"===this.status&&this.canAccess()}),c.virtual("daysRemaining").get(function(){if(!this.accessExpiresAt)return null;let e=new Date;return Math.ceil((this.accessExpiresAt.getTime()-e.getTime())/864e5)}),c.pre("save",async function(e){this.isModified("progress.lessons")&&(this.progress.completedLessons=this.progress.lessons.filter(e=>"completed"===e.status).length,this.progress.completionPercentage=this.calculateCompletionPercentage(),0===this.progress.completionPercentage?this.progress.status="not_started":100===this.progress.completionPercentage?(this.progress.status="completed",this.status="completed"):this.progress.status="in_progress"),e()}),c.methods.updateProgress=async function(){let e=o().model("Lesson"),t=await e.find({courseId:this.courseId,status:"published"}).sort({order:1});for(let e of(this.progress.totalLessons=t.length,t))this.progress.lessons.find(t=>t.lessonId.toString()===e._id.toString())||this.progress.lessons.push({lessonId:e._id,status:"not_started",watchTime:0,lastPosition:0,attempts:0});this.progress.totalWatchTime=this.progress.lessons.reduce((e,t)=>e+t.watchTime,0),await this.save()},c.methods.calculateCompletionPercentage=function(){return 0===this.progress.totalLessons?0:Math.round(this.progress.completedLessons/this.progress.totalLessons*100)},c.methods.isExpired=function(){return!!this.accessExpiresAt&&new Date>this.accessExpiresAt},c.methods.canAccess=function(){return!("active"!==this.status||this.isExpired())},c.methods.generateCertificate=async function(){if("completed"!==this.progress.status)throw Error("Kh\xf3a học chưa ho\xe0n th\xe0nh");if(this.certificate.issued)throw Error("Chứng chỉ đ\xe3 được cấp");let e=`CERT-${this.courseId}-${this.userId}-${Date.now()}`;this.certificate={issued:!0,issuedAt:new Date,certificateId:e,downloadUrl:`/api/certificates/${e}`,validUntil:new Date(Date.now()+31536e6)},await this.save()},c.methods.extendAccess=async function(e){this.accessExpiresAt?this.accessExpiresAt=new Date(this.accessExpiresAt.getTime()+864e5*e):this.accessExpiresAt=new Date(Date.now()+864e5*e),await this.save()},c.statics.getActiveEnrollments=function(e){return this.find({userId:e,status:"active",$or:[{accessExpiresAt:{$exists:!1}},{accessExpiresAt:{$gt:new Date}}]}).populate("courseId")},c.statics.getCourseStats=async function(e){return(await this.aggregate([{$match:{courseId:new(o()).Types.ObjectId(e)}},{$group:{_id:null,totalEnrollments:{$sum:1},activeEnrollments:{$sum:{$cond:[{$eq:["$status","active"]},1,0]}},completedEnrollments:{$sum:{$cond:[{$eq:["$status","completed"]},1,0]}},averageProgress:{$avg:"$progress.completionPercentage"},totalRevenue:{$sum:"$payment.amount"}}}]))[0]||{totalEnrollments:0,activeEnrollments:0,completedEnrollments:0,averageProgress:0,totalRevenue:0}};let l=o().models.Enrollment||o().model("Enrollment",c)},93330:(e,t,s)=>{s.d(t,{J0:()=>n,ZP:()=>d,i4:()=>r});var r,n,i,a=s(11185),o=s.n(a),u=s(42023),c=s.n(u);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(r||(r={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(n||(n={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(i||(i={}));let l=new a.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(r),default:"student"},status:{type:String,enum:Object.values(n),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(i)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});l.index({"profile.firstName":1,"profile.lastName":1}),l.index({role:1,status:1}),l.index({createdAt:-1}),l.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),l.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),l.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await c().genSalt(12);this.password=await c().hash(this.password,t),e()}catch(t){e(t)}}),l.methods.comparePassword=async function(e){return c().compare(e,this.password)},l.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},l.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},l.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},l.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let d=o().models.User||o().model("User",l)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,242,70,799,133],()=>s(85258));module.exports=r})();