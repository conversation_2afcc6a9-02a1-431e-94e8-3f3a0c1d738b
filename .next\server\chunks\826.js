exports.id=826,exports.ids=[826],exports.modules={83395:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},31485:(e,s,r)=>{Promise.resolve().then(r.bind(r,42126)),Promise.resolve().then(r.bind(r,25429))},42126:(e,s,r)=>{"use strict";r.d(s,{Providers:()=>i});var t=r(10326),n=r(77109),a=r(20603);function i({children:e,session:s}){return t.jsx(n.<PERSON><PERSON>,{session:s,children:t.jsx(a.VW,{children:e})})}},25429:(e,s,r)=>{"use strict";r.d(s,{ConditionalLayout:()=>m});var t=r(10326),n=r(35047),a=r(17577),i=r(90434),l=r(77109),o=r(99837),c=r(51223);function d(){let{data:e,status:s}=(0,l.useSession)(),[r,n]=(0,a.useState)(!1),[d,h]=(0,a.useState)(!1),m=[{name:"Trang chủ",href:"/"},{name:"Kh\xf3a học",href:"/courses"},{name:"Về ch\xfang t\xf4i",href:"/about"},{name:"Li\xean hệ",href:"/contact"}],x=e?[{name:"Dashboard",href:"/dashboard"},{name:"Hồ sơ",href:"/profile"},{name:"C\xe0i đặt",href:"/settings"}]:[];return t.jsx("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[t.jsx("div",{className:"flex-shrink-0",children:(0,t.jsxs)(i.default,{href:"/",className:"flex items-center",children:[t.jsx("div",{className:"text-2xl font-bold text-primary",children:"WebTA"}),t.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"LMS"})]})}),t.jsx("nav",{className:"nav-mobile-hidden space-x-8",children:m.map(e=>t.jsx(i.default,{href:e.href,className:"text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium transition-colors",children:e.name},e.name))}),t.jsx("div",{className:"hidden md:flex items-center space-x-4",children:d&&"loading"!==s?e?(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{className:"flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-primary",children:[t.jsx("div",{className:"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center",children:e.user.name?.charAt(0).toUpperCase()}),t.jsx("span",{children:e.user.name})]}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200",children:[x.map(e=>t.jsx(i.default,{href:e.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:e.name},e.name)),t.jsx("hr",{className:"my-1"}),t.jsx("button",{onClick:()=>(0,l.signOut)(),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Đăng xuất"})]})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(i.default,{href:"/auth/signin",children:t.jsx(o.z,{variant:"ghost",size:"sm",children:"Đăng nhập"})}),t.jsx(i.default,{href:"/auth/signup",children:t.jsx(o.z,{size:"sm",children:"Đăng k\xfd"})})]}):t.jsx("div",{className:"animate-pulse",children:t.jsx("div",{className:"h-8 w-20 bg-gray-200 rounded"})})}),t.jsx("div",{className:"md:hidden",children:t.jsx("button",{onClick:()=>n(!r),className:"text-gray-700 hover:text-primary p-2",children:t.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r?t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),t.jsx("div",{className:(0,c.cn)("md:hidden",r?"block":"hidden"),children:(0,t.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 border-t",children:[m.map(e=>t.jsx(i.default,{href:e.href,className:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md",onClick:()=>n(!1),children:e.name},e.name)),e?(0,t.jsxs)(t.Fragment,{children:[t.jsx("hr",{className:"my-2"}),x.map(e=>t.jsx(i.default,{href:e.href,className:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md",onClick:()=>n(!1),children:e.name},e.name)),t.jsx("button",{onClick:()=>{(0,l.signOut)(),n(!1)},className:"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md",children:"Đăng xuất"})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx("hr",{className:"my-2"}),t.jsx(i.default,{href:"/auth/signin",className:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md",onClick:()=>n(!1),children:"Đăng nhập"}),t.jsx(i.default,{href:"/auth/signup",className:"block px-3 py-2 text-base font-medium text-primary hover:bg-gray-50 rounded-md",onClick:()=>n(!1),children:"Đăng k\xfd"})]})]})})]})})}function h(){let e=new Date().getFullYear(),s=[{name:"Facebook",href:"#",icon:t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{name:"Twitter",href:"#",icon:t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{name:"YouTube",href:"#",icon:t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})},{name:"LinkedIn",href:"#",icon:t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})}];return t.jsx("footer",{className:"bg-gray-900 text-white",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsxs)(i.default,{href:"/",className:"flex items-center mb-4",children:[t.jsx("div",{className:"text-2xl font-bold text-white",children:"WebTA"}),t.jsx("span",{className:"ml-2 text-sm text-gray-400",children:"LMS"})]}),t.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Hệ thống quản l\xfd học tập to\xe0n diện với t\xednh năng đ\xe1nh gi\xe1 tự động bằng AI cho 4 kỹ năng ng\xf4n ngữ."}),t.jsx("div",{className:"flex space-x-4",children:s.map(e=>(0,t.jsxs)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:[t.jsx("span",{className:"sr-only",children:e.name}),e.icon]},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-semibold text-white uppercase tracking-wider mb-4",children:"Sản phẩm"}),t.jsx("ul",{className:"space-y-2",children:[{name:"Kh\xf3a học",href:"/courses"},{name:"T\xednh năng",href:"/features"},{name:"Gi\xe1 cả",href:"/pricing"},{name:"Đ\xe1nh gi\xe1 AI",href:"/ai-assessment"}].map(e=>t.jsx("li",{children:t.jsx(i.default,{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-semibold text-white uppercase tracking-wider mb-4",children:"C\xf4ng ty"}),t.jsx("ul",{className:"space-y-2",children:[{name:"Về ch\xfang t\xf4i",href:"/about"},{name:"Blog",href:"/blog"},{name:"Tuyển dụng",href:"/careers"},{name:"Li\xean hệ",href:"/contact"}].map(e=>t.jsx("li",{children:t.jsx(i.default,{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-semibold text-white uppercase tracking-wider mb-4",children:"Hỗ trợ"}),t.jsx("ul",{className:"space-y-2",children:[{name:"Trung t\xe2m trợ gi\xfap",href:"/help"},{name:"Hướng dẫn",href:"/guides"},{name:"FAQ",href:"/faq"},{name:"Hỗ trợ kỹ thuật",href:"/support"}].map(e=>t.jsx("li",{children:t.jsx(i.default,{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-semibold text-white uppercase tracking-wider mb-4",children:"Ph\xe1p l\xfd"}),t.jsx("ul",{className:"space-y-2",children:[{name:"Điều khoản sử dụng",href:"/terms"},{name:"Ch\xednh s\xe1ch bảo mật",href:"/privacy"},{name:"Ch\xednh s\xe1ch cookie",href:"/cookies"},{name:"Bảo mật",href:"/security"}].map(e=>t.jsx("li",{children:t.jsx(i.default,{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]})]}),t.jsx("div",{className:"mt-8 pt-8 border-t border-gray-800",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("p",{className:"text-gray-400 text-sm",children:["\xa9 ",e," WebTA LMS. Tất cả quyền được bảo lưu."]}),t.jsx("div",{className:"mt-4 md:mt-0",children:t.jsx("p",{className:"text-gray-400 text-sm",children:"Được ph\xe1t triển với ❤️ tại Việt Nam"})})]})})]})})}function m({children:e}){let s=(0,n.usePathname)();return s?.startsWith("/dashboard")?t.jsx(t.Fragment,{children:e}):(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[t.jsx(d,{}),t.jsx("main",{className:"flex-1",children:e}),t.jsx(h,{})]})}},99837:(e,s,r)=>{"use strict";r.d(s,{z:()=>o});var t=r(10326),n=r(17577),a=r(79360),i=r(51223);let l=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef(({className:e,variant:s,size:r,asChild:n=!1,...a},o)=>t.jsx("button",{className:(0,i.cn)(l({variant:s,size:r,className:e})),ref:o,...a}));o.displayName="Button"},20603:(e,s,r)=>{"use strict";r.d(s,{VW:()=>o,pm:()=>c});var t=r(10326),n=r(17577),a=r(60962),i=r(51223);let l=(0,n.createContext)(void 0);function o({children:e}){let[s,r]=(0,n.useState)([]),a=(0,n.useCallback)(e=>{let s=Math.random().toString(36).substr(2,9),t={id:s,duration:5e3,...e};r(e=>[...e,t]),t.duration&&t.duration>0&&setTimeout(()=>{i(s)},t.duration)},[]),i=(0,n.useCallback)(e=>{r(s=>s.filter(s=>s.id!==e))},[]),o=(0,n.useCallback)(()=>{r([])},[]);return(0,t.jsxs)(l.Provider,{value:{toasts:s,addToast:a,removeToast:i,clearToasts:o},children:[e,t.jsx(d,{})]})}function c(){let e=(0,n.useContext)(l);if(!e)throw Error("useToast must be used within a ToastProvider");return e}function d(){let{toasts:e}=c(),[s,r]=(0,n.useState)(!1);return(n.useEffect(()=>{r(!0)},[]),s&&0!==e.length)?(0,a.createPortal)(t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(e=>t.jsx(h,{toast:e},e.id))}),document.body):null}function h({toast:e}){let{removeToast:s}=c(),[r,a]=(0,n.useState)(!1);n.useEffect(()=>{let e=setTimeout(()=>a(!0),10);return()=>clearTimeout(e)},[]);let l={default:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),success:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})},o=e.variant||"default";return t.jsx("div",{className:(0,i.cn)("max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border transition-all duration-300 transform",{default:"bg-white border-gray-200",success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[o],r?"translate-x-0 opacity-100":"translate-x-full opacity-0"),children:t.jsx("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[t.jsx("div",{className:(0,i.cn)("flex-shrink-0",{default:"text-gray-400",success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"}[o]),children:l[o]}),(0,t.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[e.title&&t.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.title}),t.jsx("p",{className:(0,i.cn)("text-sm text-gray-500",e.title?"mt-1":""),children:e.message}),e.action&&t.jsx("div",{className:"mt-3",children:t.jsx("button",{onClick:e.action.onClick,className:"text-sm font-medium text-primary hover:text-primary/80",children:e.action.label})})]}),t.jsx("div",{className:"ml-4 flex-shrink-0 flex",children:(0,t.jsxs)("button",{onClick:()=>{a(!1),setTimeout(()=>s(e.id),150)},className:"rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[t.jsx("span",{className:"sr-only",children:"Đ\xf3ng"}),t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})})]})})})}},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>a});var t=r(41135),n=r(31009);function a(...e){return(0,n.m6)((0,t.W)(e))}},39285:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h,metadata:()=>c,viewport:()=>d});var t=r(19510),n=r(25384),a=r.n(n),i=r(68570);let l=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\providers.tsx#Providers`),o=(0,i.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\components\layout\ConditionalLayout.tsx#ConditionalLayout`);r(5023);let c={title:"WebTA LMS - Hệ thống Quản l\xfd Học tập",description:"Hệ thống LMS to\xe0n diện cho việc b\xe1n v\xe0 quản l\xfd kh\xf3a học ngoại ngữ với t\xednh năng đ\xe1nh gi\xe1 tự động bằng AI",keywords:["LMS","học tập","ngoại ngữ","AI","đ\xe1nh gi\xe1"],authors:[{name:"WebTA Team"}]},d={width:"device-width",initialScale:1};async function h({children:e}){return t.jsx("html",{lang:"vi",children:t.jsx("body",{className:a().className,children:t.jsx(l,{session:null,children:t.jsx(o,{children:e})})})})}},5023:()=>{}};