'use client'

import { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from './Button'

interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  minHeight?: number
  maxHeight?: number
}

export function RichTextEditor({
  value = '',
  onChange,
  placeholder = 'Nhập nội dung...',
  className,
  disabled = false,
  minHeight = 200,
  maxHeight = 500
}: RichTextEditorProps) {
  const [content, setContent] = useState(value)
  const [isActive, setIsActive] = useState(false)
  const editorRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (value !== content) {
      setContent(value)
      if (editorRef.current) {
        editorRef.current.innerHTML = value
      }
    }
  }, [value])

  const handleInput = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML
      setContent(newContent)
      onChange?.(newContent)
    }
  }

  const handleFocus = () => {
    setIsActive(true)
  }

  const handleBlur = () => {
    setIsActive(false)
  }

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value)
    editorRef.current?.focus()
    handleInput()
  }

  const isCommandActive = (command: string): boolean => {
    return document.queryCommandState(command)
  }

  const formatButtons = [
    {
      command: 'bold',
      icon: '𝐁',
      title: 'Đậm (Ctrl+B)',
      shortcut: 'Ctrl+B'
    },
    {
      command: 'italic',
      icon: '𝐼',
      title: 'Nghiêng (Ctrl+I)',
      shortcut: 'Ctrl+I'
    },
    {
      command: 'underline',
      icon: '𝐔',
      title: 'Gạch chân (Ctrl+U)',
      shortcut: 'Ctrl+U'
    },
    {
      command: 'strikeThrough',
      icon: '𝐒',
      title: 'Gạch ngang',
      shortcut: ''
    }
  ]

  const alignButtons = [
    {
      command: 'justifyLeft',
      icon: '⬅',
      title: 'Căn trái'
    },
    {
      command: 'justifyCenter',
      icon: '⬌',
      title: 'Căn giữa'
    },
    {
      command: 'justifyRight',
      icon: '➡',
      title: 'Căn phải'
    },
    {
      command: 'justifyFull',
      icon: '⬌',
      title: 'Căn đều'
    }
  ]

  const listButtons = [
    {
      command: 'insertUnorderedList',
      icon: '•',
      title: 'Danh sách không thứ tự'
    },
    {
      command: 'insertOrderedList',
      icon: '1.',
      title: 'Danh sách có thứ tự'
    }
  ]

  const insertLink = () => {
    const url = prompt('Nhập URL:')
    if (url) {
      execCommand('createLink', url)
    }
  }

  const insertImage = () => {
    const url = prompt('Nhập URL hình ảnh:')
    if (url) {
      execCommand('insertImage', url)
    }
  }

  const clearFormatting = () => {
    execCommand('removeFormat')
  }

  return (
    <div className={cn('border rounded-lg overflow-hidden', className)}>
      {/* Toolbar */}
      <div className="border-b bg-gray-50 p-2 flex flex-wrap gap-1">
        {/* Format buttons */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          {formatButtons.map((button) => (
            <Button
              key={button.command}
              variant={isCommandActive(button.command) ? 'default' : 'ghost'}
              size="sm"
              onClick={() => execCommand(button.command)}
              disabled={disabled}
              title={button.title}
              className="w-8 h-8 p-0 text-sm font-bold"
            >
              {button.icon}
            </Button>
          ))}
        </div>

        {/* Alignment buttons */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          {alignButtons.map((button) => (
            <Button
              key={button.command}
              variant={isCommandActive(button.command) ? 'default' : 'ghost'}
              size="sm"
              onClick={() => execCommand(button.command)}
              disabled={disabled}
              title={button.title}
              className="w-8 h-8 p-0 text-sm"
            >
              {button.icon}
            </Button>
          ))}
        </div>

        {/* List buttons */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          {listButtons.map((button) => (
            <Button
              key={button.command}
              variant={isCommandActive(button.command) ? 'default' : 'ghost'}
              size="sm"
              onClick={() => execCommand(button.command)}
              disabled={disabled}
              title={button.title}
              className="w-8 h-8 p-0 text-sm"
            >
              {button.icon}
            </Button>
          ))}
        </div>

        {/* Insert buttons */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={insertLink}
            disabled={disabled}
            title="Chèn liên kết"
            className="w-8 h-8 p-0 text-sm"
          >
            🔗
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={insertImage}
            disabled={disabled}
            title="Chèn hình ảnh"
            className="w-8 h-8 p-0 text-sm"
          >
            🖼️
          </Button>
        </div>

        {/* Clear formatting */}
        <Button
          variant="ghost"
          size="sm"
          onClick={clearFormatting}
          disabled={disabled}
          title="Xóa định dạng"
          className="w-8 h-8 p-0 text-sm"
        >
          🧹
        </Button>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={cn(
          'p-4 outline-none overflow-y-auto',
          'prose prose-sm max-w-none',
          'focus:ring-2 focus:ring-primary focus:ring-inset',
          disabled && 'bg-gray-50 cursor-not-allowed',
          isActive && 'ring-2 ring-primary ring-inset'
        )}
        style={{
          minHeight: `${minHeight}px`,
          maxHeight: `${maxHeight}px`
        }}
        dangerouslySetInnerHTML={{ __html: content }}
        data-placeholder={placeholder}
      />

      {/* Character count */}
      <div className="border-t bg-gray-50 px-4 py-2 text-xs text-gray-500 flex justify-between">
        <span>
          {editorRef.current?.textContent?.length || 0} ký tự
        </span>
        <span className="text-gray-400">
          Hỗ trợ HTML formatting
        </span>
      </div>

      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        
        [contenteditable] img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
        }
        
        [contenteditable] a {
          color: #3b82f6;
          text-decoration: underline;
        }
        
        [contenteditable] ul, [contenteditable] ol {
          padding-left: 1.5rem;
        }
        
        [contenteditable] blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: #6b7280;
        }
        
        [contenteditable] code {
          background-color: #f3f4f6;
          padding: 0.125rem 0.25rem;
          border-radius: 0.25rem;
          font-family: 'Courier New', monospace;
          font-size: 0.875em;
        }
        
        [contenteditable] pre {
          background-color: #f3f4f6;
          padding: 1rem;
          border-radius: 0.5rem;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
        }
      `}</style>
    </div>
  )
}

export default RichTextEditor
