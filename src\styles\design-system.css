/**
 * WebTA LMS Design System
 * Standardized CSS classes for consistent design across the application
 */

/* Typography Scale - Responsive */
.heading-1 {
  @apply text-2xl font-bold text-gray-900 sm:text-3xl lg:text-4xl;
}

.heading-2 {
  @apply text-xl font-semibold text-gray-900 sm:text-2xl lg:text-3xl;
}

.heading-3 {
  @apply text-lg font-semibold text-gray-900 sm:text-xl lg:text-2xl;
}

.heading-4 {
  @apply text-base font-semibold text-gray-900 sm:text-lg lg:text-xl;
}

.body-large {
  @apply text-base text-gray-700 sm:text-lg;
}

.body-medium {
  @apply text-sm text-gray-700 sm:text-base;
}

.body-small {
  @apply text-xs text-gray-600 sm:text-sm;
}

.caption {
  @apply text-xs text-gray-500;
}

/* Container System - Responsive */
.container-sm {
  @apply max-w-sm mx-auto px-4 sm:px-6;
}

.container-md {
  @apply max-w-md mx-auto px-4 sm:px-6;
}

.container-lg {
  @apply max-w-lg mx-auto px-4 sm:px-6;
}

.container-xl {
  @apply max-w-xl mx-auto px-4 sm:px-6;
}

.container-2xl {
  @apply max-w-2xl mx-auto px-4 sm:px-6;
}

.container-3xl {
  @apply max-w-3xl mx-auto px-4 sm:px-6;
}

.container-4xl {
  @apply max-w-4xl mx-auto px-4 sm:px-6;
}

.container-5xl {
  @apply max-w-5xl mx-auto px-4 sm:px-6;
}

.container-6xl {
  @apply max-w-6xl mx-auto px-4 sm:px-6;
}

.container-7xl {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-full {
  @apply w-full px-4 sm:px-6 lg:px-8;
}

/* Spacing System - Responsive */
.spacing-xs {
  @apply space-y-2 sm:space-y-3;
}

.spacing-sm {
  @apply space-y-3 sm:space-y-4;
}

.spacing-md {
  @apply space-y-4 sm:space-y-6;
}

.spacing-lg {
  @apply space-y-6 sm:space-y-8;
}

.spacing-xl {
  @apply space-y-8 sm:space-y-10;
}

.spacing-2xl {
  @apply space-y-10 sm:space-y-12;
}

/* Grid System - Responsive */
.grid-responsive-1 {
  @apply grid grid-cols-1;
}

.grid-responsive-2 {
  @apply grid grid-cols-1 sm:grid-cols-2;
}

.grid-responsive-3 {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
}

.grid-responsive-4 {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.grid-responsive-6 {
  @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6;
}

/* Card System - Responsive */
.card-base {
  @apply bg-white rounded-lg border shadow-sm;
}

.card-elevated {
  @apply bg-white rounded-lg border shadow-md;
}

.card-interactive {
  @apply bg-white rounded-lg border shadow-sm hover:shadow-md transition-shadow cursor-pointer;
}

.card-padding-sm {
  @apply p-3 sm:p-4;
}

.card-padding-md {
  @apply p-4 sm:p-6;
}

.card-padding-lg {
  @apply p-6 sm:p-8;
}

/* Button System - Responsive */
.btn-responsive {
  @apply px-3 py-2 text-sm sm:px-4 sm:py-2 sm:text-base;
}

.btn-responsive-lg {
  @apply px-4 py-2 text-base sm:px-6 sm:py-3 sm:text-lg;
}

/* Navigation - Responsive */
.nav-mobile-hidden {
  @apply hidden md:flex;
}

.nav-mobile-only {
  @apply flex md:hidden;
}

.nav-responsive {
  @apply flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-4;
}

/* Layout Utilities - Responsive */
.section-padding {
  @apply py-8 sm:py-12 lg:py-16;
}

.section-padding-sm {
  @apply py-4 sm:py-6 lg:py-8;
}

.section-padding-lg {
  @apply py-12 sm:py-16 lg:py-20;
}

.content-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

/* Visibility Utilities - Responsive */
.mobile-only {
  @apply block sm:hidden;
}

.tablet-up {
  @apply hidden sm:block;
}

.desktop-only {
  @apply hidden lg:block;
}

.mobile-tablet {
  @apply block lg:hidden;
}

/* Flex Utilities - Responsive */
.flex-responsive {
  @apply flex flex-col sm:flex-row;
}

.flex-responsive-reverse {
  @apply flex flex-col-reverse sm:flex-row;
}

.flex-center-responsive {
  @apply flex flex-col items-center sm:flex-row sm:justify-between;
}

/* Text Alignment - Responsive */
.text-responsive-center {
  @apply text-center sm:text-left;
}

.text-responsive-left {
  @apply text-left sm:text-center lg:text-left;
}

/* Image Utilities - Responsive */
.img-responsive {
  @apply w-full h-auto object-cover;
}

.img-responsive-square {
  @apply w-full aspect-square object-cover;
}

.img-responsive-video {
  @apply w-full aspect-video object-cover;
}

/* Form Utilities - Responsive */
.form-responsive {
  @apply space-y-4 sm:space-y-6;
}

.form-grid-responsive {
  @apply grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6;
}

.input-responsive {
  @apply w-full px-3 py-2 text-sm sm:px-4 sm:py-2 sm:text-base;
}

/* Dashboard Specific - Responsive */
.dashboard-grid {
  @apply grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 sm:gap-6;
}

.dashboard-stats {
  @apply grid grid-cols-2 gap-4 sm:grid-cols-4 sm:gap-6;
}

.dashboard-content {
  @apply space-y-6 sm:space-y-8;
}

/* Course Layout - Responsive */
.course-grid {
  @apply grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.course-detail-layout {
  @apply grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-12;
}

.course-content {
  @apply lg:col-span-2;
}

.course-sidebar {
  @apply lg:col-span-1;
}

/* Animation Utilities */
.animate-fade-in {
  @apply opacity-0 animate-pulse;
  animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.animate-slide-up {
  @apply transform translate-y-4 opacity-0;
  animation: slideUp 0.5s ease-out forwards;
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Focus States - Accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-inset;
}

/* Print Utilities */
@media print {
  .print-hidden {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .card-base,
  .card-elevated,
  .card-interactive {
    @apply border-2 border-gray-900;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up {
    animation: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}
