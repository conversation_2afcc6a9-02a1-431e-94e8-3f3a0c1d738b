(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65],{7012:function(e,s,t){Promise.resolve().then(t.bind(t,1346))},1346:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return u}});var a=t(7437),l=t(2265),n=t(9376),i=t(7648),r=t(3145),c=t(6334),d=t(2827),o=t(757),h=t(8711),x=t(8629),m=t(1235);function u(){let e=(0,n.useRouter)(),s=(0,n.useSearchParams)(),[t,u]=(0,l.useState)([]),[g,p]=(0,l.useState)(!0),[j,v]=(0,l.useState)(null),[b,f]=(0,l.useState)(null),[N,y]=(0,l.useState)(s.get("search")||""),[w,k]=(0,l.useState)(s.get("category")||""),[S,C]=(0,l.useState)(s.get("level")||""),[D,T]=(0,l.useState)(s.get("language")||""),[P,A]=(0,l.useState)(s.get("sortBy")||"createdAt"),[z,R]=(0,l.useState)(s.get("sortOrder")||"desc"),[_,E]=(0,l.useState)(parseInt(s.get("page")||"1")),I=async()=>{try{p(!0),v(null);let e=new URLSearchParams({page:_.toString(),limit:"12",sortBy:P,sortOrder:z});N&&e.append("search",N),w&&e.append("category",w),S&&e.append("level",S),D&&e.append("language",D);let s=await fetch("/api/courses?".concat(e)),t=await s.json();t.success?(u(t.data.courses),f(t.data.pagination)):v(t.error||"Lỗi khi tải danh s\xe1ch kh\xf3a học")}catch(e){v("Lỗi kết nối server")}finally{p(!1)}},B=()=>{let s=new URLSearchParams;N&&s.append("search",N),w&&s.append("category",w),S&&s.append("level",S),D&&s.append("language",D),"createdAt"!==P&&s.append("sortBy",P),"desc"!==z&&s.append("sortOrder",z),1!==_&&s.append("page",_.toString());let t=s.toString()?"/courses?".concat(s):"/courses";e.push(t,{scroll:!1})};(0,l.useEffect)(()=>{I()},[_,P,z]),(0,l.useEffect)(()=>{B()},[N,w,S,D,P,z,_]);let L=()=>{E(1),I()},O=(e,s)=>0===e?"Miễn ph\xed":new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"===s?"VND":"USD"}).format(e),F=(e,s)=>0===s?"Chưa c\xf3 đ\xe1nh gi\xe1":"".concat(e.toFixed(1)," ⭐ (").concat(s," đ\xe1nh gi\xe1)");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Kh\xf3a học"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Kh\xe1m ph\xe1 c\xe1c kh\xf3a học ngoại ngữ chất lượng cao"})]})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"lg:w-64 flex-shrink-0",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 sticky top-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Bộ lọc"}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),E(1),I()},className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xecm kiếm"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(d.I,{type:"text",placeholder:"T\xecm kh\xf3a học...",value:N,onChange:e=>y(e.target.value)}),(0,a.jsx)(c.z,{type:"submit",size:"sm",children:"T\xecm"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Danh mục"}),(0,a.jsxs)("select",{value:w,onChange:e=>{k(e.target.value),L()},className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Tất cả danh mục"}),(0,a.jsx)("option",{value:"english",children:"Tiếng Anh"}),(0,a.jsx)("option",{value:"chinese",children:"Tiếng Trung"}),(0,a.jsx)("option",{value:"japanese",children:"Tiếng Nhật"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tr\xecnh độ"}),(0,a.jsxs)("select",{value:S,onChange:e=>{C(e.target.value),L()},className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Tất cả tr\xecnh độ"}),(0,a.jsx)("option",{value:"beginner",children:"Cơ bản"}),(0,a.jsx)("option",{value:"intermediate",children:"Trung cấp"}),(0,a.jsx)("option",{value:"advanced",children:"N\xe2ng cao"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xf4n ngữ"}),(0,a.jsxs)("select",{value:D,onChange:e=>{T(e.target.value),L()},className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Tất cả ng\xf4n ngữ"}),(0,a.jsx)("option",{value:"english",children:"Tiếng Anh"}),(0,a.jsx)("option",{value:"chinese",children:"Tiếng Trung"}),(0,a.jsx)("option",{value:"japanese",children:"Tiếng Nhật"})]})]})]})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("div",{className:"text-gray-600",children:b&&(0,a.jsxs)("span",{children:["Hiển thị ",(b.currentPage-1)*b.limit+1,"-",Math.min(b.currentPage*b.limit,b.totalCount),"trong tổng số ",b.totalCount," kh\xf3a học"]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("label",{className:"text-sm text-gray-700",children:"Sắp xếp:"}),(0,a.jsxs)("select",{value:"".concat(P,"-").concat(z),onChange:e=>{let[s,t]=e.target.value.split("-");A(s),R(t)},className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[(0,a.jsx)("option",{value:"createdAt-desc",children:"Mới nhất"}),(0,a.jsx)("option",{value:"createdAt-asc",children:"Cũ nhất"}),(0,a.jsx)("option",{value:"stats.totalStudents-desc",children:"Nhiều học vi\xean nhất"}),(0,a.jsx)("option",{value:"stats.averageRating-desc",children:"Đ\xe1nh gi\xe1 cao nhất"}),(0,a.jsx)("option",{value:"pricing.basePrice-asc",children:"Gi\xe1 thấp nhất"}),(0,a.jsx)("option",{value:"pricing.basePrice-desc",children:"Gi\xe1 cao nhất"})]})]})]}),j&&(0,a.jsx)(x.g7,{variant:"error",message:j,className:"mb-6"}),g&&(0,a.jsx)(m.h0,{}),!g&&t.length>0&&(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8",children:t.map(e=>(0,a.jsx)(o.Zb,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:(0,a.jsxs)(i.default,{href:"/courses/".concat(e.slug),children:[(0,a.jsx)("div",{className:"aspect-video bg-gray-200 relative",children:e.thumbnail?(0,a.jsx)(r.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-400",children:"\uD83D\uDCDA"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(h.C,{variant:"secondary",size:"sm",children:e.category.name}),(0,a.jsx)(h.C,{variant:"outline",size:"sm",children:e.level})]}),(0,a.jsx)("h3",{className:"font-semibold text-lg mb-2 line-clamp-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3 text-sm text-gray-500",children:[(0,a.jsx)("span",{children:"\uD83D\uDC68‍\uD83C\uDFEB"}),(0,a.jsxs)("span",{children:[e.instructor.profile.firstName," ",e.instructor.profile.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold text-primary",children:O(e.pricing.basePrice,e.pricing.currency)}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:F(e.stats.averageRating,e.stats.totalRatings)})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.stats.totalStudents," học vi\xean"]})]})]})]})},e._id))}),!g&&0===t.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDA"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Kh\xf4ng t\xecm thấy kh\xf3a học n\xe0o"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Thử thay đổi bộ lọc hoặc từ kh\xf3a t\xecm kiếm"}),(0,a.jsx)(c.z,{onClick:()=>{y(""),k(""),C(""),T(""),E(1),I()},children:"X\xf3a bộ lọc"})]}),b&&b.totalPages>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center gap-2",children:[(0,a.jsx)(c.z,{variant:"outline",disabled:!b.hasPrevPage,onClick:()=>E(_-1),children:"Trước"}),(0,a.jsxs)("span",{className:"px-4 py-2 text-sm text-gray-600",children:["Trang ",b.currentPage," / ",b.totalPages]}),(0,a.jsx)(c.z,{variant:"outline",disabled:!b.hasNextPage,onClick:()=>E(_+1),children:"Sau"})]})]})]})})]})}},2827:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(7437),l=t(2265),n=t(3448);let i=l.forwardRef((e,s)=>{let{className:t,type:l,...i}=e;return(0,a.jsx)("input",{type:l,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"}},function(e){e.O(0,[851,648,785,777,971,117,744],function(){return e(e.s=7012)}),_N_E=e.O()}]);