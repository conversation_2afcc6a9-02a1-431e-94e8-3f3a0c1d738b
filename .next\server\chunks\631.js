"use strict";exports.id=631,exports.ids=[631],exports.modules={8555:(e,s,r)=>{r.d(s,{g7:()=>x});var a=r(10326),n=r(17577),t=r(79360),l=r(51223);let d=(0,t.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),i=n.forwardRef(({className:e,variant:s,...r},n)=>a.jsx("div",{ref:n,role:"alert",className:(0,l.cn)(d({variant:s}),e),...r}));i.displayName="Alert";let c=n.forwardRef(({className:e,...s},r)=>a.jsx("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...s}));c.displayName="AlertTitle";let o=n.forwardRef(({className:e,...s},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...s}));o.displayName="AlertDescription";let m={success:a.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:a.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:a.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:a.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function x({title:e,message:s,variant:r="info",onClose:n}){return(0,a.jsxs)(i,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[r],className:"relative",children:[m[r],(0,a.jsxs)("div",{className:"flex-1",children:[e&&a.jsx(c,{children:e}),a.jsx(o,{children:s})]}),n&&a.jsx("button",{onClick:n,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:a.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},36792:(e,s,r)=>{r.d(s,{C:()=>d});var a=r(10326);r(17577);var n=r(79360),t=r(51223);let l=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...r}){return a.jsx("div",{className:(0,t.cn)(l({variant:s}),e),...r})}},47375:(e,s,r)=>{r.d(s,{Zb:()=>i});var a=r(10326),n=r(17577),t=r(79360),l=r(51223);let d=(0,t.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),i=n.forwardRef(({className:e,variant:s,size:r,...n},t)=>a.jsx("div",{ref:t,className:(0,l.cn)(d({variant:s,size:r}),e),...n}));i.displayName="Card",n.forwardRef(({className:e,...s},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",n.forwardRef(({className:e,...s},r)=>a.jsx("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",n.forwardRef(({className:e,...s},r)=>a.jsx("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription",n.forwardRef(({className:e,...s},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s})).displayName="CardContent",n.forwardRef(({className:e,...s},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},17334:(e,s,r)=>{r.d(s,{h0:()=>x,t6:()=>h});var a=r(10326),n=r(51223);function t({className:e}){return a.jsx("div",{className:(0,n.cn)("animate-pulse rounded-md bg-gray-200",e)})}function l({lines:e=1,className:s}){return a.jsx("div",{className:(0,n.cn)("space-y-2",s),children:Array.from({length:e}).map((s,r)=>a.jsx(t,{className:(0,n.cn)("h-4",r===e-1?"w-3/4":"w-full")},r))})}function d({className:e}){return(0,a.jsxs)("div",{className:(0,n.cn)("card-base overflow-hidden",e),children:[a.jsx(t,{className:"aspect-video w-full"}),(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[a.jsx(t,{className:"h-6 w-full"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(t,{className:"h-4 w-4 rounded-full"}),a.jsx(t,{className:"h-4 w-24"})]}),a.jsx(l,{lines:2}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(t,{className:"h-4 w-16"}),a.jsx(t,{className:"h-4 w-12"})]}),a.jsx(t,{className:"h-6 w-20"})]})]})}function i({className:e}){return a.jsx("div",{className:(0,n.cn)("dashboard-stats",e),children:Array.from({length:4}).map((e,s)=>a.jsx("div",{className:"card-base card-padding-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(t,{className:"h-4 w-16"}),a.jsx(t,{className:"h-8 w-12"})]}),a.jsx(t,{className:"h-8 w-8 rounded-full"})]})},s))})}function c({rows:e=5,cols:s=4,className:r}){return(0,a.jsxs)("div",{className:(0,n.cn)("space-y-4",r),children:[a.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((e,s)=>a.jsx(t,{className:"h-6 w-full"},s))}),Array.from({length:e}).map((e,r)=>a.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((e,s)=>a.jsx(t,{className:"h-4 w-full"},s))},r))]})}function o({className:e}){return(0,a.jsxs)("div",{className:(0,n.cn)("card-base card-padding-md space-y-4",e),children:[a.jsx(t,{className:"h-6 w-48"}),a.jsx("div",{className:"space-y-2",children:Array.from({length:6}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx(t,{className:"h-4 w-12"}),a.jsx(t,{className:"h-6",style:{width:`${60*Math.random()+20}%`}})]},s))}),a.jsx("div",{className:"flex justify-between",children:Array.from({length:7}).map((e,s)=>a.jsx(t,{className:"h-4 w-8"},s))})]})}function m({items:e=5,className:s}){return a.jsx("div",{className:(0,n.cn)("space-y-4",s),children:Array.from({length:e}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[a.jsx(t,{className:"h-12 w-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[a.jsx(t,{className:"h-5 w-3/4"}),a.jsx(t,{className:"h-4 w-1/2"})]}),a.jsx(t,{className:"h-8 w-20"})]},s))})}function x({className:e}){return(0,a.jsxs)("div",{className:(0,n.cn)("container-7xl section-padding",e),children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx(t,{className:"h-12 w-96 mx-auto mb-4"}),a.jsx(t,{className:"h-6 w-128 mx-auto"})]}),a.jsx("div",{className:"flex gap-4 mb-8",children:Array.from({length:4}).map((e,s)=>a.jsx(t,{className:"h-10 w-24"},s))}),a.jsx("div",{className:"course-grid",children:Array.from({length:8}).map((e,s)=>a.jsx(d,{},s))})]})}function h({className:e}){return(0,a.jsxs)("div",{className:(0,n.cn)("container-7xl section-padding",e),children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx(t,{className:"h-10 w-64 mb-2"}),a.jsx(t,{className:"h-6 w-48"})]}),a.jsx(i,{className:"mb-8"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[a.jsx(o,{}),a.jsx(c,{})]}),a.jsx("div",{className:"space-y-6",children:a.jsx(m,{})})]})]})}}};