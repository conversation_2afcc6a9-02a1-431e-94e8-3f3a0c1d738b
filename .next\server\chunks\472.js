exports.id=472,exports.ids=[472],exports.modules={81421:(e,t,r)=>{"use strict";var o=r(31660),n=r(34006),i=r(43135),a=r(53659);e.exports=a||o.call(i,n)},34006:e=>{"use strict";e.exports=Function.prototype.apply},43135:e=>{"use strict";e.exports=Function.prototype.call},602:(e,t,r)=>{"use strict";var o=r(31660),n=r(17445),i=r(43135),a=r(81421);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},53659:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},98363:(e,t,r)=>{"use strict";var o=r(2749),n=r(602),i=n([o("%String.prototype.indexOf%")]);e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n([r]):r}},62344:(e,t,r)=>{"use strict";var o,n=r(602),i=r(86737);try{o=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!o&&i&&i(Object.prototype,"__proto__"),s=Object,l=s.getPrototypeOf;e.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof l&&function(e){return l(null==e?e:s(e))}},70091:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},86827:e=>{"use strict";e.exports=EvalError},56718:e=>{"use strict";e.exports=Error},37388:e=>{"use strict";e.exports=RangeError},63684:e=>{"use strict";e.exports=ReferenceError},31209:e=>{"use strict";e.exports=SyntaxError},17445:e=>{"use strict";e.exports=TypeError},76928:e=>{"use strict";e.exports=URIError},15678:e=>{"use strict";e.exports=Object},49214:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],d=0;d<u;d++)c[d]="$"+d;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var h=function(){};h.prototype=s.prototype,a.prototype=new h,h.prototype=null}return a}},31660:(e,t,r)=>{"use strict";var o=r(49214);e.exports=Function.prototype.bind||o},2749:(e,t,r)=>{"use strict";var o,n=r(15678),i=r(56718),a=r(86827),s=r(37388),l=r(63684),u=r(31209),c=r(17445),d=r(76928),h=r(95175),p=r(22334),m=r(46082),f=r(40430),y=r(24210),v=r(70792),P=r(92615),g=Function,T=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},_=r(86737),E=r(70091),S=function(){throw new c},b=_?function(){try{return arguments.callee,S}catch(e){try{return _(arguments,"callee").get}catch(e){return S}}}():S,O=r(91976)(),x=r(93941),w=r(67209),A=r(32395),R=r(34006),G=r(43135),C={},I="undefined"!=typeof Uint8Array&&x?x(Uint8Array):o,k={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":O&&x?x([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":C,"%AsyncGenerator%":C,"%AsyncGeneratorFunction%":C,"%AsyncIteratorPrototype%":C,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":C,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":O&&x?x(x([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&O&&x?x(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":_,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":s,"%ReferenceError%":l,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&O&&x?x(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":O&&x?x(""[Symbol.iterator]()):o,"%Symbol%":O?Symbol:o,"%SyntaxError%":u,"%ThrowTypeError%":b,"%TypedArray%":I,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":d,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":G,"%Function.prototype.apply%":R,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":w,"%Math.abs%":h,"%Math.floor%":p,"%Math.max%":m,"%Math.min%":f,"%Math.pow%":y,"%Math.round%":v,"%Math.sign%":P,"%Reflect.getPrototypeOf%":A};if(x)try{null.error}catch(e){var j=x(x(e));k["%Error.prototype%"]=j}var D=function e(t){var r;if("%AsyncFunction%"===t)r=T("async function () {}");else if("%GeneratorFunction%"===t)r=T("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=T("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&x&&(r=x(n.prototype))}return k[t]=r,r},N={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=r(31660),F=r(64995),U=M.call(G,Array.prototype.concat),q=M.call(R,Array.prototype.splice),L=M.call(G,String.prototype.replace),H=M.call(G,String.prototype.slice),$=M.call(G,RegExp.prototype.exec),B=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,z=/\\(\\)?/g,W=function(e){var t=H(e,0,1),r=H(e,-1);if("%"===t&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var o=[];return L(e,B,function(e,t,r,n){o[o.length]=r?L(n,z,"$1"):t||e}),o},K=function(e,t){var r,o=e;if(F(N,o)&&(o="%"+(r=N[o])[0]+"%"),F(k,o)){var n=k[o];if(n===C&&(n=D(o)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===$(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=W(e),o=r.length>0?r[0]:"",n=K("%"+o+"%",t),i=n.name,a=n.value,s=!1,l=n.alias;l&&(o=l[0],q(r,U([0,1],l)));for(var d=1,h=!0;d<r.length;d+=1){var p=r[d],m=H(p,0,1),f=H(p,-1);if(('"'===m||"'"===m||"`"===m||'"'===f||"'"===f||"`"===f)&&m!==f)throw new u("property names with quotes must have matching quotes");if("constructor"!==p&&h||(s=!0),o+="."+p,F(k,i="%"+o+"%"))a=k[i];else if(null!=a){if(!(p in a)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(_&&d+1>=r.length){var y=_(a,p);a=(h=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[p]}else h=F(a,p),a=a[p];h&&!s&&(k[i]=a)}}return a}},67209:(e,t,r)=>{"use strict";var o=r(15678);e.exports=o.getPrototypeOf||null},32395:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},93941:(e,t,r)=>{"use strict";var o=r(32395),n=r(67209),i=r(62344);e.exports=o?function(e){return o(e)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return n(e)}:i?function(e){return i(e)}:null},62980:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},86737:(e,t,r)=>{"use strict";var o=r(62980);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},91976:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(12522);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},12522:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},64995:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,i=r(31660);e.exports=i.call(o,n)},95175:e=>{"use strict";e.exports=Math.abs},22334:e=>{"use strict";e.exports=Math.floor},61781:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},46082:e=>{"use strict";e.exports=Math.max},40430:e=>{"use strict";e.exports=Math.min},24210:e=>{"use strict";e.exports=Math.pow},70792:e=>{"use strict";e.exports=Math.round},92615:(e,t,r)=>{"use strict";var o=r(61781);e.exports=function(e){return o(e)||0===e?e:e<0?-1:1}},19966:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,h="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,m=Boolean.prototype.valueOf,f=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,E=RegExp.prototype.test,S=Array.prototype.concat,b=Array.prototype.join,O=Array.prototype.slice,x=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,G="function"==typeof Symbol&&"object"==typeof Symbol.iterator,C="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===G?"object":"symbol")?Symbol.toStringTag:null,I=Object.prototype.propertyIsEnumerable,k=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function j(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-x(-e):x(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var D=r(71660),N=D.custom,M=B(N)?N:null,F={__proto__:null,double:'"',single:"'"},U={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function q(e,t,r){var o=F[r.quoteStyle||t];return o+e+o}function L(e){return!C||!("object"==typeof e&&(C in e||void 0!==e[C]))}function H(e){return"[object Array]"===K(e)&&L(e)}function $(e){return"[object RegExp]"===K(e)&&L(e)}function B(e){if(G)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!R)return!1;try{return R.call(e),!0}catch(e){}return!1}e.exports=function e(t,r,o,n){var s=r||{};if(W(s,"quoteStyle")&&!W(F,s.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(W(s,"maxStringLength")&&("number"==typeof s.maxStringLength?s.maxStringLength<0&&s.maxStringLength!==1/0:null!==s.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=!W(s,"customInspect")||s.customInspect;if("boolean"!=typeof l&&"symbol"!==l)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(W(s,"indent")&&null!==s.indent&&"	"!==s.indent&&!(parseInt(s.indent,10)===s.indent&&s.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(W(s,"numericSeparator")&&"boolean"!=typeof s.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var f=s.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+"... "+o+" more character"+(o>1?"s":"")}var n=U[r.quoteStyle||"single"];return n.lastIndex=0,q(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,J),"single",r)}(t,s);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var T=String(t);return f?j(t,T):T}if("bigint"==typeof t){var E=String(t)+"n";return f?j(t,E):E}var x=void 0===s.depth?5:s.depth;if(void 0===o&&(o=0),o>=x&&x>0&&"object"==typeof t)return H(t)?"[Array]":"[Object]";var A=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=b.call(Array(e.indent+1)," ")}return{base:r,prev:b.call(Array(t+1),r)}}(s,o);if(void 0===n)n=[];else if(V(n,t)>=0)return"[Circular]";function N(t,r,i){if(r&&(n=O.call(n)).push(r),i){var a={depth:s.depth};return W(s,"quoteStyle")&&(a.quoteStyle=s.quoteStyle),e(t,a,o+1,n)}return e(t,s,o+1,n)}if("function"==typeof t&&!$(t)){var z=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),et=ee(t,N);return"[Function"+(z?": "+z:" (anonymous)")+"]"+(et.length>0?" { "+b.call(et,", ")+" }":"")}if(B(t)){var er=G?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):R.call(t);return"object"!=typeof t||G?er:Q(er)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var eo,en="<"+_.call(String(t.nodeName)),ei=t.attributes||[],ea=0;ea<ei.length;ea++)en+=" "+ei[ea].name+"="+q((eo=ei[ea].value,g.call(String(eo),/"/g,"&quot;")),"double",s);return en+=">",t.childNodes&&t.childNodes.length&&(en+="..."),en+="</"+_.call(String(t.nodeName))+">"}if(H(t)){if(0===t.length)return"[]";var es=ee(t,N);return A&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(es)?"["+Z(es,A)+"]":"[ "+b.call(es,", ")+" ]"}if("[object Error]"===K(t)&&L(t)){var el=ee(t,N);return"cause"in Error.prototype||!("cause"in t)||I.call(t,"cause")?0===el.length?"["+String(t)+"]":"{ ["+String(t)+"] "+b.call(el,", ")+" }":"{ ["+String(t)+"] "+b.call(S.call("[cause]: "+N(t.cause),el),", ")+" }"}if("object"==typeof t&&l){if(M&&"function"==typeof t[M]&&D)return D(t,{depth:x-o});if("symbol"!==l&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var eu=[];return a&&a.call(t,function(e,r){eu.push(N(r,t,!0)+" => "+N(e,t))}),Y("Map",i.call(t),eu,A)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ec=[];return c&&c.call(t,function(e){ec.push(N(e,t))}),Y("Set",u.call(t),ec,A)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{h.call(e,h)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return X("WeakMap");if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{h.call(e,h);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return X("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return X("WeakRef");if("[object Number]"===K(t)&&L(t))return Q(N(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return Q(N(w.call(t)));if("[object Boolean]"===K(t)&&L(t))return Q(m.call(t));if("[object String]"===K(t)&&L(t))return Q(N(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||"undefined"!=typeof global&&t===global)return"{ [object globalThis] }";if(!("[object Date]"===K(t)&&L(t))&&!$(t)){var ed=ee(t,N),eh=k?k(t)===Object.prototype:t instanceof Object||t.constructor===Object,ep=t instanceof Object?"":"null prototype",em=!eh&&C&&Object(t)===t&&C in t?P.call(K(t),8,-1):ep?"Object":"",ef=(eh||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(em||ep?"["+b.call(S.call([],em||[],ep||[]),": ")+"] ":"");return 0===ed.length?ef+"{}":A?ef+"{"+Z(ed,A)+"}":ef+"{ "+b.call(ed,", ")+" }"}return String(t)};var z=Object.prototype.hasOwnProperty||function(e){return e in this};function W(e,t){return z.call(e,t)}function K(e){return f.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function J(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+T.call(t.toString(16))}function Q(e){return"Object("+e+")"}function X(e){return e+" { ? }"}function Y(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):b.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+b.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r,o=H(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=W(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(G){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)W(e,l)&&(!o||String(Number(l))!==l||!(l<e.length))&&(G&&r["$"+l]instanceof Symbol||(E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof A)for(var u=0;u<a.length;u++)I.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},71660:(e,t,r)=>{e.exports=r(21764).inspect},26815:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:o.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},6684:(e,t,r)=>{"use strict";var o=r(52503),n=r(23273),i=r(26815);e.exports={formats:i,parse:n,stringify:o}},23273:(e,t,r)=>{"use strict";var o=r(31847),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},s=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,t.throwOnLimitExceeded?u+1:u);if(t.throwOnLimitExceeded&&c.length>u)throw RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var d=-1,h=t.charset;if(t.charsetSentinel)for(p=0;p<c.length;++p)0===c[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[p]?h="utf-8":"utf8=%26%2310003%3B"===c[p]&&(h="iso-8859-1"),d=p,p=c.length);for(p=0;p<c.length;++p)if(p!==d){var p,m,f,y=c[p],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(m=t.decoder(y,a.decoder,h,"key"),f=t.strictNullHandling?null:""):(m=t.decoder(y.slice(0,P),a.decoder,h,"key"),f=o.maybeMap(s(y.slice(P+1),t,i(r[m])?r[m].length:0),function(e){return t.decoder(e,a.decoder,h,"value")})),f&&t.interpretNumericEntities&&"iso-8859-1"===h&&(f=String(f).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(f=i(f)?[f]:f);var g=n.call(r,m);g&&"combine"===t.duplicates?r[m]=o.combine(r[m],f):g&&"last"!==t.duplicates||(r[m]=f)}return r},u=function(e,t,r,n){var i=0;if(e.length>0&&"[]"===e[e.length-1]){var a=e.slice(0,-1).join("");i=Array.isArray(t)&&t[a]?t[a].length:0}for(var l=n?t:s(t,r,i),u=e.length-1;u>=0;--u){var c,d=e[u];if("[]"===d&&r.parseArrays)c=r.allowEmptyArrays&&(""===l||r.strictNullHandling&&null===l)?[]:o.combine([],l);else{c=r.plainObjects?{__proto__:null}:{};var h="["===d.charAt(0)&&"]"===d.charAt(d.length-1)?d.slice(1,-1):d,p=r.decodeDotInKeys?h.replace(/%2E/g,"."):h,m=parseInt(p,10);r.parseArrays||""!==p?!isNaN(m)&&d!==p&&String(m)===p&&m>=0&&r.parseArrays&&m<=r.arrayLimit?(c=[])[m]=l:"__proto__"!==p&&(c[p]=l):c={0:l}}l=c}return l},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var d=0;r.depth>0&&null!==(s=a.exec(i))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},d=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}};e.exports=function(e,t){var r=d(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],h=c(u,n[u],r,"string"==typeof e);i=o.merge(i,h,r)}return!0===r.allowSparse?i:o.compact(i)}},52503:(e,t,r)=>{"use strict";var o=r(49211),n=r(31847),i=r(26815),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},d=Date.prototype.toISOString,h=i.default,p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:h,formatter:i.formatters[h],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},m={},f=function e(t,r,i,a,s,u,d,h,f,y,v,P,g,T,_,E,S,b){for(var O,x,w=t,A=b,R=0,G=!1;void 0!==(A=A.get(m))&&!G;){var C=A.get(t);if(R+=1,void 0!==C){if(C===R)throw RangeError("Cyclic object value");G=!0}void 0===A.get(m)&&(R=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return f&&!E?f(r,p.encoder,S,"key",T):r;w=""}if("string"==typeof(O=w)||"number"==typeof O||"boolean"==typeof O||"symbol"==typeof O||"bigint"==typeof O||n.isBuffer(w))return f?[_(E?r:f(r,p.encoder,S,"key",T))+"="+_(f(w,p.encoder,S,"value",T))]:[_(r)+"="+_(String(w))];var I=[];if(void 0===w)return I;if("comma"===i&&l(w))E&&f&&(w=n.maybeMap(w,f)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))x=y;else{var k=Object.keys(w);x=v?k.sort(v):k}var j=h?String(r).replace(/\./g,"%2E"):String(r),D=a&&l(w)&&1===w.length?j+"[]":j;if(s&&l(w)&&0===w.length)return D+"[]";for(var N=0;N<x.length;++N){var M=x[N],F="object"==typeof M&&M&&void 0!==M.value?M.value:w[M];if(!d||null!==F){var U=P&&h?String(M).replace(/\./g,"%2E"):String(M),q=l(w)?"function"==typeof i?i(D,U):D:D+(P?"."+U:"["+U+"]");b.set(t,R);var L=o();L.set(m,b),c(I,e(F,q,i,a,s,u,d,h,"comma"===i&&E&&l(w)?null:f,y,v,P,g,T,_,E,S,L))}}return I},y=function(e){if(!e)return p;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=p.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||p.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}};e.exports=function(e,t){var r,n=e,i=y(t);"function"==typeof i.filter?n=(0,i.filter)("",n):l(i.filter)&&(r=i.filter);var a=[];if("object"!=typeof n||null===n)return"";var u=s[i.arrayFormat],d="comma"===u&&i.commaRoundTrip;r||(r=Object.keys(n)),i.sort&&r.sort(i.sort);for(var h=o(),p=0;p<r.length;++p){var m=r[p],v=n[m];i.skipNulls&&null===v||c(a,f(v,m,u,d,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,h))}var P=a.join(i.delimiter),g=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),P.length>0?g+P:""}},31847:(e,t,r)=>{"use strict";var o=r(26815),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,d=[],h=0;h<c.length;++h){var p=c.charCodeAt(h);if(45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||i===o.RFC1738&&(40===p||41===p)){d[d.length]=c.charAt(h);continue}if(p<128){d[d.length]=a[p];continue}if(p<2048){d[d.length]=a[192|p>>6]+a[128|63&p];continue}if(p<55296||p>=57344){d[d.length]=a[224|p>>12]+a[128|p>>6&63]+a[128|63&p];continue}h+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(h)),d[d.length]=a[240|p>>18]+a[128|p>>12&63]+a[128|p>>6&63]+a[128|63&p]}l+=d.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},52185:(e,t,r)=>{"use strict";var o=r(19966),n=r(17445),i=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o},a=function(e,t){if(e){var r=i(e,t);return r&&r.value}},s=function(e,t,r){var o=i(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}},l=function(e,t){if(e)return i(e,t,!0)};e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+o(e))},delete:function(t){var r=e&&e.next,o=l(e,t);return o&&r&&r===o&&(e=void 0),!!o},get:function(t){return a(e,t)},has:function(t){var r;return!!(r=e)&&!!i(r,t)},set:function(t,r){e||(e={next:void 0}),s(e,t,r)}};return t}},10417:(e,t,r)=>{"use strict";var o=r(2749),n=r(98363),i=r(19966),a=r(17445),s=o("%Map%",!0),l=n("Map.prototype.get",!0),u=n("Map.prototype.set",!0),c=n("Map.prototype.has",!0),d=n("Map.prototype.delete",!0),h=n("Map.prototype.size",!0);e.exports=!!s&&function(){var e,t={assert:function(e){if(!t.has(e))throw new a("Side channel does not contain "+i(e))},delete:function(t){if(e){var r=d(e,t);return 0===h(e)&&(e=void 0),r}return!1},get:function(t){if(e)return l(e,t)},has:function(t){return!!e&&c(e,t)},set:function(t,r){e||(e=new s),u(e,t,r)}};return t}},33457:(e,t,r)=>{"use strict";var o=r(2749),n=r(98363),i=r(19966),a=r(10417),s=r(17445),l=o("%WeakMap%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),h=n("WeakMap.prototype.delete",!0);e.exports=l?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new s("Side channel does not contain "+i(e))},delete:function(r){if(l&&r&&("object"==typeof r||"function"==typeof r)){if(e)return h(e,r)}else if(a&&t)return t.delete(r);return!1},get:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?u(e,r):t&&t.get(r)},has:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?d(e,r):!!t&&t.has(r)},set:function(r,o){l&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new l),c(e,r,o)):a&&(t||(t=a()),t.set(r,o))}};return r}:a},49211:(e,t,r)=>{"use strict";var o=r(17445),n=r(19966),i=r(52185),a=r(10417),s=r(33457)||a||i;e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+n(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=s()),e.set(t,r)}};return t}},48472:(e,t,r)=>{"use strict";r.d(t,{Z:()=>nu});var o={};r.r(o),r.d(o,{StripeAPIError:()=>C,StripeAuthenticationError:()=>I,StripeCardError:()=>R,StripeConnectionError:()=>D,StripeError:()=>A,StripeIdempotencyError:()=>M,StripeInvalidGrantError:()=>F,StripeInvalidRequestError:()=>G,StripePermissionError:()=>k,StripeRateLimitError:()=>j,StripeSignatureVerificationError:()=>N,StripeUnknownError:()=>U,generate:()=>w});var n={};r.r(n),r.d(n,{Account:()=>ru,AccountLinks:()=>rd,AccountSessions:()=>rp,Accounts:()=>ru,ApplePayDomains:()=>rf,ApplicationFees:()=>rv,Apps:()=>oV,Balance:()=>rg,BalanceTransactions:()=>r_,Billing:()=>oJ,BillingPortal:()=>oQ,Charges:()=>rS,Checkout:()=>oX,Climate:()=>oY,ConfirmationTokens:()=>rO,CountrySpecs:()=>rw,Coupons:()=>rR,CreditNotes:()=>rC,CustomerSessions:()=>rk,Customers:()=>rD,Disputes:()=>rM,Entitlements:()=>oZ,EphemeralKeys:()=>rU,Events:()=>rL,ExchangeRates:()=>r$,FileLinks:()=>rz,Files:()=>rV,FinancialConnections:()=>o1,Forwarding:()=>o0,Identity:()=>o2,InvoiceItems:()=>rQ,Invoices:()=>rY,Issuing:()=>o6,Mandates:()=>r1,OAuth:()=>r6,PaymentIntents:()=>r3,PaymentLinks:()=>r9,PaymentMethodConfigurations:()=>r7,PaymentMethodDomains:()=>ot,PaymentMethods:()=>oo,Payouts:()=>oi,Plans:()=>os,Prices:()=>ou,Products:()=>od,PromotionCodes:()=>op,Quotes:()=>of,Radar:()=>o8,Refunds:()=>ov,Reporting:()=>o3,Reviews:()=>og,SetupAttempts:()=>o_,SetupIntents:()=>oS,ShippingRates:()=>oO,Sigma:()=>o4,Sources:()=>ow,SubscriptionItems:()=>oR,SubscriptionSchedules:()=>oC,Subscriptions:()=>ok,Tax:()=>o9,TaxCodes:()=>oD,TaxIds:()=>oM,TaxRates:()=>oU,Terminal:()=>o5,TestHelpers:()=>o7,Tokens:()=>oL,Topups:()=>o$,Transfers:()=>oz,Treasury:()=>ne,WebhookEndpoints:()=>oK});var i=r(84770),a=r(17702);class s{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}}class l extends Error{}class u extends s{computeHMACSignature(e,t){return i.createHmac("sha256",t).update(e,"utf8").digest("hex")}async computeHMACSignatureAsync(e,t){return await this.computeHMACSignature(e,t)}}var c=r(32615),d=r.t(c,2),h=r(35240),p=r.t(h,2);class m{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(m.TIMEOUT_ERROR_CODE);return e.code=m.TIMEOUT_ERROR_CODE,e}}m.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],m.TIMEOUT_ERROR_CODE="ETIMEDOUT";class f{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}let y=c||d,v=h||p,P=new y.Agent({keepAlive:!0}),g=new v.Agent({keepAlive:!0});class T extends m{constructor(e){super(),this._agent=e}getClientName(){return"node"}makeRequest(e,t,r,o,n,i,a,s){let l="http"===a,u=this._agent;return u||(u=l?P:g),new Promise((a,c)=>{let d=(l?y:v).request({host:e,port:t,path:r,method:o,agent:u,headers:n,ciphers:"DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5"});d.setTimeout(s,()=>{d.destroy(m.makeTimeoutError())}),d.on("response",e=>{a(new _(e))}),d.on("error",e=>{c(e)}),d.once("socket",e=>{e.connecting?e.once(l?"connect":"secureConnect",()=>{d.write(i),d.end()}):(d.write(i),d.end())})})}}class _ extends f{constructor(e){super(e.statusCode,e.headers||{}),this._res=e}getRawResponse(){return this._res}toStream(e){return this._res.once("end",()=>e()),this._res}toJSON(){return new Promise((e,t)=>{let r="";this._res.setEncoding("utf8"),this._res.on("data",e=>{r+=e}),this._res.once("end",()=>{try{e(JSON.parse(r))}catch(e){t(e)}})})}}class E extends m{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=E.makeFetchWithAbortTimeout(e):this._fetchFn=E.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,o)=>{let n;let i=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(m.makeTimeoutError())},o)});return Promise.race([e(t,r),i]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,o)=>{let n=new AbortController,i=setTimeout(()=>{i=null,n.abort(m.makeTimeoutError())},o);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw m.makeTimeoutError();throw e}finally{i&&clearTimeout(i)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,o,n,i,a,s){let l=new URL(r,`${"http"===a?"http":"https"}://${e}`);l.port=t;let u="POST"==o||"PUT"==o||"PATCH"==o;return new S(await this._fetchFn(l.toString(),{method:o,headers:n,body:i||(u?"":void 0)},s))}}class S extends f{constructor(e){super(e.status,S._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class b extends s{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new l("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=O[n[e]];return i.join("")}}let O=Array(256);for(let e=0;e<O.length;e++)O[e]=e.toString(16).padStart(2,"0");class x{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new E(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new b(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}let w=e=>{switch(e.type){case"card_error":return new R(e);case"invalid_request_error":return new G(e);case"api_error":return new C(e);case"authentication_error":return new I(e);case"rate_limit_error":return new j(e);case"idempotency_error":return new M(e);case"invalid_grant":return new F(e);default:return new U(e)}};class A extends Error{constructor(e={},t=null){super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}A.generate=w;class R extends A{constructor(e={}){super(e,"StripeCardError")}}class G extends A{constructor(e={}){super(e,"StripeInvalidRequestError")}}class C extends A{constructor(e={}){super(e,"StripeAPIError")}}class I extends A{constructor(e={}){super(e,"StripeAuthenticationError")}}class k extends A{constructor(e={}){super(e,"StripePermissionError")}}class j extends A{constructor(e={}){super(e,"StripeRateLimitError")}}class D extends A{constructor(e={}){super(e,"StripeConnectionError")}}class N extends A{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class M extends A{constructor(e={}){super(e,"StripeIdempotencyError")}}class F extends A{constructor(e={}){super(e,"StripeInvalidGrantError")}}class U extends A{constructor(e={}){super(e,"StripeUnknownError")}}var q=r(6684);let L=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host"];function H(e){return e&&"object"==typeof e&&L.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function $(e){return q.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString()}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let B=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function z(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!H(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>L.includes(e));return r.length>0&&r.length!==t.length&&V(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function W(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function K(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function V(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function J(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}var Q=r(61282);class X extends A{}class Y extends x{constructor(){super(),this._exec=Q.exec,this._UNAME_CACHE=null}uuid4(){return i.randomUUID?i.randomUUID():super.uuid4()}getUname(){return this._UNAME_CACHE||(this._UNAME_CACHE=new Promise((e,t)=>{try{this._exec("uname -a",(t,r)=>{if(t)return e(null);e(r)})}catch(t){e(null)}})),this._UNAME_CACHE}secureCompare(e,t){if(!e||!t)throw Error("secureCompare must receive two arguments");if(e.length!==t.length)return!1;if(i.timingSafeEqual){let r=new TextEncoder,o=r.encode(e),n=r.encode(t);return i.timingSafeEqual(o,n)}return super.secureCompare(e,t)}createEmitter(){return new a.EventEmitter}tryBufferData(e){if(!(e.file.data instanceof a.EventEmitter))return Promise.resolve(e);let t=[];return new Promise((r,o)=>{e.file.data.on("data",e=>{t.push(e)}).once("end",()=>{let o=Object.assign({},e);o.file.data=function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}(t),r(o)}).on("error",e=>{o(new X({message:"An error occurred while attempting to process the file for upload.",detail:e}))})})}createNodeHttpClient(e){return new T(e)}createDefaultHttpClient(){return new T}createNodeCryptoProvider(){return new u}createDefaultCryptoProvider(){return this.createNodeCryptoProvider()}}function Z(e,t){for(let r in t){let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function ee(e,t){return function(e){return new Z(e,t)}}class et{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=ea(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class er extends et{getNextPage(e){let t=ea(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class eo extends et{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}let en=(e,t,r,o)=>"search"===r.methodType?ei(new eo(o,t,r,e)):"list"===r.methodType?ei(new er(o,t,r,e)):null,ei=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),o=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return K(new Promise((e,o)=>{t().then(function o(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?o({done:!0,value:void 0}):t().then(o))}).catch(o)}),o)}),o={autoPagingEach:r,autoPagingToArray:function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return K(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>o};return o};function ea(e){return!!z([].slice.call(e)).ending_before}function es(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=B(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=B(this.path),this.initialize(...arguments)}es.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},es.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=K(this._makeRequest(t,e,{}),r);return Object.assign(o,en(this,t,e,o)),o}},es.MAX_BUFFERED_REQUEST_METRICS=100,es.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){let o=(t.method||"GET").toUpperCase(),n=t.usage||[],i=t.urlParams||[],a=t.encode||(e=>e),s=!!t.fullPath,l=B(s?t.fullPath:t.path||""),u=s?t.fullPath:this.createResourcePathWithSymbols(t.path),c=[].slice.call(e),d=i.reduce((e,t)=>{let r=c.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${o} ${u}\`)`);return e[t]=r,e},{}),h=a(Object.assign({},z(c),r)),p=function(e){let t={auth:null,host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.auth=e.pop();else if(H(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!L.includes(e));o.length&&V(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.auth=r.apiKey),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host)}}return t}(c),m=p.host||t.host,f=!!t.streaming;if(c.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${c}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${o} \`${u}\`)`);let y=s?l(d):this.createFullPath(l,d),v=Object.assign(p.headers,t.headers);t.validator&&t.validator(h,{headers:v});let P="GET"===t.method||"DELETE"===t.method;return{requestMethod:o,requestPath:y,bodyData:P?{}:h,queryData:P?h:{},auth:p.auth,headers:v,host:null!=m?m:null,streaming:f,settings:p.settings,usage:n}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",$(a.queryData)].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.auth,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let el=es.method,eu=es.extend({retrieve:el({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:el({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:el({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:el({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:el({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:el({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:el({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),ec=es.method,ed=es.extend({retrieve:ec({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:ec({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),eh=es.method,ep=es.extend({create:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),increment:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),reverse:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),em=es.method,ef=es.extend({retrieve:em({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:em({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:em({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:em({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:em({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),ey=es.method,ev=es.extend({create:ey({method:"POST",fullPath:"/v1/tax/calculations"}),listLineItems:ey({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),eP=es.method,eg=es.extend({create:eP({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:eP({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:eP({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:eP({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eT=es.method,e_=es.extend({deliverCard:eT({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eT({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eT({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eT({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"})}),eE=es.method,eS=es.extend({create:eE({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:eE({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:eE({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:eE({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),eb=es.method,eO=es.extend({create:eb({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:eb({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:eb({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:eb({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),ex=es.method,ew=es.extend({create:ex({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:ex({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:ex({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:ex({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:ex({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),eA=es.method,eR=es.extend({create:eA({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),eG=es.method,eC=es.extend({create:eG({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),eI=es.method,ek=es.extend({create:eI({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:eI({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:eI({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),ej=es.method,eD=es.extend({fundCashBalance:ej({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),eN=es.method,eM=es.extend({create:eN({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:eN({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:eN({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),eF=es.method,eU=es.extend({create:eF({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:eF({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:eF({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:eF({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:eF({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),eq=es.method,eL=es.extend({retrieve:eq({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:eq({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),eH=es.method,e$=es.extend({create:eH({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:eH({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:eH({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:eH({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),eB=es.method,ez=es.extend({create:eB({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:eB({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:eB({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:eB({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),retrieveFeatures:eB({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:eB({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),eW=es.method,eK=es.extend({fail:eW({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:eW({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:eW({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),eV=es.method,eJ=es.extend({create:eV({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:eV({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:eV({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:eV({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),eQ=es.method,eX=es.extend({create:eQ({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:eQ({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:eQ({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:eQ({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:eQ({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),eY=es.method,eZ=es.extend({create:eY({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),e1=es.method,e0=es.extend({create:e1({method:"POST",fullPath:"/v1/billing/meter_events"})}),e2=es.method,e6=es.extend({create:e2({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:e2({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:e2({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:e2({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:e2({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:e2({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:e2({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),e8=es.method,e3=es.extend({create:e8({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:e8({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:e8({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:e8({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:e8({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),e4=es.method,e9=es.extend({fail:e4({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:e4({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:e4({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),e5=es.method,e7=es.extend({create:e5({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:e5({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:e5({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:e5({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),te=es.method,tt=es.extend({fail:te({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:te({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:te({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),tr=es.method,to=es.extend({create:tr({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:tr({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:tr({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:tr({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),tn=es.method,ti=es.extend({activate:tn({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:tn({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:tn({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),ta=es.method,ts=es.extend({create:ta({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:ta({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:ta({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:ta({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),tl=es.method,tu=es.extend({retrieve:tl({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:tl({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),tc=es.method,td=es.extend({retrieve:tc({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:tc({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),th=es.method,tp=es.extend({presentPaymentMethod:th({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),tm=es.method,tf=es.extend({create:tm({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:tm({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:tm({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:tm({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:tm({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:tm({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:tm({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:tm({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:tm({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:tm({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),ty=es.method,tv=es.extend({create:ty({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),tP=es.method,tg=es.extend({retrieve:tP({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:tP({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),tT=es.method,t_=es.extend({create:tT({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),tE=es.method,tS=es.extend({retrieve:tE({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:tE({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),tb=es.method,tO=es.extend({expire:tb({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),tx=es.method,tw=es.extend({create:tx({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:tx({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:tx({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:tx({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),tA=es.method,tR=es.extend({create:tA({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:tA({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:tA({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),tG=es.method,tC=es.extend({retrieve:tG({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:tG({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),tI=es.method,tk=es.extend({create:tI({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:tI({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:tI({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),tj=es.method,tD=es.extend({retrieve:tj({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:tj({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),tN=es.method,tM=es.extend({create:tN({method:"POST",fullPath:"/v1/apps/secrets"}),list:tN({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:tN({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:tN({method:"GET",fullPath:"/v1/apps/secrets/find"})}),tF=es.method,tU=es.extend({create:tF({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),tq=es.method,tL=es.extend({create:tq({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:tq({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),list:tq({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:tq({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:tq({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),tH=es.method,t$=es.extend({create:tH({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:tH({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),tB=es.method,tz=es.extend({retrieve:tB({method:"GET",fullPath:"/v1/tax/settings"}),update:tB({method:"POST",fullPath:"/v1/tax/settings"})}),tW=es.method,tK=es.extend({retrieve:tW({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:tW({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),tV=es.method,tJ=es.extend({create:tV({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:tV({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:tV({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:tV({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:tV({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),tQ=es.method,tX=es.extend({retrieve:tQ({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:tQ({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:tQ({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),tY=es.method,tZ=es.extend({retrieve:tY({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:tY({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),t1=es.method,t0=es.extend({createForceCapture:t1({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:t1({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:t1({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),t2=es.method,t6=es.extend({retrieve:t2({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:t2({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),t8=es.method,t3=es.extend({retrieve:t8({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:t8({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:t8({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),t4=es.method,t9=es.extend({retrieve:t4({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:t4({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:t4({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:t4({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),t5=es.method,t7=es.extend({retrieve:t5({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:t5({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),re=es.method,rt=es.extend({create:re({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:re({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:re({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:re({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),rr=es.method,ro=es.extend({create:rr({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:rr({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:rr({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:rr({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:rr({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),rn=es.method,ri=es.extend({retrieve:rn({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:rn({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),ra=es.method,rs=es.extend({create:ra({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:ra({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:ra({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:ra({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:ra({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:ra({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),rl=es.method,ru=es.extend({create:rl({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?rl({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),rl({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:rl({method:"POST",fullPath:"/v1/accounts/{account}"}),list:rl({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:rl({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:rl({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:rl({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:rl({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:rl({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:rl({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:rl({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:rl({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:rl({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:rl({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:rl({method:"GET",fullPath:"/v1/account"}),retrieveCapability:rl({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:rl({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:rl({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:rl({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:rl({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:rl({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),rc=es.method,rd=es.extend({create:rc({method:"POST",fullPath:"/v1/account_links"})}),rh=es.method,rp=es.extend({create:rh({method:"POST",fullPath:"/v1/account_sessions"})}),rm=es.method,rf=es.extend({create:rm({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:rm({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:rm({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:rm({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),ry=es.method,rv=es.extend({retrieve:ry({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:ry({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:ry({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:ry({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:ry({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:ry({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),rP=es.method,rg=es.extend({retrieve:rP({method:"GET",fullPath:"/v1/balance"})}),rT=es.method,r_=es.extend({retrieve:rT({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:rT({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),rE=es.method,rS=es.extend({create:rE({method:"POST",fullPath:"/v1/charges"}),retrieve:rE({method:"GET",fullPath:"/v1/charges/{charge}"}),update:rE({method:"POST",fullPath:"/v1/charges/{charge}"}),list:rE({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:rE({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:rE({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),rb=es.method,rO=es.extend({retrieve:rb({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),rx=es.method,rw=es.extend({retrieve:rx({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:rx({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),rA=es.method,rR=es.extend({create:rA({method:"POST",fullPath:"/v1/coupons"}),retrieve:rA({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:rA({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:rA({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:rA({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),rG=es.method,rC=es.extend({create:rG({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:rG({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:rG({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:rG({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:rG({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:rG({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:rG({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:rG({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),rI=es.method,rk=es.extend({create:rI({method:"POST",fullPath:"/v1/customer_sessions"})}),rj=es.method,rD=es.extend({create:rj({method:"POST",fullPath:"/v1/customers"}),retrieve:rj({method:"GET",fullPath:"/v1/customers/{customer}"}),update:rj({method:"POST",fullPath:"/v1/customers/{customer}"}),list:rj({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:rj({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:rj({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:rj({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:rj({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:rj({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:rj({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:rj({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:rj({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:rj({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:rj({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:rj({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:rj({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:rj({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:rj({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:rj({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:rj({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:rj({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:rj({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:rj({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:rj({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:rj({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:rj({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:rj({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:rj({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),rN=es.method,rM=es.extend({retrieve:rN({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:rN({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:rN({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:rN({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),rF=es.method,rU=es.extend({create:rF({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:rF({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),rq=es.method,rL=es.extend({retrieve:rq({method:"GET",fullPath:"/v1/events/{id}"}),list:rq({method:"GET",fullPath:"/v1/events",methodType:"list"})}),rH=es.method,r$=es.extend({retrieve:rH({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:rH({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),rB=es.method,rz=es.extend({create:rB({method:"POST",fullPath:"/v1/file_links"}),retrieve:rB({method:"GET",fullPath:"/v1/file_links/{link}"}),update:rB({method:"POST",fullPath:"/v1/file_links/{link}"}),list:rB({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),rW=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.keys(e).forEach(n=>{let i=e[n],a=o?`${o}[${n}]`:n;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(i)){if(!(i instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(i,"data"))return r(i,a);t[a]=i}else t[a]=String(i)})};return r(e,null),t}(t);for(let e in u){let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},rK=es.method,rV=es.extend({create:rK({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:rK({method:"GET",fullPath:"/v1/files/{file}"}),list:rK({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,$(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,rW(e,t,r))).catch(e=>o(e,null))}}),rJ=es.method,rQ=es.extend({create:rJ({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:rJ({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:rJ({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:rJ({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:rJ({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),rX=es.method,rY=es.extend({create:rX({method:"POST",fullPath:"/v1/invoices"}),retrieve:rX({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:rX({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:rX({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:rX({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),finalizeInvoice:rX({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:rX({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),listUpcomingLines:rX({method:"GET",fullPath:"/v1/invoices/upcoming/lines",methodType:"list"}),markUncollectible:rX({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:rX({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),retrieveUpcoming:rX({method:"GET",fullPath:"/v1/invoices/upcoming"}),search:rX({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:rX({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLineItem:rX({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:rX({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),rZ=es.method,r1=es.extend({retrieve:rZ({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),r0=es.method,r2="connect.stripe.com",r6=es.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${r2}/${r}?${$(e)}`},token:r0({method:"POST",path:"oauth/token",host:r2}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),r0({method:"POST",path:"oauth/deauthorize",host:r2}).apply(this,[e,...t])}}),r8=es.method,r3=es.extend({create:r8({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:r8({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:r8({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:r8({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:r8({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),r4=es.method,r9=es.extend({create:r4({method:"POST",fullPath:"/v1/payment_links"}),retrieve:r4({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:r4({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:r4({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:r4({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),r5=es.method,r7=es.extend({create:r5({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:r5({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:r5({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:r5({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),oe=es.method,ot=es.extend({create:oe({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:oe({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:oe({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:oe({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:oe({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),or=es.method,oo=es.extend({create:or({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:or({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:or({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:or({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:or({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:or({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),on=es.method,oi=es.extend({create:on({method:"POST",fullPath:"/v1/payouts"}),retrieve:on({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:on({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:on({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:on({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:on({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),oa=es.method,os=es.extend({create:oa({method:"POST",fullPath:"/v1/plans"}),retrieve:oa({method:"GET",fullPath:"/v1/plans/{plan}"}),update:oa({method:"POST",fullPath:"/v1/plans/{plan}"}),list:oa({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:oa({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),ol=es.method,ou=es.extend({create:ol({method:"POST",fullPath:"/v1/prices"}),retrieve:ol({method:"GET",fullPath:"/v1/prices/{price}"}),update:ol({method:"POST",fullPath:"/v1/prices/{price}"}),list:ol({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:ol({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),oc=es.method,od=es.extend({create:oc({method:"POST",fullPath:"/v1/products"}),retrieve:oc({method:"GET",fullPath:"/v1/products/{id}"}),update:oc({method:"POST",fullPath:"/v1/products/{id}"}),list:oc({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:oc({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:oc({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:oc({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:oc({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:oc({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:oc({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),oh=es.method,op=es.extend({create:oh({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:oh({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:oh({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:oh({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),om=es.method,of=es.extend({create:om({method:"POST",fullPath:"/v1/quotes"}),retrieve:om({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:om({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:om({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:om({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:om({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:om({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:om({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:om({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:om({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),oy=es.method,ov=es.extend({create:oy({method:"POST",fullPath:"/v1/refunds"}),retrieve:oy({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:oy({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:oy({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:oy({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),oP=es.method,og=es.extend({retrieve:oP({method:"GET",fullPath:"/v1/reviews/{review}"}),list:oP({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:oP({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),oT=es.method,o_=es.extend({list:oT({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),oE=es.method,oS=es.extend({create:oE({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:oE({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:oE({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:oE({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:oE({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:oE({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:oE({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),ob=es.method,oO=es.extend({create:ob({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:ob({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:ob({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:ob({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),ox=es.method,ow=es.extend({create:ox({method:"POST",fullPath:"/v1/sources"}),retrieve:ox({method:"GET",fullPath:"/v1/sources/{source}"}),update:ox({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:ox({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:ox({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),oA=es.method,oR=es.extend({create:oA({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:oA({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:oA({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:oA({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:oA({method:"DELETE",fullPath:"/v1/subscription_items/{item}"}),createUsageRecord:oA({method:"POST",fullPath:"/v1/subscription_items/{subscription_item}/usage_records"}),listUsageRecordSummaries:oA({method:"GET",fullPath:"/v1/subscription_items/{subscription_item}/usage_record_summaries",methodType:"list"})}),oG=es.method,oC=es.extend({create:oG({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:oG({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:oG({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:oG({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:oG({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:oG({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),oI=es.method,ok=es.extend({create:oI({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:oI({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:oI({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:oI({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:oI({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:oI({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:oI({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:oI({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),oj=es.method,oD=es.extend({retrieve:oj({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:oj({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),oN=es.method,oM=es.extend({create:oN({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:oN({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:oN({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:oN({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),oF=es.method,oU=es.extend({create:oF({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:oF({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:oF({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:oF({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),oq=es.method,oL=es.extend({create:oq({method:"POST",fullPath:"/v1/tokens"}),retrieve:oq({method:"GET",fullPath:"/v1/tokens/{token}"})}),oH=es.method,o$=es.extend({create:oH({method:"POST",fullPath:"/v1/topups"}),retrieve:oH({method:"GET",fullPath:"/v1/topups/{topup}"}),update:oH({method:"POST",fullPath:"/v1/topups/{topup}"}),list:oH({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:oH({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),oB=es.method,oz=es.extend({create:oB({method:"POST",fullPath:"/v1/transfers"}),retrieve:oB({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:oB({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:oB({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:oB({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:oB({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:oB({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:oB({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),oW=es.method,oK=es.extend({create:oW({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:oW({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:oW({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:oW({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:oW({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),oV=ee("apps",{Secrets:tM}),oJ=ee("billing",{MeterEventAdjustments:eZ,MeterEvents:e0,Meters:e6}),oQ=ee("billingPortal",{Configurations:eO,Sessions:tU}),oX=ee("checkout",{Sessions:tL}),oY=ee("climate",{Orders:e3,Products:td,Suppliers:tK}),oZ=ee("entitlements",{ActiveEntitlements:ed,Features:e$}),o1=ee("financialConnections",{Accounts:eu,Sessions:t$,Transactions:t6}),o0=ee("forwarding",{Requests:tk}),o2=ee("identity",{VerificationReports:ri,VerificationSessions:rs}),o6=ee("issuing",{Authorizations:ef,Cardholders:eg,Cards:eS,Disputes:eU,PersonalizationDesigns:ts,PhysicalBundles:tu,Tokens:tX,Transactions:t3}),o8=ee("radar",{EarlyFraudWarnings:eL,ValueListItems:rt,ValueLists:ro}),o3=ee("reporting",{ReportRuns:tR,ReportTypes:tC}),o4=ee("sigma",{ScheduledQueryRuns:tD}),o9=ee("tax",{Calculations:ev,Registrations:tw,Settings:tz,Transactions:t9}),o5=ee("terminal",{Configurations:ew,ConnectionTokens:eC,Locations:eX,Readers:tf}),o7=ee("testHelpers",{ConfirmationTokens:eR,Customers:eD,Refunds:tO,TestClocks:tJ,Issuing:ee("issuing",{Authorizations:ep,Cards:e_,PersonalizationDesigns:ti,Transactions:t0}),Terminal:ee("terminal",{Readers:tp}),Treasury:ee("treasury",{InboundTransfers:eK,OutboundPayments:e9,OutboundTransfers:tt,ReceivedCredits:tv,ReceivedDebits:t_})}),ne=ee("treasury",{CreditReversals:ek,DebitReversals:eM,FinancialAccounts:ez,InboundTransfers:eJ,OutboundPayments:e7,OutboundTransfers:to,ReceivedCredits:tg,ReceivedDebits:tS,TransactionEntries:tZ,Transactions:t7});class nt{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return W({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=o.toStream(()=>{let r=this._makeResponseEvent(e,o.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=this._getRequestId(n),a=o.getStatusCode(),s=this._makeResponseEvent(e,a,n);this._stripe._emitter.emit("response",s),o.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=n,e.error.statusCode=a,e.error.requestId=i,401===a?new I(e.error):403===a?new k(e.error):429===a?new j(e.error):A.generate(e.error);return e},e=>{throw new C({message:"Invalid JSON received from the Stripe API",exception:e,requestId:n["request-id"]})}).then(e=>{this._recordRequestMetrics(i,s.elapsed,t);let a=o.getRawResponse();this._addHeadersDirectlyToObject(a,n),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:a}),r(null,e)},e=>r(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&m.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(e-1,2),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t){let r=this._getMaxNetworkRetries(t);return"POST"===e&&r>0?`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`:null}_makeHeaders(e,t,r,o,n,i,a){let s={Authorization:e?`Bearer ${e}`:this._stripe.getApiField("auth"),Accept:"application/json","Content-Type":"application/x-www-form-urlencoded","User-Agent":this._getUserAgentString(),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":this._stripe.getApiField("stripeAccount"),"Idempotency-Key":this._defaultIdempotencyKey(n,a)},l="POST"==n||"PUT"==n||"PATCH"==n;return(l||t)&&(l||V(`${n} method had non-zero contentLength but no payload is expected for this verb`),s["Content-Length"]=t),Object.assign(W(s),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(){let e=this._stripe.getConstant("PACKAGE_VERSION"),t=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/v1 NodeBindings/${e} ${t}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e){if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)V("Request metrics buffer is full, dropping telemetry message.");else{let o={request_id:e,request_duration_ms:t};r&&r.length>0&&(o.usage=r),this._stripe._prevRequestMetrics.push(o)}}}_request(e,t,r,o,n,i={},a=[],s,l=null){let u;let c=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),d=(o,n,l)=>{let h=i.settings&&i.settings.timeout&&Number.isInteger(i.settings.timeout)&&i.settings.timeout>=0?i.settings.timeout:this._stripe.getApiField("timeout"),p=this._stripe.getApiField("httpClient").makeRequest(t||this._stripe.getApiField("host"),this._stripe.getApiField("port"),r,e,n,u,this._stripe.getApiField("protocol"),h),f=Date.now(),y=W({api_version:o,account:n["Stripe-Account"],idempotency_key:n["Idempotency-Key"],method:e,path:r,request_start_time:f}),v=l||0,P=this._getMaxNetworkRetries(i.settings||{});this._stripe._emitter.emit("request",y),p.then(e=>nt._shouldRetry(e,v,P)?c(d,o,n,v,e.getHeaders()["retry-after"]):i.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(y,a,s)(e):this._jsonResponseHandler(y,a,s)(e)).catch(e=>nt._shouldRetry(null,v,P,e)?c(d,o,n,v,null):s(new D({message:e.code&&e.code===m.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${h}ms)`:nt._generateConnectionErrorMessage(v),detail:e})))},h=(t,r)=>{if(t)return s(t);u=r,this._stripe.getClientUserAgent(t=>{var r,o;let a=this._stripe.getApiField("version"),s=this._makeHeaders(n,u.length,a,t,e,null!==(r=i.headers)&&void 0!==r?r:null,null!==(o=i.settings)&&void 0!==o?o:{});d(a,s,0)})};l?l(e,o,i.headers,h):h(null,$(o||{}))}}function nr(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof l&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){if(!e)throw new A({message:"Options are required"});return e.timestamp=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),e.scheme=e.scheme||r.EXPECTED_SCHEME,e.cryptoProvider=e.cryptoProvider||s(),e.signature=e.signature||e.cryptoProvider.computeHMACSignature(e.timestamp+"."+e.payload,e.secret),["t="+e.timestamp,e.scheme+"="+e.signature].join(",")}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r),f=(l=l||s()).computeHMACSignature(o(d,h),r);return i(d,c,h,f,a,p,m,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r);l=l||s();let f=await l.computeHMACSignatureAsync(o(d,h),r);return i(d,c,h,f,a,p,m,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new N(t,e,{message:"No webhook payload was provided."});let o="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new N(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,s="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let o=t.split("=");return"t"===o[0]&&(e.timestamp=parseInt(o[1],10)),o[0]===r&&e.signatures.push(o[1]),e},{timestamp:-1,signatures:[]});if(!s||-1===s.timestamp)throw new N(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!s.signatures.length)throw new N(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:s,suspectPayloadType:o}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://github.com/stripe/stripe-node#webhook-signing",d=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new N(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+d});throw new N(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+d})}let h=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&h>i)throw new N(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}return t.signature=r,t}let no="api.stripe.com",nn="/v1/",ni="2023-10-16",na=["name","version","url","partner_id"],ns=["apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount"],nl=e=>new nt(e,es.MAX_BUFFERED_REQUEST_METRICS),nu=function(e,t=nl){function r(t=e){return nr(t)}function i(n,a={}){if(!(this instanceof i))return new i(n,a);let s=this._getPropsFromConfig(a);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=i.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let l=s.httpAgent||null;this._api={auth:null,host:s.host||no,port:s.port||"443",protocol:s.protocol||"https",basePath:nn,version:s.apiVersion||ni,timeout:J("timeout",s.timeout,8e4),maxNetworkRetries:J("maxNetworkRetries",s.maxNetworkRetries,1),agent:l,httpClient:s.httpClient||(l?this._platformFunctions.createNodeHttpClient(l):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:s.stripeAccount||null};let u=s.typescript||!1;u!==i.USER_AGENT.typescript&&(i.USER_AGENT.typescript=u),s.appInfo&&this._setAppInfo(s.appInfo),this._prepResources(),this._setApiKey(n),this.errors=o,this.webhooks=r(),this._prevRequestMetrics=[],this._enableTelemetry=!1!==s.telemetry,this._requestSender=t(this),this.StripeResource=i.StripeResource}return i.PACKAGE_VERSION="14.25.0",i.USER_AGENT=Object.assign({bindings_version:i.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),i.StripeResource=es,i.resources=n,i.HttpClient=m,i.HttpClientResponse=f,i.CryptoProvider=s,i.webhooks=Object.assign(r,nr(e)),i.errors=o,i.createNodeHttpClient=e.createNodeHttpClient,i.createFetchHttpClient=e.createFetchHttpClient,i.createNodeCryptoProvider=e.createNodeCryptoProvider,i.createSubtleCryptoProvider=e.createSubtleCryptoProvider,i.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,_setApiKey(e){e&&this._setApiField("auth",`Bearer ${e}`)},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=na.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return no;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return nn;case"DEFAULT_API_VERSION":return ni;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 2;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return i[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=J(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>2,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(i.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)n[t]=encodeURIComponent(null!==(o=e[t])&&void 0!==o?o:"null");n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this)},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!ns.includes(e)).length>0)throw Error(`Config object may only contain the following: ${ns.join(", ")}`);return e}},i}(new Y)}};