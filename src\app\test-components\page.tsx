'use client'

import { useState, Suspense } from 'react'
import dynamic from 'next/dynamic'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Loading } from '@/components/ui/Loading'
import { Column } from '@/components/ui/DataTable'

// Dynamic imports for heavy components
const RichTextEditor = dynamic(() => import('@/components/ui/RichTextEditor'), {
  loading: () => <Loading text="Đang tải Rich Text Editor..." />,
  ssr: false
})

const VideoPlayer = dynamic(() => import('@/components/ui/VideoPlayer'), {
  loading: () => <Loading text="Đang tải Video Player..." />,
  ssr: false
})

const DataTable = dynamic(() => import('@/components/ui/DataTable'), {
  loading: () => <Loading text="Đang tải Data Table..." />,
  ssr: false
})

// Sample data for DataTable
interface SampleData {
  id: number
  name: string
  email: string
  role: string
  status: 'active' | 'inactive'
  createdAt: string
  score: number
}

const sampleData: SampleData[] = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: 'ng<PERSON><PERSON><PERSON>@example.com',
    role: 'student',
    status: 'active',
    createdAt: '2024-01-15',
    score: 85
  },
  {
    id: 2,
    name: 'Trần Thị B',
    email: '<EMAIL>',
    role: 'instructor',
    status: 'active',
    createdAt: '2024-01-10',
    score: 92
  },
  {
    id: 3,
    name: 'Lê Văn C',
    email: '<EMAIL>',
    role: 'student',
    status: 'inactive',
    createdAt: '2024-01-20',
    score: 78
  },
  {
    id: 4,
    name: 'Phạm Thị D',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    createdAt: '2024-01-05',
    score: 95
  },
  {
    id: 5,
    name: 'Hoàng Văn E',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    createdAt: '2024-01-25',
    score: 88
  }
]

export default function TestComponentsPage() {
  const [richTextContent, setRichTextContent] = useState('<p>Đây là nội dung mẫu cho <strong>Rich Text Editor</strong>. Bạn có thể <em>định dạng</em> văn bản, thêm <u>gạch chân</u>, tạo danh sách và nhiều hơn nữa!</p>')

  const columns: Column<SampleData>[] = [
    {
      key: 'id',
      title: 'ID',
      sortable: true,
      width: 80,
      align: 'center'
    },
    {
      key: 'name',
      title: 'Họ và tên',
      sortable: true,
      filterable: true,
      render: (value, row) => (
        <div className="flex items-center">
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm mr-3">
            {value.charAt(0)}
          </div>
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'email',
      title: 'Email',
      sortable: true,
      filterable: true,
      render: (value) => (
        <a href={`mailto:${value}`} className="text-blue-600 hover:underline">
          {value}
        </a>
      )
    },
    {
      key: 'role',
      title: 'Vai trò',
      sortable: true,
      filterable: true,
      render: (value) => {
        const roleColors = {
          student: 'bg-blue-100 text-blue-800',
          instructor: 'bg-green-100 text-green-800',
          admin: 'bg-purple-100 text-purple-800'
        }
        const roleLabels = {
          student: 'Học viên',
          instructor: 'Giảng viên',
          admin: 'Quản trị'
        }
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${roleColors[value as keyof typeof roleColors]}`}>
            {roleLabels[value as keyof typeof roleLabels]}
          </span>
        )
      }
    },
    {
      key: 'status',
      title: 'Trạng thái',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === 'active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {value === 'active' ? 'Hoạt động' : 'Không hoạt động'}
        </span>
      )
    },
    {
      key: 'score',
      title: 'Điểm số',
      sortable: true,
      align: 'center',
      render: (value) => (
        <div className="flex items-center justify-center">
          <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold ${
            value >= 90 ? 'bg-green-500' :
            value >= 80 ? 'bg-blue-500' :
            value >= 70 ? 'bg-yellow-500' : 'bg-red-500'
          }`}>
            {value}
          </div>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString('vi-VN')
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-7xl space-y-12">
        {/* Header */}
        <div className="text-center">
          <h1 className="heading-1 mb-4">
            Advanced UI Components Demo
          </h1>
          <p className="body-large text-gray-600">
            Showcase các components nâng cao của WebTA LMS
          </p>
        </div>

        {/* Rich Text Editor */}
        <Card className="card-padding-lg">
          <h2 className="heading-2 mb-6">Rich Text Editor</h2>
          <p className="body-medium text-gray-600 mb-4">
            Editor WYSIWYG với đầy đủ tính năng định dạng văn bản, hỗ trợ HTML và các shortcut phổ biến.
          </p>
          
          <Suspense fallback={<Loading text="Đang tải Rich Text Editor..." />}>
            <RichTextEditor
              value={richTextContent}
              onChange={setRichTextContent}
              placeholder="Nhập nội dung của bạn tại đây..."
              minHeight={300}
              className="mb-4"
            />
          </Suspense>
          
          <div className="mt-4 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-semibold mb-2">HTML Output:</h3>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap">
              {richTextContent}
            </pre>
          </div>
        </Card>

        {/* Video Player */}
        <Card className="card-padding-lg">
          <h2 className="heading-2 mb-6">Video Player</h2>
          <p className="body-medium text-gray-600 mb-4">
            Video player tùy chỉnh với controls đầy đủ, hỗ trợ fullscreen, speed control và progress tracking.
          </p>
          
          <Suspense fallback={<Loading text="Đang tải Video Player..." />}>
            <VideoPlayer
              src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
              poster="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg"
              title="Big Buck Bunny - Sample Video"
              className="aspect-video max-w-4xl mx-auto"
              onTimeUpdate={(currentTime, duration) => {
                console.log(`Video progress: ${currentTime}/${duration}`)
              }}
              onPlay={() => console.log('Video started playing')}
              onPause={() => console.log('Video paused')}
              onEnded={() => console.log('Video ended')}
            />
          </Suspense>
          
          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Features:</strong></p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Custom controls với play/pause, volume, progress bar</li>
              <li>Fullscreen support</li>
              <li>Playback speed control (0.5x - 2x)</li>
              <li>Keyboard shortcuts (Space: play/pause, F: fullscreen)</li>
              <li>Progress tracking và event callbacks</li>
              <li>Responsive design</li>
            </ul>
          </div>
        </Card>

        {/* Data Table */}
        <Card className="card-padding-lg">
          <h2 className="heading-2 mb-6">Data Table</h2>
          <p className="body-medium text-gray-600 mb-4">
            Bảng dữ liệu với tính năng sorting, filtering, pagination và custom rendering.
          </p>
          
          <Suspense fallback={<Loading text="Đang tải Data Table..." />}>
            <DataTable
              data={sampleData}
              columns={columns}
              pageSize={3}
              showPagination={true}
              showSearch={true}
              searchPlaceholder="Tìm kiếm người dùng..."
              onRowClick={(row) => {
                alert(`Clicked on: ${row.name} (${row.email})`)
              }}
              rowClassName={(row) =>
                row.status === 'inactive' ? 'opacity-60' : ''
              }
              className="mt-4"
            />
          </Suspense>
          
          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Features:</strong></p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Sorting theo column (click vào header)</li>
              <li>Global search và column-specific filtering</li>
              <li>Pagination với navigation</li>
              <li>Custom cell rendering</li>
              <li>Row click events</li>
              <li>Responsive design</li>
              <li>Loading states và empty states</li>
            </ul>
          </div>
        </Card>

        {/* Component Usage Examples */}
        <Card className="card-padding-lg">
          <h2 className="heading-2 mb-6">Usage Examples</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="heading-3 mb-3">Rich Text Editor</h3>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
{`import RichTextEditor from '@/components/ui/RichTextEditor'

function MyComponent() {
  const [content, setContent] = useState('')
  
  return (
    <RichTextEditor
      value={content}
      onChange={setContent}
      placeholder="Nhập nội dung..."
      minHeight={200}
    />
  )
}`}
              </pre>
            </div>
            
            <div>
              <h3 className="heading-3 mb-3">Video Player</h3>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
{`import VideoPlayer from '@/components/ui/VideoPlayer'

function MyComponent() {
  return (
    <VideoPlayer
      src="/path/to/video.mp4"
      poster="/path/to/poster.jpg"
      title="Video Title"
      onTimeUpdate={(current, duration) => {
        // Track progress
      }}
    />
  )
}`}
              </pre>
            </div>
            
            <div>
              <h3 className="heading-3 mb-3">Data Table</h3>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
{`import DataTable from '@/components/ui/DataTable'

const columns = [
  { key: 'name', title: 'Name', sortable: true },
  { key: 'email', title: 'Email', filterable: true },
  { 
    key: 'status', 
    title: 'Status',
    render: (value) => <Badge>{value}</Badge>
  }
]

function MyComponent() {
  return (
    <DataTable
      data={data}
      columns={columns}
      showPagination={true}
      showSearch={true}
    />
  )
}`}
              </pre>
            </div>
          </div>
        </Card>

        {/* Back to Home */}
        <div className="text-center">
          <Button onClick={() => window.history.back()}>
            ← Quay lại
          </Button>
        </div>
      </div>
    </div>
  )
}
