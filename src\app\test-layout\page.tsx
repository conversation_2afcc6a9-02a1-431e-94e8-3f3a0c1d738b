'use client'

import Link from 'next/link'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function TestLayoutPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <Card className="p-8">
          <h1 className="text-3xl font-bold mb-6">Layout Testing Page</h1>
          
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">Layout Structure Test</h2>
              <p className="text-gray-600 mb-4">
                Trang này để test layout structure và đảm bảo không có duplicate headers/footers.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Main App Routes */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Main App Routes</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Các routes này sẽ có Header và Footer
                </p>
                <div className="space-y-2">
                  <Link href="/" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      🏠 Home Page
                    </Button>
                  </Link>
                  <Link href="/courses" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      📚 Courses Page
                    </Button>
                  </Link>
                  <Link href="/about" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      ℹ️ About Page
                    </Button>
                  </Link>
                  <Link href="/contact" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      📞 Contact Page
                    </Button>
                  </Link>
                </div>
              </Card>

              {/* Dashboard Routes */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Dashboard Routes</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Các routes này có layout riêng, không có Header/Footer chính
                </p>
                <div className="space-y-2">
                  <Link href="/dashboard" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      📊 Dashboard (Auto-redirect)
                    </Button>
                  </Link>
                  <Link href="/dashboard/student" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      🎓 Student Dashboard
                    </Button>
                  </Link>
                  <Link href="/dashboard/instructor" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      👨‍🏫 Instructor Dashboard
                    </Button>
                  </Link>
                  <Link href="/test-dashboard" className="block">
                    <Button variant="outline" className="w-full justify-start">
                      🧪 Dashboard API Test
                    </Button>
                  </Link>
                </div>
              </Card>
            </div>

            {/* Layout Verification */}
            <Card className="p-6 bg-blue-50 border-blue-200">
              <h3 className="text-lg font-semibold mb-4 text-blue-900">
                Layout Verification Checklist
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span>Main app routes có Header và Footer</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span>Dashboard routes có navigation riêng</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span>Không có duplicate headers</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span>Layout transitions mượt mà</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span>Responsive design hoạt động đúng</span>
                </div>
              </div>
            </Card>

            {/* Current Route Info */}
            <Card className="p-6 bg-gray-50">
              <h3 className="text-lg font-semibold mb-4">Current Route Info</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Current Path:</strong> /test-layout
                </div>
                <div>
                  <strong>Layout Type:</strong> Main App Layout (with Header/Footer)
                </div>
                <div>
                  <strong>Expected Behavior:</strong> Should show main navigation header and footer
                </div>
              </div>
            </Card>

            {/* Instructions */}
            <Card className="p-6 bg-yellow-50 border-yellow-200">
              <h3 className="text-lg font-semibold mb-4 text-yellow-900">
                Testing Instructions
              </h3>
              <ol className="list-decimal list-inside space-y-2 text-sm text-yellow-800">
                <li>Kiểm tra trang này có Header và Footer không</li>
                <li>Click vào các main app routes và verify có Header/Footer</li>
                <li>Click vào dashboard routes và verify chỉ có dashboard navigation</li>
                <li>Kiểm tra không có duplicate navigation bars</li>
                <li>Test responsive behavior trên mobile và desktop</li>
                <li>Verify layout transitions mượt mà</li>
              </ol>
            </Card>

            {/* Back to Home */}
            <div className="text-center pt-6">
              <Link href="/">
                <Button>
                  ← Về trang chủ
                </Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
