'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Loading } from '@/components/ui/Loading'
import { AlertMessage } from '@/components/ui/Alert'

interface EnrollmentData {
  _id: string
  courseId: {
    _id: string
    title: string
    slug: string
    thumbnail?: string
    instructor: {
      profile: {
        firstName: string
        lastName: string
      }
    }
    stats: {
      totalLessons: number
      totalDuration: number
    }
  } | null
  progress: {
    status: string
    completionPercentage: number
    completedLessons: number
    totalLessons: number
    totalWatchTime: number
    lastAccessedAt: string
  }
  accessExpiresAt?: string
  createdAt: string
}

interface DashboardStats {
  totalCourses: number
  completedCourses: number
  totalWatchTime: number
  averageProgress: number
}

export default function StudentDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [enrollments, setEnrollments] = useState<EnrollmentData[]>([])
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'in_progress' | 'completed'>('all')

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!session?.user?.id) return

      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/dashboard/student')
        const data = await response.json()

        if (data.success) {
          setEnrollments(data.data.enrollments)
          setStats(data.data.stats)
        } else {
          setError(data.error || 'Lỗi khi tải dữ liệu dashboard')
        }
      } catch (err) {
        setError('Lỗi kết nối server')
      } finally {
        setLoading(false)
      }
    }

    if (session?.user?.id) {
      fetchDashboardData()
    }
  }, [session])

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const formatWatchTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500'
    if (percentage >= 50) return 'bg-yellow-500'
    return 'bg-blue-500'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">Hoàn thành</Badge>
      case 'in_progress':
        return <Badge variant="warning">Đang học</Badge>
      case 'not_started':
        return <Badge variant="secondary">Chưa bắt đầu</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const filteredEnrollments = enrollments.filter(enrollment => {
    if (filter === 'all') return true
    if (filter === 'completed') return enrollment.progress.status === 'completed'
    if (filter === 'in_progress') return enrollment.progress.status === 'in_progress'
    return true
  })

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Xin chào, {session.user.name || 'Học viên'}!
              </h1>
              <p className="mt-2 text-gray-600">
                Theo dõi tiến độ học tập của bạn
              </p>
            </div>
            <Link href="/courses">
              <Button>
                Khám phá khóa học mới
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6">
            <AlertMessage variant="error" message={error} />
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <span className="text-2xl">📚</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tổng khóa học</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCourses}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <span className="text-2xl">✅</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Đã hoàn thành</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completedCourses}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <span className="text-2xl">⏱️</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Thời gian học</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWatchTime(stats.totalWatchTime)}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <span className="text-2xl">📊</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tiến độ trung bình</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {Math.round(stats.averageProgress)}%
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Course Filter */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Khóa học của tôi</h2>
          
          <div className="flex gap-2">
            {[
              { key: 'all', label: 'Tất cả' },
              { key: 'in_progress', label: 'Đang học' },
              { key: 'completed', label: 'Hoàn thành' }
            ].map((option) => (
              <Button
                key={option.key}
                variant={filter === option.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter(option.key as any)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Courses Grid */}
        {filteredEnrollments.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEnrollments.filter(enrollment => enrollment.courseId).map((enrollment) => (
              <Card key={enrollment._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <Link href={`/learn/${enrollment.courseId!.slug}`}>
                  <div className="aspect-video bg-gray-200 relative">
                    {enrollment.courseId.thumbnail ? (
                      <Image
                        src={enrollment.courseId.thumbnail}
                        alt={enrollment.courseId.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-400 text-4xl">
                        📚
                      </div>
                    )}
                    
                    {/* Progress Overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">
                      <div className="flex justify-between items-center text-sm">
                        <span>{enrollment.progress.completionPercentage}% hoàn thành</span>
                        {getStatusBadge(enrollment.progress.status)}
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-2 mt-1">
                        <div
                          className={`h-2 rounded-full ${getProgressColor(enrollment.progress.completionPercentage)}`}
                          style={{ width: `${enrollment.progress.completionPercentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <h3 className="font-semibold text-lg mb-2 line-clamp-2">
                      {enrollment.courseId.title}
                    </h3>
                    
                    <div className="flex items-center gap-2 mb-3 text-sm text-gray-500">
                      <span>👨‍🏫</span>
                      <span>
                        {enrollment.courseId.instructor.profile.firstName}{' '}
                        {enrollment.courseId.instructor.profile.lastName}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center text-sm text-gray-600">
                      <div>
                        {enrollment.progress.completedLessons}/{enrollment.progress.totalLessons} bài học
                      </div>
                      <div>
                        {formatWatchTime(enrollment.progress.totalWatchTime)}
                      </div>
                    </div>
                    
                    {enrollment.accessExpiresAt && (
                      <div className="mt-2 text-xs text-orange-600">
                        Hết hạn: {new Date(enrollment.accessExpiresAt).toLocaleDateString('vi-VN')}
                      </div>
                    )}
                  </div>
                </Link>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {filter === 'all' 
                ? 'Chưa có khóa học nào'
                : filter === 'completed'
                ? 'Chưa hoàn thành khóa học nào'
                : 'Chưa có khóa học đang học'
              }
            </h3>
            <p className="text-gray-600 mb-4">
              {filter === 'all'
                ? 'Hãy khám phá và đăng ký khóa học đầu tiên của bạn'
                : 'Thử thay đổi bộ lọc để xem khóa học khác'
              }
            </p>
            {filter === 'all' ? (
              <Link href="/courses">
                <Button>Khám phá khóa học</Button>
              </Link>
            ) : (
              <Button onClick={() => setFilter('all')}>
                Xem tất cả khóa học
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
