"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@next-auth";
exports.ids = ["vendor-chunks/@next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@next-auth/mongodb-adapter/dist/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@next-auth/mongodb-adapter/dist/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongoDBAdapter = exports._id = exports.format = exports.defaultCollections = void 0;\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  <p style={{fontWeight: \"normal\"}}>Official <a href=\"https://www.mongodb.com\">MongoDB</a> adapter for Auth.js / NextAuth.js.</p>\n *  <a href=\"https://www.mongodb.com\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/mongodb.svg\" width=\"30\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn2pnpm\n * npm install next-auth @next-auth/mongodb-adapter mongodb\n * ```\n *\n * @module @next-auth/mongodb-adapter\n */\nconst mongodb_1 = __webpack_require__(/*! mongodb */ \"mongodb\");\nexports.defaultCollections = {\n    Users: \"users\",\n    Accounts: \"accounts\",\n    Sessions: \"sessions\",\n    VerificationTokens: \"verification_tokens\",\n};\nexports.format = {\n    /** Takes a mongoDB object and returns a plain old JavaScript object */\n    from(object) {\n        const newObject = {};\n        for (const key in object) {\n            const value = object[key];\n            if (key === \"_id\") {\n                newObject.id = value.toHexString();\n            }\n            else if (key === \"userId\") {\n                newObject[key] = value.toHexString();\n            }\n            else {\n                newObject[key] = value;\n            }\n        }\n        return newObject;\n    },\n    /** Takes a plain old JavaScript object and turns it into a mongoDB object */\n    to(object) {\n        const newObject = {\n            _id: _id(object.id),\n        };\n        for (const key in object) {\n            const value = object[key];\n            if (key === \"userId\")\n                newObject[key] = _id(value);\n            else if (key === \"id\")\n                continue;\n            else\n                newObject[key] = value;\n        }\n        return newObject;\n    },\n};\n/** @internal */\nfunction _id(hex) {\n    if ((hex === null || hex === void 0 ? void 0 : hex.length) !== 24)\n        return new mongodb_1.ObjectId();\n    return new mongodb_1.ObjectId(hex);\n}\nexports._id = _id;\n/**\n * ## Setup\n *\n * The MongoDB adapter does not handle connections automatically, so you will have to make sure that you pass the Adapter a `MongoClient` that is connected already. Below you can see an example how to do this.\n *\n * ### Add the MongoDB client\n *\n * ```ts\n * // This approach is taken from https://github.com/vercel/next.js/tree/canary/examples/with-mongodb\n * import { MongoClient } from \"mongodb\"\n *\n * if (!process.env.MONGODB_URI) {\n *   throw new Error('Invalid/Missing environment variable: \"MONGODB_URI\"')\n * }\n *\n * const uri = process.env.MONGODB_URI\n * const options = {}\n *\n * let client\n * let clientPromise: Promise<MongoClient>\n *\n * if (process.env.NODE_ENV === \"development\") {\n *   // In development mode, use a global variable so that the value\n *   // is preserved across module reloads caused by HMR (Hot Module Replacement).\n *   if (!global._mongoClientPromise) {\n *     client = new MongoClient(uri, options)\n *     global._mongoClientPromise = client.connect()\n *   }\n *   clientPromise = global._mongoClientPromise\n * } else {\n *   // In production mode, it's best to not use a global variable.\n *   client = new MongoClient(uri, options)\n *   clientPromise = client.connect()\n * }\n *\n * // Export a module-scoped MongoClient promise. By doing this in a\n * // separate module, the client can be shared across functions.\n * export default clientPromise\n * ```\n *\n * ### Configure Auth.js\n *\n * ```js\n * import NextAuth from \"next-auth\"\n * import { MongoDBAdapter } from \"@next-auth/mongodb-adapter\"\n * import clientPromise from \"../../../lib/mongodb\"\n *\n * // For more information on each option (and a full list of options) go to\n * // https://authjs.dev/reference/providers/oauth\n * export default NextAuth({\n *   adapter: MongoDBAdapter(clientPromise),\n *   ...\n * })\n * ```\n **/\nfunction MongoDBAdapter(client, options = {}) {\n    const { collections } = options;\n    const { from, to } = exports.format;\n    const db = (async () => {\n        const _db = (await client).db(options.databaseName);\n        const c = { ...exports.defaultCollections, ...collections };\n        return {\n            U: _db.collection(c.Users),\n            A: _db.collection(c.Accounts),\n            S: _db.collection(c.Sessions),\n            V: _db.collection(c === null || c === void 0 ? void 0 : c.VerificationTokens),\n        };\n    })();\n    return {\n        async createUser(data) {\n            const user = to(data);\n            await (await db).U.insertOne(user);\n            return from(user);\n        },\n        async getUser(id) {\n            const user = await (await db).U.findOne({ _id: _id(id) });\n            if (!user)\n                return null;\n            return from(user);\n        },\n        async getUserByEmail(email) {\n            const user = await (await db).U.findOne({ email });\n            if (!user)\n                return null;\n            return from(user);\n        },\n        async getUserByAccount(provider_providerAccountId) {\n            const account = await (await db).A.findOne(provider_providerAccountId);\n            if (!account)\n                return null;\n            const user = await (await db).U.findOne({ _id: new mongodb_1.ObjectId(account.userId) });\n            if (!user)\n                return null;\n            return from(user);\n        },\n        async updateUser(data) {\n            const { _id, ...user } = to(data);\n            const result = await (await db).U.findOneAndUpdate({ _id }, { $set: user }, { returnDocument: \"after\" });\n            return from(result.value);\n        },\n        async deleteUser(id) {\n            const userId = _id(id);\n            const m = await db;\n            await Promise.all([\n                m.A.deleteMany({ userId: userId }),\n                m.S.deleteMany({ userId: userId }),\n                m.U.deleteOne({ _id: userId }),\n            ]);\n        },\n        linkAccount: async (data) => {\n            const account = to(data);\n            await (await db).A.insertOne(account);\n            return account;\n        },\n        async unlinkAccount(provider_providerAccountId) {\n            const { value: account } = await (await db).A.findOneAndDelete(provider_providerAccountId);\n            return from(account);\n        },\n        async getSessionAndUser(sessionToken) {\n            const session = await (await db).S.findOne({ sessionToken });\n            if (!session)\n                return null;\n            const user = await (await db).U.findOne({ _id: new mongodb_1.ObjectId(session.userId) });\n            if (!user)\n                return null;\n            return {\n                user: from(user),\n                session: from(session),\n            };\n        },\n        async createSession(data) {\n            const session = to(data);\n            await (await db).S.insertOne(session);\n            return from(session);\n        },\n        async updateSession(data) {\n            const { _id, ...session } = to(data);\n            const result = await (await db).S.findOneAndUpdate({ sessionToken: session.sessionToken }, { $set: session }, { returnDocument: \"after\" });\n            return from(result.value);\n        },\n        async deleteSession(sessionToken) {\n            const { value: session } = await (await db).S.findOneAndDelete({\n                sessionToken,\n            });\n            return from(session);\n        },\n        async createVerificationToken(data) {\n            await (await db).V.insertOne(to(data));\n            return data;\n        },\n        async useVerificationToken(identifier_token) {\n            const { value: verificationToken } = await (await db).V.findOneAndDelete(identifier_token);\n            if (!verificationToken)\n                return null;\n            // @ts-expect-error\n            delete verificationToken._id;\n            return verificationToken;\n        },\n    };\n}\nexports.MongoDBAdapter = MongoDBAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next-auth/mongodb-adapter/dist/index.js\n");

/***/ })

};
;