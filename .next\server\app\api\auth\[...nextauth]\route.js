"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ADMIN_OneDrive_Desktop_WebTA_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ADMIN_OneDrive_Desktop_WebTA_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFDUTtBQUV4QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHM/MDA5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmV4dEF1dGggZnJvbSAnbmV4dC1hdXRoJ1xuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICdAL2xpYi9hdXRoJ1xuXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpXG5cbmV4cG9ydCB7IGhhbmRsZXIgYXMgR0VULCBoYW5kbGVyIGFzIFBPU1QgfVxuIl0sIm5hbWVzIjpbIk5leHRBdXRoIiwiYXV0aE9wdGlvbnMiLCJoYW5kbGVyIiwiR0VUIiwiUE9TVCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _next_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @next-auth/mongodb-adapter */ \"(rsc)/./node_modules/@next-auth/mongodb-adapter/dist/index.js\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n\n\n\n\n\n\n// MongoDB client for NextAuth adapter\nconst client = new mongodb__WEBPACK_IMPORTED_MODULE_3__.MongoClient(process.env.MONGODB_URI);\nconst clientPromise = client.connect();\nconst authOptions = {\n    adapter: (0,_next_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_2__.MongoDBAdapter)(clientPromise),\n    secret: process.env.NEXTAUTH_SECRET,\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Email v\\xe0 mật khẩu l\\xe0 bắt buộc\");\n                }\n                try {\n                    await (0,_mongodb__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n                    // Find user with password field\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                        email: credentials.email.toLowerCase()\n                    }).select(\"+password\");\n                    if (!user) {\n                        throw new Error(\"Email hoặc mật khẩu kh\\xf4ng đ\\xfang\");\n                    }\n                    // Check if account is locked\n                    if (user.isLocked()) {\n                        throw new Error(\"T\\xe0i khoản đ\\xe3 bị kh\\xf3a do đăng nhập sai qu\\xe1 nhiều lần\");\n                    }\n                    // Check if account is active\n                    if (user.status !== _models_User__WEBPACK_IMPORTED_MODULE_5__.UserStatus.ACTIVE) {\n                        throw new Error(\"T\\xe0i khoản chưa được k\\xedch hoạt\");\n                    }\n                    // Verify password\n                    const isPasswordValid = await user.comparePassword(credentials.password);\n                    if (!isPasswordValid) {\n                        // Increment login attempts\n                        await user.incrementLoginAttempts();\n                        throw new Error(\"Email hoặc mật khẩu kh\\xf4ng đ\\xfang\");\n                    }\n                    // Reset login attempts on successful login\n                    if (user.loginAttempts > 0) {\n                        await user.updateOne({\n                            $unset: {\n                                loginAttempts: 1,\n                                lockUntil: 1\n                            }\n                        });\n                    }\n                    // Update last login\n                    await user.updateOne({\n                        lastLogin: new Date()\n                    });\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.profile.fullName,\n                        role: user.role,\n                        status: user.status,\n                        emailVerified: user.emailVerified,\n                        image: user.profile.avatar\n                    };\n                } catch (error) {\n                    throw new Error(error.message || \"Đ\\xe3 xảy ra lỗi trong qu\\xe1 tr\\xecnh đăng nhập\");\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\",\n        verifyRequest: \"/auth/verify-request\",\n        newUser: \"/auth/welcome\"\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // Initial sign in\n            if (account && user) {\n                token.role = user.role;\n                token.status = user.status;\n                token.emailVerified = user.emailVerified;\n            }\n            // Return previous token if the access token has not expired yet\n            return token;\n        },\n        async session ({ session, token }) {\n            // Send properties to the client\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.emailVerified = token.emailVerified;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow OAuth without email verification\n            if (account?.provider !== \"credentials\") {\n                return true;\n            }\n            // For credentials, check email verification\n            return user.emailVerified === true;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            console.log(`User ${user.email} signed in with ${account?.provider}`);\n        },\n        async signOut ({ session, token }) {\n            console.log(`User signed out`);\n        },\n        async createUser ({ user }) {\n            console.log(`New user created: ${user.email}`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONNECTION_STATES: () => (/* binding */ CONNECTION_STATES),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   disconnectDB: () => (/* binding */ disconnectDB),\n/* harmony export */   getConnectionStatus: () => (/* binding */ getConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env.local\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n/**\n * Utility function để disconnect từ database\n * Chủ yếu sử dụng trong testing\n */ async function disconnectDB() {\n    if (cached.conn) {\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().disconnect();\n        cached.conn = null;\n        cached.promise = null;\n    }\n}\n/**\n * Check database connection status\n */ function getConnectionStatus() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState;\n}\n/**\n * Connection states:\n * 0 = disconnected\n * 1 = connected\n * 2 = connecting\n * 3 = disconnecting\n */ const CONNECTION_STATES = {\n    DISCONNECTED: 0,\n    CONNECTED: 1,\n    CONNECTING: 2,\n    DISCONNECTING: 3\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageLevel: () => (/* binding */ LanguageLevel),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   UserStatus: () => (/* binding */ UserStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"STUDENT\"] = \"student\";\n    UserRole[\"INSTRUCTOR\"] = \"instructor\";\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n})(UserRole || (UserRole = {}));\nvar UserStatus;\n(function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n    UserStatus[\"PENDING_VERIFICATION\"] = \"pending_verification\";\n})(UserStatus || (UserStatus = {}));\nvar LanguageLevel;\n(function(LanguageLevel) {\n    LanguageLevel[\"BEGINNER\"] = \"beginner\";\n    LanguageLevel[\"ELEMENTARY\"] = \"elementary\";\n    LanguageLevel[\"INTERMEDIATE\"] = \"intermediate\";\n    LanguageLevel[\"UPPER_INTERMEDIATE\"] = \"upper_intermediate\";\n    LanguageLevel[\"ADVANCED\"] = \"advanced\";\n    LanguageLevel[\"PROFICIENT\"] = \"proficient\";\n})(LanguageLevel || (LanguageLevel = {}));\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: [\n            true,\n            \"Email l\\xe0 bắt buộc\"\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            \"Email kh\\xf4ng hợp lệ\"\n        ]\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"Mật khẩu l\\xe0 bắt buộc\"\n        ],\n        minlength: [\n            8,\n            \"Mật khẩu phải c\\xf3 \\xedt nhất 8 k\\xfd tự\"\n        ],\n        select: false // Không trả về password khi query\n    },\n    role: {\n        type: String,\n        enum: Object.values(UserRole),\n        default: \"student\"\n    },\n    status: {\n        type: String,\n        enum: Object.values(UserStatus),\n        default: \"pending_verification\"\n    },\n    profile: {\n        firstName: {\n            type: String,\n            required: [\n                true,\n                \"T\\xean l\\xe0 bắt buộc\"\n            ],\n            trim: true,\n            maxlength: [\n                50,\n                \"T\\xean kh\\xf4ng được vượt qu\\xe1 50 k\\xfd tự\"\n            ]\n        },\n        lastName: {\n            type: String,\n            required: [\n                true,\n                \"Họ l\\xe0 bắt buộc\"\n            ],\n            trim: true,\n            maxlength: [\n                50,\n                \"Họ kh\\xf4ng được vượt qu\\xe1 50 k\\xfd tự\"\n            ]\n        },\n        dateOfBirth: Date,\n        phoneNumber: {\n            type: String,\n            match: [\n                /^[+]?[\\d\\s\\-\\(\\)]+$/,\n                \"Số điện thoại kh\\xf4ng hợp lệ\"\n            ]\n        },\n        address: {\n            street: String,\n            city: String,\n            state: String,\n            country: String,\n            zipCode: String\n        },\n        avatar: String,\n        bio: {\n            type: String,\n            maxlength: [\n                500,\n                \"Bio kh\\xf4ng được vượt qu\\xe1 500 k\\xfd tự\"\n            ]\n        },\n        languagePreferences: {\n            native: [\n                String\n            ],\n            learning: [\n                String\n            ],\n            currentLevel: {\n                type: String,\n                enum: Object.values(LanguageLevel)\n            }\n        },\n        timezone: String\n    },\n    preferences: {\n        notifications: {\n            email: {\n                type: Boolean,\n                default: true\n            },\n            push: {\n                type: Boolean,\n                default: true\n            },\n            sms: {\n                type: Boolean,\n                default: false\n            }\n        },\n        privacy: {\n            profileVisibility: {\n                type: String,\n                enum: [\n                    \"public\",\n                    \"private\",\n                    \"friends\"\n                ],\n                default: \"public\"\n            },\n            showProgress: {\n                type: Boolean,\n                default: true\n            },\n            showAchievements: {\n                type: Boolean,\n                default: true\n            }\n        },\n        learning: {\n            dailyGoal: {\n                type: Number,\n                min: 5,\n                max: 480\n            },\n            reminderTime: String,\n            preferredDifficulty: {\n                type: String,\n                enum: [\n                    \"easy\",\n                    \"medium\",\n                    \"hard\"\n                ],\n                default: \"medium\"\n            }\n        }\n    },\n    emailVerified: {\n        type: Boolean,\n        default: false\n    },\n    emailVerificationToken: String,\n    passwordResetToken: String,\n    passwordResetExpires: Date,\n    lastLogin: Date,\n    loginAttempts: {\n        type: Number,\n        default: 0\n    },\n    lockUntil: Date\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\n// Note: email index is automatically created by unique: true in schema\nUserSchema.index({\n    \"profile.firstName\": 1,\n    \"profile.lastName\": 1\n});\nUserSchema.index({\n    role: 1,\n    status: 1\n});\nUserSchema.index({\n    createdAt: -1\n});\n// Virtual fields\nUserSchema.virtual(\"profile.fullName\").get(function() {\n    return `${this.profile.firstName} ${this.profile.lastName}`;\n});\nUserSchema.virtual(\"isAccountLocked\").get(function() {\n    return !!(this.lockUntil && this.lockUntil.getTime() > Date.now());\n});\n// Pre-save middleware\nUserSchema.pre(\"save\", async function(next) {\n    if (!this.isModified(\"password\")) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(candidatePassword, this.password);\n};\nUserSchema.methods.generatePasswordResetToken = function() {\n    const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.passwordResetToken = resetToken;\n    this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes\n    ;\n    return resetToken;\n};\nUserSchema.methods.generateEmailVerificationToken = function() {\n    const verificationToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.emailVerificationToken = verificationToken;\n    return verificationToken;\n};\nUserSchema.methods.isLocked = function() {\n    return !!(this.lockUntil && this.lockUntil > Date.now());\n};\nUserSchema.methods.incrementLoginAttempts = async function() {\n    // If we have a previous lock that has expired, restart at 1\n    if (this.lockUntil && this.lockUntil < Date.now()) {\n        return this.updateOne({\n            $unset: {\n                lockUntil: 1\n            },\n            $set: {\n                loginAttempts: 1\n            }\n        });\n    }\n    const updates = {\n        $inc: {\n            loginAttempts: 1\n        }\n    };\n    // Lock account after 5 failed attempts for 2 hours\n    if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {\n        updates.$set = {\n            lockUntil: Date.now() + 2 * 60 * 60 * 1000\n        } // 2 hours\n        ;\n    }\n    return this.updateOne(updates);\n};\n// Export model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbW9kZWxzL1VzZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBcUQ7QUFDeEI7O1VBR2pCRzs7Ozs7R0FBQUEsYUFBQUE7O1VBT0FDOzs7OztHQUFBQSxlQUFBQTs7VUFPQUM7Ozs7Ozs7R0FBQUEsa0JBQUFBO0FBMkVaLE1BQU1DLGFBQWEsSUFBSUwsNENBQU1BLENBQVE7SUFDbkNNLE9BQU87UUFDTEMsTUFBTUM7UUFDTkMsVUFBVTtZQUFDO1lBQU07U0FBb0I7UUFDckNDLFFBQVE7UUFDUkMsV0FBVztRQUNYQyxNQUFNO1FBQ05DLE9BQU87WUFBQztZQUErQztTQUFxQjtJQUM5RTtJQUNBQyxVQUFVO1FBQ1JQLE1BQU1DO1FBQ05DLFVBQVU7WUFBQztZQUFNO1NBQXVCO1FBQ3hDTSxXQUFXO1lBQUM7WUFBRztTQUFtQztRQUNsREMsUUFBUSxNQUFNLGtDQUFrQztJQUNsRDtJQUNBQyxNQUFNO1FBQ0pWLE1BQU1DO1FBQ05VLE1BQU1DLE9BQU9DLE1BQU0sQ0FBQ2xCO1FBQ3BCbUIsT0FBTztJQUNUO0lBQ0FDLFFBQVE7UUFDTmYsTUFBTUM7UUFDTlUsTUFBTUMsT0FBT0MsTUFBTSxDQUFDakI7UUFDcEJrQixPQUFPO0lBQ1Q7SUFDQUUsU0FBUztRQUNQQyxXQUFXO1lBQ1RqQixNQUFNQztZQUNOQyxVQUFVO2dCQUFDO2dCQUFNO2FBQWtCO1lBQ25DRyxNQUFNO1lBQ05hLFdBQVc7Z0JBQUM7Z0JBQUk7YUFBbUM7UUFDckQ7UUFDQUMsVUFBVTtZQUNSbkIsTUFBTUM7WUFDTkMsVUFBVTtnQkFBQztnQkFBTTthQUFpQjtZQUNsQ0csTUFBTTtZQUNOYSxXQUFXO2dCQUFDO2dCQUFJO2FBQWtDO1FBQ3BEO1FBQ0FFLGFBQWFDO1FBQ2JDLGFBQWE7WUFDWHRCLE1BQU1DO1lBQ05LLE9BQU87Z0JBQUM7Z0JBQXVCO2FBQTZCO1FBQzlEO1FBQ0FpQixTQUFTO1lBQ1BDLFFBQVF2QjtZQUNSd0IsTUFBTXhCO1lBQ055QixPQUFPekI7WUFDUDBCLFNBQVMxQjtZQUNUMkIsU0FBUzNCO1FBQ1g7UUFDQTRCLFFBQVE1QjtRQUNSNkIsS0FBSztZQUNIOUIsTUFBTUM7WUFDTmlCLFdBQVc7Z0JBQUM7Z0JBQUs7YUFBb0M7UUFDdkQ7UUFDQWEscUJBQXFCO1lBQ25CQyxRQUFRO2dCQUFDL0I7YUFBTztZQUNoQmdDLFVBQVU7Z0JBQUNoQzthQUFPO1lBQ2xCaUMsY0FBYztnQkFDWmxDLE1BQU1DO2dCQUNOVSxNQUFNQyxPQUFPQyxNQUFNLENBQUNoQjtZQUN0QjtRQUNGO1FBQ0FzQyxVQUFVbEM7SUFDWjtJQUNBbUMsYUFBYTtRQUNYQyxlQUFlO1lBQ2J0QyxPQUFPO2dCQUFFQyxNQUFNc0M7Z0JBQVN4QixTQUFTO1lBQUs7WUFDdEN5QixNQUFNO2dCQUFFdkMsTUFBTXNDO2dCQUFTeEIsU0FBUztZQUFLO1lBQ3JDMEIsS0FBSztnQkFBRXhDLE1BQU1zQztnQkFBU3hCLFNBQVM7WUFBTTtRQUN2QztRQUNBMkIsU0FBUztZQUNQQyxtQkFBbUI7Z0JBQ2pCMUMsTUFBTUM7Z0JBQ05VLE1BQU07b0JBQUM7b0JBQVU7b0JBQVc7aUJBQVU7Z0JBQ3RDRyxTQUFTO1lBQ1g7WUFDQTZCLGNBQWM7Z0JBQUUzQyxNQUFNc0M7Z0JBQVN4QixTQUFTO1lBQUs7WUFDN0M4QixrQkFBa0I7Z0JBQUU1QyxNQUFNc0M7Z0JBQVN4QixTQUFTO1lBQUs7UUFDbkQ7UUFDQW1CLFVBQVU7WUFDUlksV0FBVztnQkFBRTdDLE1BQU04QztnQkFBUUMsS0FBSztnQkFBR0MsS0FBSztZQUFJO1lBQzVDQyxjQUFjaEQ7WUFDZGlELHFCQUFxQjtnQkFDbkJsRCxNQUFNQztnQkFDTlUsTUFBTTtvQkFBQztvQkFBUTtvQkFBVTtpQkFBTztnQkFDaENHLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFDQXFDLGVBQWU7UUFBRW5ELE1BQU1zQztRQUFTeEIsU0FBUztJQUFNO0lBQy9Dc0Msd0JBQXdCbkQ7SUFDeEJvRCxvQkFBb0JwRDtJQUNwQnFELHNCQUFzQmpDO0lBQ3RCa0MsV0FBV2xDO0lBQ1htQyxlQUFlO1FBQUV4RCxNQUFNOEM7UUFBUWhDLFNBQVM7SUFBRTtJQUMxQzJDLFdBQVdwQztBQUNiLEdBQUc7SUFDRHFDLFlBQVk7SUFDWkMsUUFBUTtRQUFFQyxVQUFVO0lBQUs7SUFDekJDLFVBQVU7UUFBRUQsVUFBVTtJQUFLO0FBQzdCO0FBRUEsVUFBVTtBQUNWLHVFQUF1RTtBQUN2RTlELFdBQVdnRSxLQUFLLENBQUM7SUFBRSxxQkFBcUI7SUFBRyxvQkFBb0I7QUFBRTtBQUNqRWhFLFdBQVdnRSxLQUFLLENBQUM7SUFBRXBELE1BQU07SUFBR0ssUUFBUTtBQUFFO0FBQ3RDakIsV0FBV2dFLEtBQUssQ0FBQztJQUFFQyxXQUFXLENBQUM7QUFBRTtBQUVqQyxpQkFBaUI7QUFDakJqRSxXQUFXa0UsT0FBTyxDQUFDLG9CQUFvQkMsR0FBRyxDQUFDO0lBQ3pDLE9BQU8sQ0FBQyxFQUFFLElBQUksQ0FBQ2pELE9BQU8sQ0FBQ0MsU0FBUyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUNELE9BQU8sQ0FBQ0csUUFBUSxDQUFDLENBQUM7QUFDN0Q7QUFFQXJCLFdBQVdrRSxPQUFPLENBQUMsbUJBQW1CQyxHQUFHLENBQUM7SUFDeEMsT0FBTyxDQUFDLENBQUUsS0FBSSxDQUFDUixTQUFTLElBQUksSUFBSSxDQUFDQSxTQUFTLENBQUNTLE9BQU8sS0FBSzdDLEtBQUs4QyxHQUFHLEVBQUM7QUFDbEU7QUFFQSxzQkFBc0I7QUFDdEJyRSxXQUFXc0UsR0FBRyxDQUFDLFFBQVEsZUFBZUMsSUFBSTtJQUN4QyxJQUFJLENBQUMsSUFBSSxDQUFDQyxVQUFVLENBQUMsYUFBYSxPQUFPRDtJQUV6QyxJQUFJO1FBQ0YsTUFBTUUsT0FBTyxNQUFNN0UsdURBQWMsQ0FBQztRQUNsQyxJQUFJLENBQUNhLFFBQVEsR0FBRyxNQUFNYixvREFBVyxDQUFDLElBQUksQ0FBQ2EsUUFBUSxFQUFFZ0U7UUFDakRGO0lBQ0YsRUFBRSxPQUFPSyxPQUFZO1FBQ25CTCxLQUFLSztJQUNQO0FBQ0Y7QUFFQSxtQkFBbUI7QUFDbkI1RSxXQUFXNkUsT0FBTyxDQUFDQyxlQUFlLEdBQUcsZUFBZUMsaUJBQXlCO0lBQzNFLE9BQU9uRix1REFBYyxDQUFDbUYsbUJBQW1CLElBQUksQ0FBQ3RFLFFBQVE7QUFDeEQ7QUFFQVQsV0FBVzZFLE9BQU8sQ0FBQ0ksMEJBQTBCLEdBQUc7SUFDOUMsTUFBTUMsYUFBYUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUcsTUFBTUgsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7SUFDekcsSUFBSSxDQUFDL0Isa0JBQWtCLEdBQUcyQjtJQUMxQixJQUFJLENBQUMxQixvQkFBb0IsR0FBRyxJQUFJakMsS0FBS0EsS0FBSzhDLEdBQUcsS0FBSyxLQUFLLEtBQUssTUFBTSxhQUFhOztJQUMvRSxPQUFPYTtBQUNUO0FBRUFsRixXQUFXNkUsT0FBTyxDQUFDVSw4QkFBOEIsR0FBRztJQUNsRCxNQUFNQyxvQkFBb0JMLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHLE1BQU1ILEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO0lBQ2hILElBQUksQ0FBQ2hDLHNCQUFzQixHQUFHa0M7SUFDOUIsT0FBT0E7QUFDVDtBQUVBeEYsV0FBVzZFLE9BQU8sQ0FBQ1ksUUFBUSxHQUFHO0lBQzVCLE9BQU8sQ0FBQyxDQUFFLEtBQUksQ0FBQzlCLFNBQVMsSUFBSSxJQUFJLENBQUNBLFNBQVMsR0FBR3BDLEtBQUs4QyxHQUFHLEVBQUM7QUFDeEQ7QUFFQXJFLFdBQVc2RSxPQUFPLENBQUNhLHNCQUFzQixHQUFHO0lBQzFDLDREQUE0RDtJQUM1RCxJQUFJLElBQUksQ0FBQy9CLFNBQVMsSUFBSSxJQUFJLENBQUNBLFNBQVMsR0FBR3BDLEtBQUs4QyxHQUFHLElBQUk7UUFDakQsT0FBTyxJQUFJLENBQUNzQixTQUFTLENBQUM7WUFDcEJDLFFBQVE7Z0JBQUVqQyxXQUFXO1lBQUU7WUFDdkJrQyxNQUFNO2dCQUFFbkMsZUFBZTtZQUFFO1FBQzNCO0lBQ0Y7SUFFQSxNQUFNb0MsVUFBZTtRQUFFQyxNQUFNO1lBQUVyQyxlQUFlO1FBQUU7SUFBRTtJQUVsRCxtREFBbUQ7SUFDbkQsSUFBSSxJQUFJLENBQUNBLGFBQWEsR0FBRyxLQUFLLEtBQUssQ0FBQyxJQUFJLENBQUMrQixRQUFRLElBQUk7UUFDbkRLLFFBQVFELElBQUksR0FBRztZQUFFbEMsV0FBV3BDLEtBQUs4QyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUs7UUFBSyxFQUFFLFVBQVU7O0lBQzFFO0lBRUEsT0FBTyxJQUFJLENBQUNzQixTQUFTLENBQUNHO0FBQ3hCO0FBRUEsZUFBZTtBQUNmLGlFQUFlcEcsd0RBQWUsQ0FBQ3VHLElBQUksSUFBSXZHLHFEQUFjLENBQVEsUUFBUU0sV0FBV0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL3NyYy9tb2RlbHMvVXNlci50cz8wOTZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtb25nb29zZSwgeyBEb2N1bWVudCwgU2NoZW1hIH0gZnJvbSAnbW9uZ29vc2UnXG5pbXBvcnQgYmNyeXB0IGZyb20gJ2JjcnlwdGpzJ1xuXG4vLyBFbnVtIGRlZmluaXRpb25zXG5leHBvcnQgZW51bSBVc2VyUm9sZSB7XG4gIFNUVURFTlQgPSAnc3R1ZGVudCcsXG4gIElOU1RSVUNUT1IgPSAnaW5zdHJ1Y3RvcicsXG4gIEFETUlOID0gJ2FkbWluJyxcbiAgU1VQRVJfQURNSU4gPSAnc3VwZXJfYWRtaW4nXG59XG5cbmV4cG9ydCBlbnVtIFVzZXJTdGF0dXMge1xuICBBQ1RJVkUgPSAnYWN0aXZlJyxcbiAgSU5BQ1RJVkUgPSAnaW5hY3RpdmUnLFxuICBTVVNQRU5ERUQgPSAnc3VzcGVuZGVkJyxcbiAgUEVORElOR19WRVJJRklDQVRJT04gPSAncGVuZGluZ192ZXJpZmljYXRpb24nXG59XG5cbmV4cG9ydCBlbnVtIExhbmd1YWdlTGV2ZWwge1xuICBCRUdJTk5FUiA9ICdiZWdpbm5lcicsXG4gIEVMRU1FTlRBUlkgPSAnZWxlbWVudGFyeScsXG4gIElOVEVSTUVESUFURSA9ICdpbnRlcm1lZGlhdGUnLFxuICBVUFBFUl9JTlRFUk1FRElBVEUgPSAndXBwZXJfaW50ZXJtZWRpYXRlJyxcbiAgQURWQU5DRUQgPSAnYWR2YW5jZWQnLFxuICBQUk9GSUNJRU5UID0gJ3Byb2ZpY2llbnQnXG59XG5cbi8vIEludGVyZmFjZSBkZWZpbml0aW9uc1xuZXhwb3J0IGludGVyZmFjZSBJVXNlclByb2ZpbGUge1xuICBmaXJzdE5hbWU6IHN0cmluZ1xuICBsYXN0TmFtZTogc3RyaW5nXG4gIGRhdGVPZkJpcnRoPzogRGF0ZVxuICBwaG9uZU51bWJlcj86IHN0cmluZ1xuICBhZGRyZXNzPzoge1xuICAgIHN0cmVldD86IHN0cmluZ1xuICAgIGNpdHk/OiBzdHJpbmdcbiAgICBzdGF0ZT86IHN0cmluZ1xuICAgIGNvdW50cnk/OiBzdHJpbmdcbiAgICB6aXBDb2RlPzogc3RyaW5nXG4gIH1cbiAgYXZhdGFyPzogc3RyaW5nXG4gIGJpbz86IHN0cmluZ1xuICBsYW5ndWFnZVByZWZlcmVuY2VzOiB7XG4gICAgbmF0aXZlOiBzdHJpbmdbXVxuICAgIGxlYXJuaW5nOiBzdHJpbmdbXVxuICAgIGN1cnJlbnRMZXZlbD86IExhbmd1YWdlTGV2ZWxcbiAgfVxuICB0aW1lem9uZT86IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIElVc2VyUHJlZmVyZW5jZXMge1xuICBub3RpZmljYXRpb25zOiB7XG4gICAgZW1haWw6IGJvb2xlYW5cbiAgICBwdXNoOiBib29sZWFuXG4gICAgc21zOiBib29sZWFuXG4gIH1cbiAgcHJpdmFjeToge1xuICAgIHByb2ZpbGVWaXNpYmlsaXR5OiAncHVibGljJyB8ICdwcml2YXRlJyB8ICdmcmllbmRzJ1xuICAgIHNob3dQcm9ncmVzczogYm9vbGVhblxuICAgIHNob3dBY2hpZXZlbWVudHM6IGJvb2xlYW5cbiAgfVxuICBsZWFybmluZzoge1xuICAgIGRhaWx5R29hbD86IG51bWJlciAvLyBtaW51dGVzIHBlciBkYXlcbiAgICByZW1pbmRlclRpbWU/OiBzdHJpbmcgLy8gSEg6TU0gZm9ybWF0XG4gICAgcHJlZmVycmVkRGlmZmljdWx0eT86ICdlYXN5JyB8ICdtZWRpdW0nIHwgJ2hhcmQnXG4gIH1cbn1cblxuZXhwb3J0IGludGVyZmFjZSBJVXNlciBleHRlbmRzIERvY3VtZW50IHtcbiAgZW1haWw6IHN0cmluZ1xuICBwYXNzd29yZDogc3RyaW5nXG4gIHJvbGU6IFVzZXJSb2xlXG4gIHN0YXR1czogVXNlclN0YXR1c1xuICBwcm9maWxlOiBJVXNlclByb2ZpbGVcbiAgcHJlZmVyZW5jZXM6IElVc2VyUHJlZmVyZW5jZXNcbiAgZW1haWxWZXJpZmllZDogYm9vbGVhblxuICBlbWFpbFZlcmlmaWNhdGlvblRva2VuPzogc3RyaW5nXG4gIHBhc3N3b3JkUmVzZXRUb2tlbj86IHN0cmluZ1xuICBwYXNzd29yZFJlc2V0RXhwaXJlcz86IERhdGVcbiAgbGFzdExvZ2luPzogRGF0ZVxuICBsb2dpbkF0dGVtcHRzOiBudW1iZXJcbiAgbG9ja1VudGlsPzogRGF0ZVxuICBjcmVhdGVkQXQ6IERhdGVcbiAgdXBkYXRlZEF0OiBEYXRlXG4gIFxuICAvLyBNZXRob2RzXG4gIGNvbXBhcmVQYXNzd29yZChjYW5kaWRhdGVQYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPlxuICBnZW5lcmF0ZVBhc3N3b3JkUmVzZXRUb2tlbigpOiBzdHJpbmdcbiAgZ2VuZXJhdGVFbWFpbFZlcmlmaWNhdGlvblRva2VuKCk6IHN0cmluZ1xuICBpc0xvY2tlZCgpOiBib29sZWFuXG4gIGluY3JlbWVudExvZ2luQXR0ZW1wdHMoKTogUHJvbWlzZTx2b2lkPlxufVxuXG5jb25zdCBVc2VyU2NoZW1hID0gbmV3IFNjaGVtYTxJVXNlcj4oe1xuICBlbWFpbDoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogW3RydWUsICdFbWFpbCBsw6AgYuG6r3QgYnXhu5ljJ10sXG4gICAgdW5pcXVlOiB0cnVlLFxuICAgIGxvd2VyY2FzZTogdHJ1ZSxcbiAgICB0cmltOiB0cnVlLFxuICAgIG1hdGNoOiBbL15cXHcrKFsuLV0/XFx3KykqQFxcdysoWy4tXT9cXHcrKSooXFwuXFx3ezIsM30pKyQvLCAnRW1haWwga2jDtG5nIGjhu6NwIGzhu4cnXVxuICB9LFxuICBwYXNzd29yZDoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogW3RydWUsICdN4bqtdCBraOG6qXUgbMOgIGLhuq90IGJ14buZYyddLFxuICAgIG1pbmxlbmd0aDogWzgsICdN4bqtdCBraOG6qXUgcGjhuqNpIGPDsyDDrXQgbmjhuqV0IDgga8O9IHThu7EnXSxcbiAgICBzZWxlY3Q6IGZhbHNlIC8vIEtow7RuZyB0cuG6oyB24buBIHBhc3N3b3JkIGtoaSBxdWVyeVxuICB9LFxuICByb2xlOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGVudW06IE9iamVjdC52YWx1ZXMoVXNlclJvbGUpLFxuICAgIGRlZmF1bHQ6IFVzZXJSb2xlLlNUVURFTlRcbiAgfSxcbiAgc3RhdHVzOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGVudW06IE9iamVjdC52YWx1ZXMoVXNlclN0YXR1cyksXG4gICAgZGVmYXVsdDogVXNlclN0YXR1cy5QRU5ESU5HX1ZFUklGSUNBVElPTlxuICB9LFxuICBwcm9maWxlOiB7XG4gICAgZmlyc3ROYW1lOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdUw6puIGzDoCBi4bqvdCBideG7mWMnXSxcbiAgICAgIHRyaW06IHRydWUsXG4gICAgICBtYXhsZW5ndGg6IFs1MCwgJ1TDqm4ga2jDtG5nIMSRxrDhu6NjIHbGsOG7o3QgcXXDoSA1MCBrw70gdOG7sSddXG4gICAgfSxcbiAgICBsYXN0TmFtZToge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgcmVxdWlyZWQ6IFt0cnVlLCAnSOG7jSBsw6AgYuG6r3QgYnXhu5ljJ10sXG4gICAgICB0cmltOiB0cnVlLFxuICAgICAgbWF4bGVuZ3RoOiBbNTAsICdI4buNIGtow7RuZyDEkcaw4bujYyB2xrDhu6N0IHF1w6EgNTAga8O9IHThu7EnXVxuICAgIH0sXG4gICAgZGF0ZU9mQmlydGg6IERhdGUsXG4gICAgcGhvbmVOdW1iZXI6IHtcbiAgICAgIHR5cGU6IFN0cmluZyxcbiAgICAgIG1hdGNoOiBbL15bK10/W1xcZFxcc1xcLVxcKFxcKV0rJC8sICdT4buRIMSRaeG7h24gdGhv4bqhaSBraMO0bmcgaOG7o3AgbOG7hyddXG4gICAgfSxcbiAgICBhZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6IFN0cmluZyxcbiAgICAgIGNpdHk6IFN0cmluZyxcbiAgICAgIHN0YXRlOiBTdHJpbmcsXG4gICAgICBjb3VudHJ5OiBTdHJpbmcsXG4gICAgICB6aXBDb2RlOiBTdHJpbmdcbiAgICB9LFxuICAgIGF2YXRhcjogU3RyaW5nLFxuICAgIGJpbzoge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgbWF4bGVuZ3RoOiBbNTAwLCAnQmlvIGtow7RuZyDEkcaw4bujYyB2xrDhu6N0IHF1w6EgNTAwIGvDvSB04buxJ11cbiAgICB9LFxuICAgIGxhbmd1YWdlUHJlZmVyZW5jZXM6IHtcbiAgICAgIG5hdGl2ZTogW1N0cmluZ10sXG4gICAgICBsZWFybmluZzogW1N0cmluZ10sXG4gICAgICBjdXJyZW50TGV2ZWw6IHtcbiAgICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgICBlbnVtOiBPYmplY3QudmFsdWVzKExhbmd1YWdlTGV2ZWwpXG4gICAgICB9XG4gICAgfSxcbiAgICB0aW1lem9uZTogU3RyaW5nXG4gIH0sXG4gIHByZWZlcmVuY2VzOiB7XG4gICAgbm90aWZpY2F0aW9uczoge1xuICAgICAgZW1haWw6IHsgdHlwZTogQm9vbGVhbiwgZGVmYXVsdDogdHJ1ZSB9LFxuICAgICAgcHVzaDogeyB0eXBlOiBCb29sZWFuLCBkZWZhdWx0OiB0cnVlIH0sXG4gICAgICBzbXM6IHsgdHlwZTogQm9vbGVhbiwgZGVmYXVsdDogZmFsc2UgfVxuICAgIH0sXG4gICAgcHJpdmFjeToge1xuICAgICAgcHJvZmlsZVZpc2liaWxpdHk6IHtcbiAgICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgICBlbnVtOiBbJ3B1YmxpYycsICdwcml2YXRlJywgJ2ZyaWVuZHMnXSxcbiAgICAgICAgZGVmYXVsdDogJ3B1YmxpYydcbiAgICAgIH0sXG4gICAgICBzaG93UHJvZ3Jlc3M6IHsgdHlwZTogQm9vbGVhbiwgZGVmYXVsdDogdHJ1ZSB9LFxuICAgICAgc2hvd0FjaGlldmVtZW50czogeyB0eXBlOiBCb29sZWFuLCBkZWZhdWx0OiB0cnVlIH1cbiAgICB9LFxuICAgIGxlYXJuaW5nOiB7XG4gICAgICBkYWlseUdvYWw6IHsgdHlwZTogTnVtYmVyLCBtaW46IDUsIG1heDogNDgwIH0sIC8vIDUgbWludXRlcyB0byA4IGhvdXJzXG4gICAgICByZW1pbmRlclRpbWU6IFN0cmluZyxcbiAgICAgIHByZWZlcnJlZERpZmZpY3VsdHk6IHtcbiAgICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgICBlbnVtOiBbJ2Vhc3knLCAnbWVkaXVtJywgJ2hhcmQnXSxcbiAgICAgICAgZGVmYXVsdDogJ21lZGl1bSdcbiAgICAgIH1cbiAgICB9XG4gIH0sXG4gIGVtYWlsVmVyaWZpZWQ6IHsgdHlwZTogQm9vbGVhbiwgZGVmYXVsdDogZmFsc2UgfSxcbiAgZW1haWxWZXJpZmljYXRpb25Ub2tlbjogU3RyaW5nLFxuICBwYXNzd29yZFJlc2V0VG9rZW46IFN0cmluZyxcbiAgcGFzc3dvcmRSZXNldEV4cGlyZXM6IERhdGUsXG4gIGxhc3RMb2dpbjogRGF0ZSxcbiAgbG9naW5BdHRlbXB0czogeyB0eXBlOiBOdW1iZXIsIGRlZmF1bHQ6IDAgfSxcbiAgbG9ja1VudGlsOiBEYXRlXG59LCB7XG4gIHRpbWVzdGFtcHM6IHRydWUsXG4gIHRvSlNPTjogeyB2aXJ0dWFsczogdHJ1ZSB9LFxuICB0b09iamVjdDogeyB2aXJ0dWFsczogdHJ1ZSB9XG59KVxuXG4vLyBJbmRleGVzXG4vLyBOb3RlOiBlbWFpbCBpbmRleCBpcyBhdXRvbWF0aWNhbGx5IGNyZWF0ZWQgYnkgdW5pcXVlOiB0cnVlIGluIHNjaGVtYVxuVXNlclNjaGVtYS5pbmRleCh7ICdwcm9maWxlLmZpcnN0TmFtZSc6IDEsICdwcm9maWxlLmxhc3ROYW1lJzogMSB9KVxuVXNlclNjaGVtYS5pbmRleCh7IHJvbGU6IDEsIHN0YXR1czogMSB9KVxuVXNlclNjaGVtYS5pbmRleCh7IGNyZWF0ZWRBdDogLTEgfSlcblxuLy8gVmlydHVhbCBmaWVsZHNcblVzZXJTY2hlbWEudmlydHVhbCgncHJvZmlsZS5mdWxsTmFtZScpLmdldChmdW5jdGlvbigpIHtcbiAgcmV0dXJuIGAke3RoaXMucHJvZmlsZS5maXJzdE5hbWV9ICR7dGhpcy5wcm9maWxlLmxhc3ROYW1lfWBcbn0pXG5cblVzZXJTY2hlbWEudmlydHVhbCgnaXNBY2NvdW50TG9ja2VkJykuZ2V0KGZ1bmN0aW9uKCkge1xuICByZXR1cm4gISEodGhpcy5sb2NrVW50aWwgJiYgdGhpcy5sb2NrVW50aWwuZ2V0VGltZSgpID4gRGF0ZS5ub3coKSlcbn0pXG5cbi8vIFByZS1zYXZlIG1pZGRsZXdhcmVcblVzZXJTY2hlbWEucHJlKCdzYXZlJywgYXN5bmMgZnVuY3Rpb24obmV4dCkge1xuICBpZiAoIXRoaXMuaXNNb2RpZmllZCgncGFzc3dvcmQnKSkgcmV0dXJuIG5leHQoKVxuICBcbiAgdHJ5IHtcbiAgICBjb25zdCBzYWx0ID0gYXdhaXQgYmNyeXB0LmdlblNhbHQoMTIpXG4gICAgdGhpcy5wYXNzd29yZCA9IGF3YWl0IGJjcnlwdC5oYXNoKHRoaXMucGFzc3dvcmQsIHNhbHQpXG4gICAgbmV4dCgpXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBuZXh0KGVycm9yKVxuICB9XG59KVxuXG4vLyBJbnN0YW5jZSBtZXRob2RzXG5Vc2VyU2NoZW1hLm1ldGhvZHMuY29tcGFyZVBhc3N3b3JkID0gYXN5bmMgZnVuY3Rpb24oY2FuZGlkYXRlUGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICByZXR1cm4gYmNyeXB0LmNvbXBhcmUoY2FuZGlkYXRlUGFzc3dvcmQsIHRoaXMucGFzc3dvcmQpXG59XG5cblVzZXJTY2hlbWEubWV0aG9kcy5nZW5lcmF0ZVBhc3N3b3JkUmVzZXRUb2tlbiA9IGZ1bmN0aW9uKCk6IHN0cmluZyB7XG4gIGNvbnN0IHJlc2V0VG9rZW4gPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTUpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDE1KVxuICB0aGlzLnBhc3N3b3JkUmVzZXRUb2tlbiA9IHJlc2V0VG9rZW5cbiAgdGhpcy5wYXNzd29yZFJlc2V0RXhwaXJlcyA9IG5ldyBEYXRlKERhdGUubm93KCkgKyAxMCAqIDYwICogMTAwMCkgLy8gMTAgbWludXRlc1xuICByZXR1cm4gcmVzZXRUb2tlblxufVxuXG5Vc2VyU2NoZW1hLm1ldGhvZHMuZ2VuZXJhdGVFbWFpbFZlcmlmaWNhdGlvblRva2VuID0gZnVuY3Rpb24oKTogc3RyaW5nIHtcbiAgY29uc3QgdmVyaWZpY2F0aW9uVG9rZW4gPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTUpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDE1KVxuICB0aGlzLmVtYWlsVmVyaWZpY2F0aW9uVG9rZW4gPSB2ZXJpZmljYXRpb25Ub2tlblxuICByZXR1cm4gdmVyaWZpY2F0aW9uVG9rZW5cbn1cblxuVXNlclNjaGVtYS5tZXRob2RzLmlzTG9ja2VkID0gZnVuY3Rpb24oKTogYm9vbGVhbiB7XG4gIHJldHVybiAhISh0aGlzLmxvY2tVbnRpbCAmJiB0aGlzLmxvY2tVbnRpbCA+IERhdGUubm93KCkpXG59XG5cblVzZXJTY2hlbWEubWV0aG9kcy5pbmNyZW1lbnRMb2dpbkF0dGVtcHRzID0gYXN5bmMgZnVuY3Rpb24oKTogUHJvbWlzZTx2b2lkPiB7XG4gIC8vIElmIHdlIGhhdmUgYSBwcmV2aW91cyBsb2NrIHRoYXQgaGFzIGV4cGlyZWQsIHJlc3RhcnQgYXQgMVxuICBpZiAodGhpcy5sb2NrVW50aWwgJiYgdGhpcy5sb2NrVW50aWwgPCBEYXRlLm5vdygpKSB7XG4gICAgcmV0dXJuIHRoaXMudXBkYXRlT25lKHtcbiAgICAgICR1bnNldDogeyBsb2NrVW50aWw6IDEgfSxcbiAgICAgICRzZXQ6IHsgbG9naW5BdHRlbXB0czogMSB9XG4gICAgfSlcbiAgfVxuICBcbiAgY29uc3QgdXBkYXRlczogYW55ID0geyAkaW5jOiB7IGxvZ2luQXR0ZW1wdHM6IDEgfSB9XG4gIFxuICAvLyBMb2NrIGFjY291bnQgYWZ0ZXIgNSBmYWlsZWQgYXR0ZW1wdHMgZm9yIDIgaG91cnNcbiAgaWYgKHRoaXMubG9naW5BdHRlbXB0cyArIDEgPj0gNSAmJiAhdGhpcy5pc0xvY2tlZCgpKSB7XG4gICAgdXBkYXRlcy4kc2V0ID0geyBsb2NrVW50aWw6IERhdGUubm93KCkgKyAyICogNjAgKiA2MCAqIDEwMDAgfSAvLyAyIGhvdXJzXG4gIH1cbiAgXG4gIHJldHVybiB0aGlzLnVwZGF0ZU9uZSh1cGRhdGVzKVxufVxuXG4vLyBFeHBvcnQgbW9kZWxcbmV4cG9ydCBkZWZhdWx0IG1vbmdvb3NlLm1vZGVscy5Vc2VyIHx8IG1vbmdvb3NlLm1vZGVsPElVc2VyPignVXNlcicsIFVzZXJTY2hlbWEpXG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJTY2hlbWEiLCJiY3J5cHQiLCJVc2VyUm9sZSIsIlVzZXJTdGF0dXMiLCJMYW5ndWFnZUxldmVsIiwiVXNlclNjaGVtYSIsImVtYWlsIiwidHlwZSIsIlN0cmluZyIsInJlcXVpcmVkIiwidW5pcXVlIiwibG93ZXJjYXNlIiwidHJpbSIsIm1hdGNoIiwicGFzc3dvcmQiLCJtaW5sZW5ndGgiLCJzZWxlY3QiLCJyb2xlIiwiZW51bSIsIk9iamVjdCIsInZhbHVlcyIsImRlZmF1bHQiLCJzdGF0dXMiLCJwcm9maWxlIiwiZmlyc3ROYW1lIiwibWF4bGVuZ3RoIiwibGFzdE5hbWUiLCJkYXRlT2ZCaXJ0aCIsIkRhdGUiLCJwaG9uZU51bWJlciIsImFkZHJlc3MiLCJzdHJlZXQiLCJjaXR5Iiwic3RhdGUiLCJjb3VudHJ5IiwiemlwQ29kZSIsImF2YXRhciIsImJpbyIsImxhbmd1YWdlUHJlZmVyZW5jZXMiLCJuYXRpdmUiLCJsZWFybmluZyIsImN1cnJlbnRMZXZlbCIsInRpbWV6b25lIiwicHJlZmVyZW5jZXMiLCJub3RpZmljYXRpb25zIiwiQm9vbGVhbiIsInB1c2giLCJzbXMiLCJwcml2YWN5IiwicHJvZmlsZVZpc2liaWxpdHkiLCJzaG93UHJvZ3Jlc3MiLCJzaG93QWNoaWV2ZW1lbnRzIiwiZGFpbHlHb2FsIiwiTnVtYmVyIiwibWluIiwibWF4IiwicmVtaW5kZXJUaW1lIiwicHJlZmVycmVkRGlmZmljdWx0eSIsImVtYWlsVmVyaWZpZWQiLCJlbWFpbFZlcmlmaWNhdGlvblRva2VuIiwicGFzc3dvcmRSZXNldFRva2VuIiwicGFzc3dvcmRSZXNldEV4cGlyZXMiLCJsYXN0TG9naW4iLCJsb2dpbkF0dGVtcHRzIiwibG9ja1VudGlsIiwidGltZXN0YW1wcyIsInRvSlNPTiIsInZpcnR1YWxzIiwidG9PYmplY3QiLCJpbmRleCIsImNyZWF0ZWRBdCIsInZpcnR1YWwiLCJnZXQiLCJnZXRUaW1lIiwibm93IiwicHJlIiwibmV4dCIsImlzTW9kaWZpZWQiLCJzYWx0IiwiZ2VuU2FsdCIsImhhc2giLCJlcnJvciIsIm1ldGhvZHMiLCJjb21wYXJlUGFzc3dvcmQiLCJjYW5kaWRhdGVQYXNzd29yZCIsImNvbXBhcmUiLCJnZW5lcmF0ZVBhc3N3b3JkUmVzZXRUb2tlbiIsInJlc2V0VG9rZW4iLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJnZW5lcmF0ZUVtYWlsVmVyaWZpY2F0aW9uVG9rZW4iLCJ2ZXJpZmljYXRpb25Ub2tlbiIsImlzTG9ja2VkIiwiaW5jcmVtZW50TG9naW5BdHRlbXB0cyIsInVwZGF0ZU9uZSIsIiR1bnNldCIsIiRzZXQiLCJ1cGRhdGVzIiwiJGluYyIsIm1vZGVscyIsIlVzZXIiLCJtb2RlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/@next-auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();