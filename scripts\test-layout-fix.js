#!/usr/bin/env node

/**
 * Layout Fix Testing Script for WebTA LMS
 * Tests that dashboard layout duplication issues are resolved
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 Testing Layout Fix - WebTA LMS')
console.log('=' .repeat(50))

// Test results
let totalTests = 0
let passedTests = 0
const results = []

function test(name, condition, details = '') {
  totalTests++
  const passed = condition
  if (passed) passedTests++
  
  const status = passed ? '✅ PASS' : '❌ FAIL'
  console.log(`${status} ${name}`)
  if (details) console.log(`   ${details}`)
  
  results.push({ name, passed, details })
}

// 1. Layout Structure Tests
console.log('\n🏗️ 1. Layout Structure')
console.log('-'.repeat(30))

test(
  'Root Layout exists',
  fs.existsSync(path.join(__dirname, '../src/app/layout.tsx')),
  'Main application layout file'
)

test(
  'Dashboard Layout exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/layout.tsx')),
  'Dashboard-specific layout file'
)

test(
  'ConditionalLayout component exists',
  fs.existsSync(path.join(__dirname, '../src/components/layout/ConditionalLayout.tsx')),
  'Component to handle layout switching'
)

test(
  'Test Layout page exists',
  fs.existsSync(path.join(__dirname, '../src/app/test-layout/page.tsx')),
  'Page for testing layout functionality'
)

// 2. Layout Configuration Tests
console.log('\n⚙️ 2. Layout Configuration')
console.log('-'.repeat(30))

// Check root layout content
const rootLayoutContent = fs.readFileSync(path.join(__dirname, '../src/app/layout.tsx'), 'utf8')

test(
  'Root layout uses ConditionalLayout',
  rootLayoutContent.includes('ConditionalLayout'),
  'Root layout delegates to ConditionalLayout component'
)

test(
  'Root layout does not directly import Header/Footer',
  !rootLayoutContent.includes('import Header') && !rootLayoutContent.includes('import Footer'),
  'Header/Footer are handled by ConditionalLayout'
)

// Check dashboard layout content
const dashboardLayoutContent = fs.readFileSync(path.join(__dirname, '../src/app/dashboard/layout.tsx'), 'utf8')

test(
  'Dashboard layout has its own navigation',
  dashboardLayoutContent.includes('Dashboard Navigation') || dashboardLayoutContent.includes('header'),
  'Dashboard has independent navigation system'
)

test(
  'Dashboard layout does not import main Header/Footer',
  !dashboardLayoutContent.includes('import Header from') && !dashboardLayoutContent.includes('import Footer from'),
  'Dashboard layout is independent from main layout'
)

// Check ConditionalLayout content
const conditionalLayoutContent = fs.readFileSync(path.join(__dirname, '../src/components/layout/ConditionalLayout.tsx'), 'utf8')

test(
  'ConditionalLayout checks for dashboard routes',
  conditionalLayoutContent.includes('isDashboardRoute') || conditionalLayoutContent.includes('/dashboard'),
  'ConditionalLayout detects dashboard routes'
)

test(
  'ConditionalLayout imports Header and Footer',
  conditionalLayoutContent.includes('Header') && conditionalLayoutContent.includes('Footer'),
  'ConditionalLayout manages Header/Footer for non-dashboard routes'
)

// 3. Dashboard Pages Tests
console.log('\n📊 3. Dashboard Pages')
console.log('-'.repeat(30))

test(
  'Student Dashboard exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/student/page.tsx')),
  'Student dashboard page'
)

test(
  'Instructor Dashboard exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/instructor/page.tsx')),
  'Instructor dashboard page'
)

test(
  'Dashboard redirect page exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/page.tsx')),
  'Dashboard auto-redirect page'
)

// Check student dashboard for duplicate headers
const studentDashboardContent = fs.readFileSync(path.join(__dirname, '../src/app/dashboard/student/page.tsx'), 'utf8')

test(
  'Student dashboard has page header only',
  studentDashboardContent.includes('Page Header') && !studentDashboardContent.includes('Main Header'),
  'Student dashboard has appropriate page-level header'
)

// Check instructor dashboard for duplicate headers
const instructorDashboardContent = fs.readFileSync(path.join(__dirname, '../src/app/dashboard/instructor/page.tsx'), 'utf8')

test(
  'Instructor dashboard has page header only',
  instructorDashboardContent.includes('Page Header') && !instructorDashboardContent.includes('Main Header'),
  'Instructor dashboard has appropriate page-level header'
)

// 4. Component Structure Tests
console.log('\n🧩 4. Component Structure')
console.log('-'.repeat(30))

test(
  'Header component exists',
  fs.existsSync(path.join(__dirname, '../src/components/layout/Header.tsx')),
  'Main header component'
)

test(
  'Footer component exists',
  fs.existsSync(path.join(__dirname, '../src/components/layout/Footer.tsx')),
  'Main footer component'
)

// 5. TypeScript Compliance Tests
console.log('\n📝 5. TypeScript Compliance')
console.log('-'.repeat(30))

// Check for proper TypeScript interfaces
test(
  'ConditionalLayout has proper TypeScript interface',
  conditionalLayoutContent.includes('interface') && conditionalLayoutContent.includes('children: React.ReactNode'),
  'ConditionalLayout component is properly typed'
)

test(
  'Dashboard layout has proper TypeScript interface',
  dashboardLayoutContent.includes('interface') || dashboardLayoutContent.includes('children: React.ReactNode'),
  'Dashboard layout component is properly typed'
)

// 6. Routing Tests
console.log('\n🛣️ 6. Routing Structure')
console.log('-'.repeat(30))

test(
  'Dashboard directory structure is correct',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard')) &&
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/student')) &&
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/instructor')),
  'Dashboard routing structure is properly organized'
)

test(
  'Test pages exist for verification',
  fs.existsSync(path.join(__dirname, '../src/app/test-layout/page.tsx')) &&
  fs.existsSync(path.join(__dirname, '../src/app/test-dashboard/page.tsx')),
  'Test pages available for manual verification'
)

// 7. CSS and Styling Tests
console.log('\n🎨 7. CSS and Styling')
console.log('-'.repeat(30))

test(
  'Global CSS exists',
  fs.existsSync(path.join(__dirname, '../src/app/globals.css')),
  'Global styles are available'
)

test(
  'Tailwind config exists',
  fs.existsSync(path.join(__dirname, '../tailwind.config.ts')) ||
  fs.existsSync(path.join(__dirname, '../tailwind.config.js')),
  'Tailwind CSS configuration is present'
)

// 8. Layout Fix Verification
console.log('\n🔍 8. Layout Fix Verification')
console.log('-'.repeat(30))

// Check that dashboard layout doesn't create conflicts
test(
  'Dashboard layout is self-contained',
  dashboardLayoutContent.includes('min-h-screen') && 
  dashboardLayoutContent.includes('header') &&
  !dashboardLayoutContent.includes('import.*Header.*from.*@/components/layout'),
  'Dashboard layout is completely self-contained'
)

test(
  'ConditionalLayout prevents conflicts',
  conditionalLayoutContent.includes('isDashboardRoute') &&
  conditionalLayoutContent.includes('return <>{children}</>'),
  'ConditionalLayout properly excludes dashboard routes from main layout'
)

test(
  'No layout nesting issues',
  !rootLayoutContent.includes('Header') || rootLayoutContent.includes('ConditionalLayout'),
  'Root layout properly delegates layout responsibility'
)

// Summary
console.log('\n' + '='.repeat(50))
console.log('🔧 LAYOUT FIX TESTING SUMMARY')
console.log('='.repeat(50))

const passRate = Math.round((passedTests / totalTests) * 100)
console.log(`Total Tests: ${totalTests}`)
console.log(`Passed: ${passedTests}`)
console.log(`Failed: ${totalTests - passedTests}`)
console.log(`Pass Rate: ${passRate}%`)

if (passRate >= 95) {
  console.log('\n🎉 EXCELLENT! Layout fix is highly successful!')
  console.log('✅ Dashboard layout duplication issues are resolved!')
} else if (passRate >= 85) {
  console.log('\n✅ GOOD! Layout fix is mostly successful!')
  console.log('⚠️  Minor issues may remain - check failed tests')
} else if (passRate >= 75) {
  console.log('\n⚠️  FAIR! Layout fix needs some improvements!')
} else {
  console.log('\n❌ POOR! Layout fix requires significant work!')
}

// Failed tests details
const failedTests = results.filter(r => !r.passed)
if (failedTests.length > 0) {
  console.log('\n❌ Failed Tests:')
  failedTests.forEach(test => {
    console.log(`   - ${test.name}`)
    if (test.details) console.log(`     ${test.details}`)
  })
}

// Manual testing instructions
console.log('\n📋 Manual Testing Instructions:')
console.log('1. Start the development server: npm run dev')
console.log('2. Visit /test-layout to verify main app layout')
console.log('3. Visit /dashboard/student to verify dashboard layout')
console.log('4. Visit /dashboard/instructor to verify instructor layout')
console.log('5. Check that there are no duplicate headers/footers')
console.log('6. Verify smooth transitions between layouts')

console.log('\n🚀 Layout Fix Testing Complete!')

// Exit with appropriate code
process.exit(failedTests.length > 0 ? 1 : 0)
