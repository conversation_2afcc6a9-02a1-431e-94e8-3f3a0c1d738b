export default function HomePage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container-7xl section-padding-lg">
        <div className="text-responsive-center">
          <h1 className="heading-1 mb-6">
            Chào mừng đến với WebTA LMS
          </h1>
          <p className="body-large mb-8 container-2xl">
            Hệ thống quản lý học tập toàn diện với tính năng đánh giá tự động bằng AI
            cho 4 kỹ năng ngôn ngữ: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, V<PERSON>ết
          </p>

          <div className="grid-responsive-4 gap-6 mt-12">
            <div className="card-base card-padding-md">
              <div className="text-blue-600 text-3xl mb-4">🎧</div>
              <h3 className="heading-4 mb-2">Nghe</h3>
              <p className="body-small">
                Đ<PERSON><PERSON> gi<PERSON> khả năng nghe hiểu với AI
              </p>
            </div>

            <div className="card-base card-padding-md">
              <div className="text-green-600 text-3xl mb-4">🗣️</div>
              <h3 className="heading-4 mb-2">Nói</h3>
              <p className="body-small">
                Phân tích phát âm và độ trôi chảy
              </p>
            </div>

            <div className="card-base card-padding-md">
              <div className="text-purple-600 text-3xl mb-4">📖</div>
              <h3 className="heading-4 mb-2">Đọc</h3>
              <p className="body-small">
                Kiểm tra hiểu đọc và tốc độ đọc
              </p>
            </div>

            <div className="card-base card-padding-md">
              <div className="text-orange-600 text-3xl mb-4">✍️</div>
              <h3 className="heading-4 mb-2">Viết</h3>
              <p className="body-small">
                Đánh giá ngữ pháp và từ vựng
              </p>
            </div>
          </div>
          
          <div className="mt-12">
            <div className="bg-white p-8 rounded-lg shadow-lg max-w-md mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Phase 0: Project Setup
              </h2>
              <div className="text-left space-y-2">
                <div className="flex items-center">
                  <span className="text-green-500 mr-2">✅</span>
                  <span className="text-sm">Next.js 14+ với TypeScript</span>
                </div>
                <div className="flex items-center">
                  <span className="text-yellow-500 mr-2">🔄</span>
                  <span className="text-sm">MongoDB connection setup</span>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-400 mr-2">⏳</span>
                  <span className="text-sm">Authentication system</span>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-400 mr-2">⏳</span>
                  <span className="text-sm">Development tools</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
