(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[727],{67:e=>{"use strict";e.exports=require("node:async_hooks")},195:e=>{"use strict";e.exports=require("node:buffer")},806:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>eR});var a,i,o,s,c,l,d,u,p,h,f,g,y={};async function w(){let e="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&(await _ENTRIES.middleware_instrumentation).register;if(e)try{await e()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}r.r(y),r.d(y,{config:()=>eP,default:()=>eA});let m=null;function b(){return m||(m=w()),m}function v(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(v(e))},construct(){throw Error(v(e))},apply(r,n,a){if("function"==typeof a[0])return a[0](t);throw Error(v(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),b();var S=r(416),E=r(329);let _=Symbol("response"),A=Symbol("passThrough"),P=Symbol("waitUntil");class C{constructor(e){this[P]=[],this[A]=!1}respondWith(e){this[_]||(this[_]=Promise.resolve(e))}passThroughOnException(){this[A]=!0}waitUntil(e){this[P].push(e)}}class x extends C{constructor(e){super(e.request),this.sourcePage=e.page}get request(){throw new S.qJ({page:this.sourcePage})}respondWith(){throw new S.qJ({page:this.sourcePage})}}var T=r(669),R=r(241);function O(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),a=r.protocol+"//"+r.host;return n.protocol+"//"+n.host===a?n.toString().replace(a,""):n.toString()}var k=r(718);let I=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]],N=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],H=["__nextDataReq"];var M=r(217);class D extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new D}}class W extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return M.g.get(t,r,n);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==i)return M.g.get(t,i,n)},set(t,r,n,a){if("symbol"==typeof r)return M.g.set(t,r,n,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return M.g.set(t,o??r,n,a)},has(t,r){if("symbol"==typeof r)return M.g.has(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==a&&M.g.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return M.g.deleteProperty(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===a||M.g.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return D.callable;default:return M.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new W(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var K=r(938);let U=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class L{disable(){throw U}getStore(){}run(){throw U}exit(){throw U}enterWith(){throw U}}let j=globalThis.AsyncLocalStorage;function J(){return j?new j:new L}let B=J();class $ extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new $}}class V{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return $.callable;default:return M.g.get(e,t,r)}}})}}let G=Symbol.for("next.mutated.cookies");class q{static wrap(e,t){let r=new K.nV(new Headers);for(let t of e.getAll())r.set(t);let n=[],a=new Set,i=()=>{let e=B.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of n){let r=new K.nV(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case G:return n;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{i()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{i()}};default:return M.g.get(e,t,r)}}})}}var F=r(300);!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(a||(a={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(i||(i={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(o||(o={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(s||(s={})),(c||(c={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(l||(l={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(d||(d={})),(u||(u={})).executeRoute="Router.executeRoute",(p||(p={})).runHandler="Node.runHandler",(h||(h={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(f||(f={})),(g||(g={})).execute="Middleware.execute";let z=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],X=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"],{context:Y,propagation:Z,trace:Q,SpanStatusCode:ee,SpanKind:et,ROOT_CONTEXT:er}=n=r(439),en=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,ea=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:ee.ERROR,message:null==t?void 0:t.message})),e.end()},ei=new Map,eo=n.createContextKey("next.rootSpanId"),es=0,ec=()=>es++;class el{getTracerInstance(){return Q.getTracer("next.js","0.0.1")}getContext(){return Y}getActiveScopeSpan(){return Q.getSpan(null==Y?void 0:Y.active())}withPropagatedContext(e,t,r){let n=Y.active();if(Q.getSpanContext(n))return t();let a=Z.extract(n,e,r);return Y.with(a,t)}trace(...e){var t;let[r,n,a]=e,{fn:i,options:o}="function"==typeof n?{fn:n,options:{}}:{fn:a,options:{...n}},s=o.spanName??r;if(!z.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||o.hideSpan)return i();let c=this.getSpanContext((null==o?void 0:o.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=Q.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==Y?void 0:Y.active())??er,l=!0);let d=ec();return o.attributes={"next.span_name":s,"next.span_type":r,...o.attributes},Y.with(c.setValue(eo,d),()=>this.getTracerInstance().startActiveSpan(s,o,e=>{let t="performance"in globalThis?globalThis.performance.now():void 0,n=()=>{ei.delete(d),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&X.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&ei.set(d,new Map(Object.entries(o.attributes??{})));try{if(i.length>1)return i(e,t=>ea(e,t));let t=i(e);if(en(t))return t.then(t=>(e.end(),t)).catch(t=>{throw ea(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw ea(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,a]=3===e.length?e:[e[0],{},e[1]];return z.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof a&&(e=e.apply(this,arguments));let i=arguments.length-1,o=arguments[i];if("function"!=typeof o)return t.trace(r,e,()=>a.apply(this,arguments));{let n=t.getContext().bind(Y.active(),o);return t.trace(r,e,(e,t)=>(arguments[i]=function(e){return null==t||t(e),n.apply(this,arguments)},a.apply(this,arguments)))}}:a}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?Q.setSpan(Y.active(),e):void 0}getRootSpanAttributes(){let e=Y.active().getValue(eo);return ei.get(e)}}let ed=(()=>{let e=new el;return()=>e})(),eu="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eu);class ep{constructor(e,t,r,n){var a;let i=e&&function(e,t){let r=W.from(e.headers);return{isOnDemandRevalidate:r.get(F.y3)===t.previewModeId,revalidateOnlyGenerated:r.has(F.Qq)}}(t,e).isOnDemandRevalidate,o=null==(a=r.get(eu))?void 0:a.value;this.isEnabled=!!(!i&&o&&e&&o===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eu,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eu,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function eh(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of(0,E.l$)(r))n.append("set-cookie",e);for(let e of new K.nV(n).getAll())t.set(e)}}let ef={wrap(e,{req:t,res:r,renderOpts:n},a){let i;function o(e){r&&r.setHeader("Set-Cookie",e)}n&&"previewProps"in n&&(i=n.previewProps);let s={},c={get headers(){return s.headers||(s.headers=function(e){let t=W.from(e);for(let e of I)t.delete(e.toString().toLowerCase());return W.seal(t)}(t.headers)),s.headers},get cookies(){if(!s.cookies){let e=new K.qC(W.from(t.headers));eh(t,e),s.cookies=V.seal(e)}return s.cookies},get mutableCookies(){if(!s.mutableCookies){let e=function(e,t){let r=new K.qC(W.from(e));return q.wrap(r,t)}(t.headers,(null==n?void 0:n.onUpdateCookies)||(r?o:void 0));eh(t,e),s.mutableCookies=e}return s.mutableCookies},get draftMode(){return s.draftMode||(s.draftMode=new ep(i,t,this.cookies,this.mutableCookies)),s.draftMode},reactLoadableManifest:(null==n?void 0:n.reactLoadableManifest)||{},assetPrefix:(null==n?void 0:n.assetPrefix)||""};return e.run(c,a,c)}},eg=J();function ey(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}class ew extends T.I{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new S.qJ({page:this.sourcePage})}respondWith(){throw new S.qJ({page:this.sourcePage})}waitUntil(){throw new S.qJ({page:this.sourcePage})}}let em={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eb=(e,t)=>ed().withPropagatedContext(e.headers,t,em),ev=!1;async function eS(e){let t,n;!function(){if(!ev&&(ev=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(177);e(),eb=t(eb)}}(),await b();let a=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let i=new k.c(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...i.searchParams.keys()]){let t=i.searchParams.getAll(e);(0,E.LI)(e,r=>{for(let e of(i.searchParams.delete(r),t))i.searchParams.append(r,e);i.searchParams.delete(e)})}let o=i.buildId;i.buildId="";let s=e.request.headers["x-nextjs-data"];s&&"/index"===i.pathname&&(i.pathname="/");let c=(0,E.EK)(e.request.headers),l=new Map;if(!a)for(let e of I){let t=e.toString().toLowerCase();c.get(t)&&(l.set(t,c.get(t)),c.delete(t))}let d=new ew({page:e.page,input:(function(e,t){let r="string"==typeof e,n=r?new URL(e):e;for(let e of N)n.searchParams.delete(e);if(t)for(let e of H)n.searchParams.delete(e);return r?n.toString():n})(i,!0).toString(),init:{body:e.request.body,geo:e.request.geo,headers:c,ip:e.request.ip,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});s&&Object.defineProperty(d,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:ey()})}));let u=new x({request:d,page:e.page});if((t=await eb(d,()=>"/middleware"===e.page||"/src/middleware"===e.page?ed().trace(g.execute,{spanName:`middleware ${d.method} ${d.nextUrl.pathname}`,attributes:{"http.target":d.nextUrl.pathname,"http.method":d.method}},()=>ef.wrap(eg,{req:d,renderOpts:{onUpdateCookies:e=>{n=e},previewProps:ey()}},()=>e.handler(d,u))):e.handler(d,u)))&&!(t instanceof Response))throw TypeError("Expected an instance of Response to be returned");t&&n&&t.headers.set("set-cookie",n);let p=null==t?void 0:t.headers.get("x-middleware-rewrite");if(t&&p&&!a){let r=new k.c(p,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});r.host===d.nextUrl.host&&(r.buildId=o||r.buildId,t.headers.set("x-middleware-rewrite",String(r)));let n=O(String(r),String(i));s&&t.headers.set("x-nextjs-rewrite",n)}let h=null==t?void 0:t.headers.get("Location");if(t&&h&&!a){let r=new k.c(h,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});t=new Response(t.body,t),r.host===d.nextUrl.host&&(r.buildId=o||r.buildId,t.headers.set("Location",String(r))),s&&(t.headers.delete("Location"),t.headers.set("x-nextjs-redirect",O(String(r),String(i))))}let f=t||R.x.next(),y=f.headers.get("x-middleware-override-headers"),w=[];if(y){for(let[e,t]of l)f.headers.set(`x-middleware-request-${e}`,t),w.push(e);w.length>0&&f.headers.set("x-middleware-override-headers",y+","+w.join(","))}return{response:f,waitUntil:Promise.all(u[P]),fetchMetrics:d.fetchMetrics}}var eE=r(784),e_=r(635);let eA=(0,eE.withAuth)(function(e){let t=e.nextauth.token,{pathname:r}=e.nextUrl;return r.startsWith("/admin")&&(!t||"admin"!==t.role&&"super_admin"!==t.role)||r.startsWith("/instructor")&&(!t||!["instructor","admin","super_admin"].includes(t.role))||r.startsWith("/dashboard")&&!r.startsWith("/dashboard/admin")&&!r.startsWith("/dashboard/instructor")&&(!t||"student"!==t.role)?e_.NextResponse.redirect(new URL("/auth/signin",e.url)):r.startsWith("/api/admin")&&(!t||"admin"!==t.role&&"super_admin"!==t.role)||r.startsWith("/api/instructor")&&(!t||!["instructor","admin","super_admin"].includes(t.role))?e_.NextResponse.json({error:"Kh\xf4ng c\xf3 quyền truy cập"},{status:403}):r.startsWith("/api/courses")&&"GET"!==e.method&&!t?.emailVerified?e_.NextResponse.json({error:"Vui l\xf2ng x\xe1c thực email trước khi thực hiện thao t\xe1c n\xe0y"},{status:403}):e_.NextResponse.next()},{callbacks:{authorized:({token:e,req:t})=>{let{pathname:r}=t.nextUrl;return!!(["/","/courses","/about","/contact","/auth/signin","/auth/signup","/auth/error","/auth/verify-request"].some(e=>r===e||r.startsWith(e+"/"))||"GET"===t.method&&["/api/auth","/api/courses","/api/categories"].some(e=>r.startsWith(e)))||!!e}}}),eP={matcher:["/((?!_next/static|_next/image|favicon.ico|public/).*)"]},eC={...y},ex=eC.middleware||eC.default,eT="/src/middleware";if("function"!=typeof ex)throw Error(`The Middleware "${eT}" must export a \`middleware\` or a \`default\` function`);function eR(e){return eS({...e,page:eT,handler:ex})}},282:(e,t)=>{"use strict";function r(e,t,r){n(e,t),t.set(e,r)}function n(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function a(e,t){return e.get(o(e,t))}function i(e,t,r){return e.set(o(e,t),r),r}function o(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw TypeError("Private element is not present on this object")}Object.defineProperty(t,"__esModule",{value:!0}),t.SessionStore=void 0,t.defaultCookies=function(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}next-auth.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}next-auth.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}next-auth.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}next-auth.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}next-auth.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}next-auth.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}}}};var s=new WeakMap,c=new WeakMap,l=new WeakMap,d=new WeakSet;class u{constructor(e,t,o){!function(e,t){n(e,t),t.add(e)}(this,d),r(this,s,{}),r(this,c,void 0),r(this,l,void 0),i(l,this,o),i(c,this,e);let{cookies:u}=t,{name:p}=e;if("function"==typeof(null==u?void 0:u.getAll))for(let{name:e,value:t}of u.getAll())e.startsWith(p)&&(a(s,this)[e]=t);else if(u instanceof Map)for(let e of u.keys())e.startsWith(p)&&(a(s,this)[e]=u.get(e));else for(let e in u)e.startsWith(p)&&(a(s,this)[e]=u[e])}get value(){return Object.keys(a(s,this)).sort((e,t)=>{var r,n;return parseInt(null!==(r=e.split(".").pop())&&void 0!==r?r:"0")-parseInt(null!==(n=t.split(".").pop())&&void 0!==n?n:"0")}).map(e=>a(s,this)[e]).join("")}chunk(e,t){let r=o(d,this,h).call(this);for(let n of o(d,this,p).call(this,{name:a(c,this).name,value:e,options:{...a(c,this).options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(o(d,this,h).call(this))}}function p(e){let t=Math.ceil(e.value.length/3933);if(1===t)return a(s,this)[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3933*n,3933);r.push({...e,name:t,value:i}),a(s,this)[t]=i}return a(l,this).debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:163,valueSize:e.value.length,chunks:r.map(e=>e.value.length+163)}),r}function h(){let e={};for(let r in a(s,this)){var t;null===(t=a(s,this))||void 0===t||delete t[r],e[r]={name:r,value:"",options:{...a(c,this).options,maxAge:0}}}return e}t.SessionStore=u},859:(e,t,r)=>{"use strict";var n=r(476);Object.defineProperty(t,"__esModule",{value:!0});var a={encode:!0,decode:!0,getToken:!0};t.decode=p,t.encode=u,t.getToken=h;var i=r(507),o=n(r(728)),s=r(842),c=r(282),l=r(555);Object.keys(l).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===l[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return l[e]}}))});let d=()=>Date.now()/1e3|0;async function u(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a=""}=e,o=await f(r,a);return await new i.EncryptJWT(t).setProtectedHeader({alg:"dir",enc:"A256GCM"}).setIssuedAt().setExpirationTime(d()+n).setJti((0,s.v4)()).encrypt(o)}async function p(e){let{token:t,secret:r,salt:n=""}=e;if(!t)return null;let a=await f(r,n),{payload:o}=await (0,i.jwtDecrypt)(t,a,{clockTolerance:15});return o}async function h(e){var t,r,n,a;let{req:i,secureCookie:o=null!==(t=null===(r=process.env.NEXTAUTH_URL)||void 0===r?void 0:r.startsWith("https://"))&&void 0!==t?t:!!process.env.VERCEL,cookieName:s=o?"__Secure-next-auth.session-token":"next-auth.session-token",raw:l,decode:d=p,logger:u=console,secret:h=null!==(n=process.env.NEXTAUTH_SECRET)&&void 0!==n?n:process.env.AUTH_SECRET}=e;if(!i)throw Error("Must pass `req` to JWT getToken()");let f=new c.SessionStore({name:s,options:{secure:o}},{cookies:i.cookies,headers:i.headers},u).value,g=i.headers instanceof Headers?i.headers.get("authorization"):null===(a=i.headers)||void 0===a?void 0:a.authorization;if(f||(null==g?void 0:g.split(" ")[0])!=="Bearer"||(f=decodeURIComponent(g.split(" ")[1])),!f)return null;if(l)return f;try{return await d({token:f,secret:h})}catch(e){return null}}async function f(e,t){return await (0,o.default)("sha256",e,t,`NextAuth.js Generated Encryption Key${t?` (${t})`:""}`,32)}},555:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},784:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(93));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},93:(e,t,r)=>{"use strict";var n=r(476);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.withAuth=c;var a=r(635),i=r(859),o=n(r(66));async function s(e,t,r){var n,s,c,l,d,u,p,h,f,g,y;let{pathname:w,search:m,origin:b,basePath:v}=e.nextUrl,S=null!==(n=null==t||null===(s=t.pages)||void 0===s?void 0:s.signIn)&&void 0!==n?n:"/api/auth/signin",E=null!==(c=null==t||null===(l=t.pages)||void 0===l?void 0:l.error)&&void 0!==c?c:"/api/auth/error",_=(0,o.default)(process.env.NEXTAUTH_URL).path;if(`${v}${w}`.startsWith(_)||[S,E].includes(w)||["/_next","/favicon.ico"].some(e=>w.startsWith(e)))return;let A=null!==(d=null!==(u=null==t?void 0:t.secret)&&void 0!==u?u:process.env.NEXTAUTH_SECRET)&&void 0!==d?d:process.env.AUTH_SECRET;if(!A){console.error("[next-auth][error][NO_SECRET]",`
https://next-auth.js.org/errors#no_secret`);let e=new URL(`${v}${E}`,b);return e.searchParams.append("error","Configuration"),a.NextResponse.redirect(e)}let P=await (0,i.getToken)({req:e,decode:null==t||null===(p=t.jwt)||void 0===p?void 0:p.decode,cookieName:null==t||null===(h=t.cookies)||void 0===h||null===(h=h.sessionToken)||void 0===h?void 0:h.name,secret:A});if(null!==(f=await (null==t||null===(g=t.callbacks)||void 0===g||null===(y=g.authorized)||void 0===y?void 0:y.call(g,{req:e,token:P})))&&void 0!==f?f:!!P)return await (null==r?void 0:r(P));let C=new URL(`${v}${S}`,b);return C.searchParams.append("callbackUrl",`${v}${w}${m}`),a.NextResponse.redirect(C)}function c(...e){if(!e.length||e[0]instanceof Request)return s(...e);if("function"==typeof e[0]){let t=e[0],r=e[1];return async(...e)=>await s(e[0],r,async r=>(e[0].nextauth={token:r},await t(...e)))}let t=e[0];return async(...e)=>await s(e[0],t)}t.default=c},66:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!==(t=e)&&void 0!==t?t:r),a=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),i=`${n.origin}${a}`;return{origin:n.origin,host:n.host,path:a,base:i,toString:()=>i}}},945:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,i={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function c(e){var t,r;if(!e)return;let[[n,a],...i]=s(e),{domain:o,expires:c,httponly:u,maxage:p,path:h,samesite:f,secure:g,partitioned:y,priority:w}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(a),domain:o,...c&&{expires:new Date(c)},...u&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...f&&{sameSite:l.includes(t=(t=f).toLowerCase())?t:void 0},...g&&{secure:!0},...w&&{priority:d.includes(r=(r=w).toLowerCase())?r:void 0},...y&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(i,{RequestCookies:()=>u,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,i,o,s)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let c of n(i))a.call(e,c)||c===o||t(e,c,{get:()=>i[c],enumerable:!(s=r(i,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),i);var l=["strict","lax","none"],d=["low","medium","high"],u=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let a=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(a)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},439:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),a=r(172),i=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),a=r(912),i=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:i.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=e.stack)&&void 0!==n?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,o.getGlobal)("diag"),d=(0,a.createLogLevelDiagLogger)(null!==(s=r.logLevel)&&void 0!==s?s:i.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!==(c=Error().stack)&&void 0!==c?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),d.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",d,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),a=r(172),i=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),a=r(874),i=r(194),o=r(277),s=r(369),c=r(930),l="propagation",d=new a.NoopTextMapPropagator;class u{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=i.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=i.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||d}}t.PropagationAPI=u},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),a=r(846),i=r(139),o=r(607),s=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=i.wrapSpanContext,this.isSpanContextValid=i.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),a=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function i(e){return e.getValue(a)||void 0}t.getBaggage=i,t.getActiveBaggage=function(){return i(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),a=r(993),i=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:i.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let n=r(491);t.context=n.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class a{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=a},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new r(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let n=r(930);t.diag=n.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class a{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return i("debug",this._namespace,e)}error(...e){return i("error",this._namespace,e)}info(...e){return i("info",this._namespace,e)}warn(...e){return i("warn",this._namespace,e)}verbose(...e){return i("verbose",this._namespace,e)}}function i(e,t,r){let a=(0,n.getGlobal)("diag");if(a)return r.unshift(t),a[e](...r)}t.DiagComponentLogger=a},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let a=t[r];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),a=r(521),i=r(130),o=a.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var i;let o=c[s]=null!==(i=c[s])&&void 0!==i?i:{version:a.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${a.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null===(t=c[s])||void 0===t?void 0:t.version;if(n&&(0,i.isCompatible)(n))return null===(r=c[s])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function i(e){let t=new Set([e]),r=new Set,n=e.match(a);if(!n)return()=>!1;let i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(a);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};return null!=s.prerelease||i.major!==s.major?o(e):0===i.major?i.minor===s.minor&&i.patch<=s.patch?(t.add(e),!0):o(e):i.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=i,t.isCompatible=i(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let n=r(653);t.metrics=n.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class i extends n{add(e,t){}}t.NoopUpDownCounterMetric=i;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class l extends s{}t.NoopObservableGaugeMetric=l;class d extends s{}t.NoopObservableUpDownCounterMetric=d,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new i,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new d,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class a{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let n=r(181);t.propagation=n.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let n=r(997);t.trace=n.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class a{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=a},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),a=r(607),i=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){if(null==t?void 0:t.root)return new i.NonRecordingSpan;let n=r&&(0,a.getSpanContext)(r);return"object"==typeof n&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(n)?new i.NonRecordingSpan(n):new i.NonRecordingSpan}startActiveSpan(e,t,r,n){let i,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(i=t,c=r):(i=t,o=r,c=n);let l=null!=o?o:s.active(),d=this.startSpan(e,i,l),u=(0,a.setSpan)(l,d);return s.with(u,c,void 0,d)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class a{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=a},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class a{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=a},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),a=new(r(124)).NoopTracerProvider;class i{getTracer(e,t,r){var a;return null!==(a=this.getDelegateTracer(e,t,r))&&void 0!==a?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=i},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),a=r(403),i=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(i.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=s(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),a=r.indexOf("=");if(-1!==a){let i=r.slice(0,a),o=r.slice(a+1,t.length);(0,n.validateKey)(i)&&(0,n.validateValue)(o)&&e.set(i,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,i=RegExp(`^(?:${n}|${a})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return i.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),a=r(403),i=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return i.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var i=n[e]={exports:{}},o=!0;try{t[e].call(i.exports,i,i.exports,a),o=!1}finally{o&&delete n[e]}return i.exports}a.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0}),i.trace=i.propagation=i.metrics=i.diag=i.context=i.INVALID_SPAN_CONTEXT=i.INVALID_TRACEID=i.INVALID_SPANID=i.isValidSpanId=i.isValidTraceId=i.isSpanContextValid=i.createTraceState=i.TraceFlags=i.SpanStatusCode=i.SpanKind=i.SamplingDecision=i.ProxyTracerProvider=i.ProxyTracer=i.defaultTextMapSetter=i.defaultTextMapGetter=i.ValueType=i.createNoopMeter=i.DiagLogLevel=i.DiagConsoleLogger=i.ROOT_CONTEXT=i.createContextKey=i.baggageEntryMetadataFromString=void 0;var e=a(369);Object.defineProperty(i,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=a(780);Object.defineProperty(i,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(i,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=a(972);Object.defineProperty(i,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=a(957);Object.defineProperty(i,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var o=a(102);Object.defineProperty(i,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=a(901);Object.defineProperty(i,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=a(194);Object.defineProperty(i,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(i,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=a(125);Object.defineProperty(i,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var d=a(846);Object.defineProperty(i,"ProxyTracerProvider",{enumerable:!0,get:function(){return d.ProxyTracerProvider}});var u=a(996);Object.defineProperty(i,"SamplingDecision",{enumerable:!0,get:function(){return u.SamplingDecision}});var p=a(357);Object.defineProperty(i,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var h=a(847);Object.defineProperty(i,"SpanStatusCode",{enumerable:!0,get:function(){return h.SpanStatusCode}});var f=a(475);Object.defineProperty(i,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=a(98);Object.defineProperty(i,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var y=a(139);Object.defineProperty(i,"isSpanContextValid",{enumerable:!0,get:function(){return y.isSpanContextValid}}),Object.defineProperty(i,"isValidTraceId",{enumerable:!0,get:function(){return y.isValidTraceId}}),Object.defineProperty(i,"isValidSpanId",{enumerable:!0,get:function(){return y.isValidSpanId}});var w=a(476);Object.defineProperty(i,"INVALID_SPANID",{enumerable:!0,get:function(){return w.INVALID_SPANID}}),Object.defineProperty(i,"INVALID_TRACEID",{enumerable:!0,get:function(){return w.INVALID_TRACEID}}),Object.defineProperty(i,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return w.INVALID_SPAN_CONTEXT}});let m=a(67);Object.defineProperty(i,"context",{enumerable:!0,get:function(){return m.context}});let b=a(506);Object.defineProperty(i,"diag",{enumerable:!0,get:function(){return b.diag}});let v=a(886);Object.defineProperty(i,"metrics",{enumerable:!0,get:function(){return v.metrics}});let S=a(939);Object.defineProperty(i,"propagation",{enumerable:!0,get:function(){return S.propagation}});let E=a(845);Object.defineProperty(i,"trace",{enumerable:!0,get:function(){return E.trace}}),i.default={context:m.context,diag:b.diag,metrics:v.metrics,propagation:S.propagation,trace:E.trace}})(),e.exports=i})()},133:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var a={},i=t.split(n),o=(r||{}).decode||e,s=0;s<i.length;s++){var c=i[s],l=c.indexOf("=");if(!(l<0)){var d=c.substr(0,l).trim(),u=c.substr(++l,c.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==a[d]&&(a[d]=function(e,t){try{return t(e)}catch(t){return e}}(u,o))}}return a},t.serialize=function(e,t,n){var i=n||{},o=i.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!a.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=i.maxAge){var l=i.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(i.domain){if(!a.test(i.domain))throw TypeError("option domain is invalid");c+="; Domain="+i.domain}if(i.path){if(!a.test(i.path))throw TypeError("option path is invalid");c+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(c+="; HttpOnly"),i.secure&&(c+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},340:(e,t,r)=>{var n;(()=>{var a={226:function(a,i){!function(o,s){"use strict";var c="function",l="undefined",d="object",u="string",p="major",h="model",f="name",g="type",y="vendor",w="version",m="architecture",b="console",v="mobile",S="tablet",E="smarttv",_="wearable",A="embedded",P="Amazon",C="Apple",x="ASUS",T="BlackBerry",R="Browser",O="Chrome",k="Firefox",I="Google",N="Huawei",H="Microsoft",M="Motorola",D="Opera",W="Samsung",K="Sharp",U="Sony",L="Xiaomi",j="Zebra",J="Facebook",B="Chromium OS",$="Mac OS",V=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},G=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},q=function(e,t){return typeof e===u&&-1!==F(t).indexOf(F(e))},F=function(e){return e.toLowerCase()},z=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,a,i,o,l,u=0;u<t.length&&!o;){var p=t[u],h=t[u+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(a=0;a<h.length;a++)l=o[++n],typeof(i=h[a])===d&&i.length>0?2===i.length?typeof i[1]==c?this[i[0]]=i[1].call(this,l):this[i[0]]=i[1]:3===i.length?typeof i[1]!==c||i[1].exec&&i[1].test?this[i[0]]=l?l.replace(i[1],i[2]):void 0:this[i[0]]=l?i[1].call(this,l,i[2]):void 0:4===i.length&&(this[i[0]]=l?i[3].call(this,l.replace(i[1],i[2])):void 0):this[i]=l||s;u+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===d&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(q(t[r][n],e))return"?"===r?s:r}else if(q(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[w,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[w,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,w],[/opios[\/ ]+([\w\.]+)/i],[w,[f,D+" Mini"]],[/\bopr\/([\w\.]+)/i],[w,[f,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,w],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[w,[f,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[w,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[w,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[w,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[w,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[w,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+R],w],[/\bfocus\/([\w\.]+)/i],[w,[f,k+" Focus"]],[/\bopt\/([\w\.]+)/i],[w,[f,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[w,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[w,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[w,[f,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[w,[f,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[w,[f,k]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+R],w],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],w],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,w],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,J],w],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,w],[/\bgsa\/([\w\.]+) .*safari\//i],[w,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[w,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[w,[f,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,O+" WebView"],w],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[w,[f,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,w],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[w,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[w,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[w,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,w],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],w],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[w,[f,k+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,w],[/(cobalt)\/([\w\.]+)/i],[f,[w,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,F]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",F]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,F]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[y,W],[g,S]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[y,W],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[y,C],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[y,C],[g,S]],[/(macintosh);/i],[h,[y,C]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[y,K],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[y,N],[g,S]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[y,N],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[y,L],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[y,L],[g,S]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[y,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[y,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[y,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[y,M],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[y,M],[g,S]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[y,"LG"],[g,S]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[y,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[y,"Lenovo"],[g,S]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[y,"Nokia"],[g,v]],[/(pixel c)\b/i],[h,[y,I],[g,S]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[y,I],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[y,U],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[y,U],[g,S]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[y,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[y,P],[g,S]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[y,P],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[h,y,[g,S]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[y,T],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[y,x],[g,S]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[y,x],[g,v]],[/(nexus 9)/i],[h,[y,"HTC"],[g,S]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[h,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[y,"Acer"],[g,S]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[y,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,h,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,h,[g,S]],[/(surface duo)/i],[h,[y,H],[g,S]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[y,"Fairphone"],[g,v]],[/(u304aa)/i],[h,[y,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[h,[y,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[h,[y,"RCA"],[g,S]],[/\b(venue[\d ]{2,7}) b/i],[h,[y,"Dell"],[g,S]],[/\b(q(?:mv|ta)\w+) b/i],[h,[y,"Verizon"],[g,S]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[y,"Barnes & Noble"],[g,S]],[/\b(tm\d{3}\w+) b/i],[h,[y,"NuVision"],[g,S]],[/\b(k88) b/i],[h,[y,"ZTE"],[g,S]],[/\b(nx\d{3}j) b/i],[h,[y,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[h,[y,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[h,[y,"Swiss"],[g,S]],[/\b((zeki)?tb.*\b) b/i],[h,[y,"Zeki"],[g,S]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],h,[g,S]],[/\b(ns-?\w{0,9}) b/i],[h,[y,"Insignia"],[g,S]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[y,"NextBook"],[g,S]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],h,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],h,[g,v]],[/\b(ph-1) /i],[h,[y,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[y,"Envizen"],[g,S]],[/\b(trio[-\w\. ]+) b/i],[h,[y,"MachSpeed"],[g,S]],[/\btu_(1491) b/i],[h,[y,"Rotor"],[g,S]],[/(shield[\w ]+) b/i],[h,[y,"Nvidia"],[g,S]],[/(sprint) (\w+)/i],[y,h,[g,v]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[y,H],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[y,j],[g,S]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[y,j],[g,v]],[/smart-tv.+(samsung)/i],[y,[g,E]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[y,W],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[g,E]],[/(apple) ?tv/i],[y,[h,C+" TV"],[g,E]],[/crkey/i],[[h,O+"cast"],[y,I],[g,E]],[/droid.+aft(\w)( bui|\))/i],[h,[y,P],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[y,K],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[h,[y,U],[g,E]],[/(mitv-\w{5}) bui/i],[h,[y,L],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[y,h,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,z],[h,z],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,h,[g,b]],[/droid.+; (shield) bui/i],[h,[y,"Nvidia"],[g,b]],[/(playstation [345portablevi]+)/i],[h,[y,U],[g,b]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[y,H],[g,b]],[/((pebble))app/i],[y,h,[g,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[y,C],[g,_]],[/droid.+; (glass) \d/i],[h,[y,I],[g,_]],[/droid.+; (wt63?0{2,3})\)/i],[h,[y,j],[g,_]],[/(quest( 2| pro)?)/i],[h,[y,J],[g,_]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[g,A]],[/(aeobc)\b/i],[h,[y,P],[g,A]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[g,S]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,S]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[w,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[w,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,w],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[w,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,w],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[w,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[w,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[w,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,$],[w,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[w,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,w],[/\(bb(10);/i],[w,[f,T]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[w,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[w,[f,k+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[w,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[w,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[w,[f,O+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,B],w],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,w],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],w],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,w]]},ee=function(e,t){if(typeof e===d&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==l&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),a=r&&r.userAgentData?r.userAgentData:s,i=t?V(Q,t):Q,b=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=s,t[w]=s,X.call(t,n,i.browser),t[p]=typeof(e=t[w])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:s,b&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[m]=s,X.call(e,n,i.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[h]=s,e[g]=s,X.call(e,n,i.device),b&&!e[g]&&a&&a.mobile&&(e[g]=v),b&&"Macintosh"==e[h]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[g]=S),e},this.getEngine=function(){var e={};return e[f]=s,e[w]=s,X.call(e,n,i.engine),e},this.getOS=function(){var e={};return e[f]=s,e[w]=s,X.call(e,n,i.os),b&&!e[f]&&a&&"Unknown"!=a.platform&&(e[f]=a.platform.replace(/chrome os/i,B).replace(/macos/i,$)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===u&&e.length>350?z(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=G([f,w,p]),ee.CPU=G([m]),ee.DEVICE=G([h,y,g,b,v,E,S,_,A]),ee.ENGINE=ee.OS=G([f,w]),typeof i!==l?(a.exports&&(i=a.exports=ee),i.UAParser=ee):r.amdO?void 0!==(n=(function(){return ee}).call(t,r,t,e))&&(e.exports=n):typeof o!==l&&(o.UAParser=ee);var et=typeof o!==l&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}},n=!0;try{a[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete i[e]}return r.exports}o.ab="//";var s=o(226);e.exports=s})()},635:(e,t,r)=>{"use strict";function n(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}r.r(t),r.d(t,{ImageResponse:()=>n,NextRequest:()=>a.I,NextResponse:()=>i.x,URLPattern:()=>d,userAgent:()=>l,userAgentFromString:()=>c});var a=r(669),i=r(241),o=r(340),s=r.n(o);function c(e){return{...s()(e),isBot:void 0!==e&&/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}}function l({headers:e}){return c(e.get("user-agent")||void 0)}let d="undefined"==typeof URLPattern?void 0:URLPattern},300:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>o,dN:()=>n,u7:()=>a,y3:()=>i});let n="nxtP",a="nxtI",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...s,GROUP:{serverOnly:[s.reactServerComponents,s.actionBrowser,s.appMetadataRoute,s.appRouteHandler,s.instrument],clientOnly:[s.serverSideRendering,s.appPagesBrowser],nonClientServerTarget:[s.middleware,s.api],app:[s.reactServerComponents,s.actionBrowser,s.appMetadataRoute,s.appRouteHandler,s.serverSideRendering,s.appPagesBrowser,s.shared,s.instrument]}})},416:(e,t,r)=>{"use strict";r.d(t,{Y5:()=>i,cR:()=>a,qJ:()=>n});class n extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class a extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class i extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},718:(e,t,r)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}function a(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=a(e);return""+t+r+n+i}function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=a(e);return""+r+t+n+i}function s(e,t){if("string"!=typeof e)return!1;let{pathname:r}=a(e);return r===t||r.startsWith(t+"/")}function c(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}r.d(t,{c:()=>p});let l=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function d(e,t){return new URL(String(e).replace(l,"localhost"),t&&String(t).replace(l,"localhost"))}let u=Symbol("NextURLInternal");class p{constructor(e,t,r){let n,a;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,a=r||{}):a=r||t||{},this[u]={url:d(e,n??a.base),options:a,basePath:""},this.analyze()}analyze(){var e,t,r,n,a;let i=function(e,t){var r,n;let{basePath:a,i18n:i,trailingSlash:o}=null!=(r=t.nextConfig)?r:{},l={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):o};a&&s(l.pathname,a)&&(l.pathname=function(e,t){if(!s(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(l.pathname,a),l.basePath=a);let d=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){let e=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];l.buildId=r,d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(l.pathname=d)}if(i){let e=t.i18nProvider?t.i18nProvider.analyze(l.pathname):c(l.pathname,i.locales);l.locale=e.detectedLocale,l.pathname=null!=(n=e.pathname)?n:l.pathname,!e.detectedLocale&&l.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):c(d,i.locales)).detectedLocale&&(l.locale=e.detectedLocale)}return l}(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),o=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(o):function(e,t,r){if(e)for(let i of(r&&(r=r.toLowerCase()),e)){var n,a;if(t===(null==(n=i.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===i.defaultLocale.toLowerCase()||(null==(a=i.locales)?void 0:a.some(e=>e.toLowerCase()===r)))return i}}(null==(t=this[u].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,o);let l=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(a=this[u].options.nextConfig)?void 0:null==(n=a.i18n)?void 0:n.defaultLocale);this[u].url.pathname=i.pathname,this[u].defaultLocale=l,this[u].basePath=i.basePath??"",this[u].buildId=i.buildId,this[u].locale=i.locale??l,this[u].trailingSlash=i.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let a=e.toLowerCase();return!n&&(s(a,"/api")||s(a,"/"+t.toLowerCase()))?e:i(e,"/"+t)}((e={basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=n(t)),e.buildId&&(t=o(i(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=i(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:o(t,"/"):n(t)}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=d(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new p(String(this),this[u].options)}}},217:(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},938:(e,t,r)=>{"use strict";r.d(t,{Q7:()=>n.stringifyCookie,nV:()=>n.ResponseCookies,qC:()=>n.RequestCookies});var n=r(945)},669:(e,t,r)=>{"use strict";r.d(t,{I:()=>c});var n=r(718),a=r(329),i=r(416),o=r(938);let s=Symbol("internal request");class c extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,a.r4)(r),e instanceof Request?super(e,t):super(r,t);let i=new n.c(r,{headers:(0,a.lb)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new o.qC(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get geo(){return this[s].geo}get ip(){return this[s].ip}get nextUrl(){return this[s].nextUrl}get page(){throw new i.cR}get ua(){throw new i.Y5}get url(){return this[s].url}}},241:(e,t,r)=>{"use strict";r.d(t,{x:()=>d});var n=r(938),a=r(718),i=r(329),o=r(217);let s=Symbol("internal response"),c=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[n,a]of e.request.headers)t.set("x-middleware-request-"+n,a),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,c=new Proxy(new n.nV(r),{get(e,a,i){switch(a){case"delete":case"set":return(...i)=>{let o=Reflect.apply(e[a],e,i),s=new Headers(r);return o instanceof n.nV&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,n.Q7)(e)).join(",")),l(t,s),o};default:return o.g.get(e,a,i)}}});this[s]={cookies:c,url:t.url?new a.c(t.url,{headers:(0,i.lb)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[s].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!c.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let n="object"==typeof t?t:{},a=new Headers(null==n?void 0:n.headers);return a.set("Location",(0,i.r4)(e)),new d(null,{...n,headers:a,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,i.r4)(e)),l(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new d(null,{...e,headers:t})}}},329:(e,t,r)=>{"use strict";r.d(t,{EK:()=>a,LI:()=>c,l$:()=>i,lb:()=>o,r4:()=>s});var n=r(300);function a(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function i(e){var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function o(e){let t={},r=[];if(e)for(let[n,a]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...i(a)),t[n]=1===r.length?r[0]:r):t[n]=a;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}function c(e,t){for(let r of[n.dN,n.u7])e!==r&&e.startsWith(r)&&t(e.substring(r.length))}},488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return o},withRequest:function(){return i}});let n=new(r(67)).AsyncLocalStorage;function a(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function i(e,t,r){let i=a(e,t);return i?n.run(i,r):r()}function o(e,t){return n.getStore()||(e&&t?a(e,t):void 0)}},375:(e,t,r)=>{"use strict";var n=r(195).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return c},reader:function(){return i}});let a=r(488),i={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function o(e,t){let{url:r,method:a,headers:i,body:o,cache:s,credentials:c,integrity:l,mode:d,redirect:u,referrer:p,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:a,headers:[...Array.from(i),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:o?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:c,integrity:l,mode:d,redirect:u,referrer:p,referrerPolicy:h}}}async function s(e,t){let r=(0,a.getTestReqInfo)(t,i);if(!r)return e(t);let{testData:s,proxyPort:c}=r,l=await o(s,t),d=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!d.ok)throw Error(`Proxy request failed: ${d.status}`);let u=await d.json(),{api:p}=u;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:a}=e.response;return new Response(a?n.from(a,"base64"):null,{status:t,headers:new Headers(r)})}(u)}function c(e){return r.g.fetch=function(t,r){var n;return(null==r?void 0:null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},177:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return i},wrapRequestHandler:function(){return o}});let n=r(488),a=r(375);function i(){return(0,a.interceptFetch)(r.g.fetch)}function o(e){return(t,r)=>(0,n.withRequest)(t,a.reader,()=>e(t,r))}},842:(e,t,r)=>{"use strict";r.r(t),r.d(t,{NIL:()=>R,parse:()=>y,stringify:()=>p,v1:()=>g,v3:()=>P,v4:()=>C,v5:()=>T,validate:()=>l,version:()=>O});var n,a,i,o=new Uint8Array(16);function s(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(o)}let c=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,l=function(e){return"string"==typeof e&&c.test(e)};for(var d=[],u=0;u<256;++u)d.push((u+256).toString(16).substr(1));let p=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=(d[e[t+0]]+d[e[t+1]]+d[e[t+2]]+d[e[t+3]]+"-"+d[e[t+4]]+d[e[t+5]]+"-"+d[e[t+6]]+d[e[t+7]]+"-"+d[e[t+8]]+d[e[t+9]]+"-"+d[e[t+10]]+d[e[t+11]]+d[e[t+12]]+d[e[t+13]]+d[e[t+14]]+d[e[t+15]]).toLowerCase();if(!l(r))throw TypeError("Stringified UUID is invalid");return r};var h=0,f=0;let g=function(e,t,r){var n=t&&r||0,o=t||Array(16),c=(e=e||{}).node||a,l=void 0!==e.clockseq?e.clockseq:i;if(null==c||null==l){var d=e.random||(e.rng||s)();null==c&&(c=a=[1|d[0],d[1],d[2],d[3],d[4],d[5]]),null==l&&(l=i=(d[6]<<8|d[7])&16383)}var u=void 0!==e.msecs?e.msecs:Date.now(),g=void 0!==e.nsecs?e.nsecs:f+1,y=u-h+(g-f)/1e4;if(y<0&&void 0===e.clockseq&&(l=l+1&16383),(y<0||u>h)&&void 0===e.nsecs&&(g=0),g>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");h=u,f=g,i=l;var w=((268435455&(u+=122192928e5))*1e4+g)%4294967296;o[n++]=w>>>24&255,o[n++]=w>>>16&255,o[n++]=w>>>8&255,o[n++]=255&w;var m=u/4294967296*1e4&268435455;o[n++]=m>>>8&255,o[n++]=255&m,o[n++]=m>>>24&15|16,o[n++]=m>>>16&255,o[n++]=l>>>8|128,o[n++]=255&l;for(var b=0;b<6;++b)o[n+b]=c[b];return t||p(o)},y=function(e){if(!l(e))throw TypeError("Invalid UUID");var t,r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,r[11]=t/4294967296&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r};function w(e,t,r){function n(e,n,a,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof n&&(n=y(n)),16!==n.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var o=new Uint8Array(16+e.length);if(o.set(n),o.set(e,n.length),(o=r(o))[6]=15&o[6]|t,o[8]=63&o[8]|128,a){i=i||0;for(var s=0;s<16;++s)a[i+s]=o[s];return a}return p(o)}try{n.name=e}catch(e){}return n.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",n.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",n}function m(e){return(e+64>>>9<<4)+14+1}function b(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function v(e,t,r,n,a,i){var o;return b((o=b(b(t,e),b(n,i)))<<a|o>>>32-a,r)}function S(e,t,r,n,a,i,o){return v(t&r|~t&n,e,t,a,i,o)}function E(e,t,r,n,a,i,o){return v(t&n|r&~n,e,t,a,i,o)}function _(e,t,r,n,a,i,o){return v(t^r^n,e,t,a,i,o)}function A(e,t,r,n,a,i,o){return v(r^(t|~n),e,t,a,i,o)}let P=w("v3",48,function(e){if("string"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var r=0;r<t.length;++r)e[r]=t.charCodeAt(r)}return function(e){for(var t=[],r=32*e.length,n="0123456789abcdef",a=0;a<r;a+=8){var i=e[a>>5]>>>a%32&255,o=parseInt(n.charAt(i>>>4&15)+n.charAt(15&i),16);t.push(o)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[m(t)-1]=t;for(var r=1732584193,n=-271733879,a=-1732584194,i=271733878,o=0;o<e.length;o+=16){var s=r,c=n,l=a,d=i;r=S(r,n,a,i,e[o],7,-680876936),i=S(i,r,n,a,e[o+1],12,-389564586),a=S(a,i,r,n,e[o+2],17,606105819),n=S(n,a,i,r,e[o+3],22,-1044525330),r=S(r,n,a,i,e[o+4],7,-176418897),i=S(i,r,n,a,e[o+5],12,1200080426),a=S(a,i,r,n,e[o+6],17,-1473231341),n=S(n,a,i,r,e[o+7],22,-45705983),r=S(r,n,a,i,e[o+8],7,1770035416),i=S(i,r,n,a,e[o+9],12,-1958414417),a=S(a,i,r,n,e[o+10],17,-42063),n=S(n,a,i,r,e[o+11],22,-1990404162),r=S(r,n,a,i,e[o+12],7,1804603682),i=S(i,r,n,a,e[o+13],12,-40341101),a=S(a,i,r,n,e[o+14],17,-1502002290),n=S(n,a,i,r,e[o+15],22,1236535329),r=E(r,n,a,i,e[o+1],5,-165796510),i=E(i,r,n,a,e[o+6],9,-1069501632),a=E(a,i,r,n,e[o+11],14,643717713),n=E(n,a,i,r,e[o],20,-373897302),r=E(r,n,a,i,e[o+5],5,-701558691),i=E(i,r,n,a,e[o+10],9,38016083),a=E(a,i,r,n,e[o+15],14,-660478335),n=E(n,a,i,r,e[o+4],20,-405537848),r=E(r,n,a,i,e[o+9],5,568446438),i=E(i,r,n,a,e[o+14],9,-1019803690),a=E(a,i,r,n,e[o+3],14,-187363961),n=E(n,a,i,r,e[o+8],20,1163531501),r=E(r,n,a,i,e[o+13],5,-1444681467),i=E(i,r,n,a,e[o+2],9,-51403784),a=E(a,i,r,n,e[o+7],14,1735328473),n=E(n,a,i,r,e[o+12],20,-1926607734),r=_(r,n,a,i,e[o+5],4,-378558),i=_(i,r,n,a,e[o+8],11,-2022574463),a=_(a,i,r,n,e[o+11],16,1839030562),n=_(n,a,i,r,e[o+14],23,-35309556),r=_(r,n,a,i,e[o+1],4,-1530992060),i=_(i,r,n,a,e[o+4],11,1272893353),a=_(a,i,r,n,e[o+7],16,-155497632),n=_(n,a,i,r,e[o+10],23,-1094730640),r=_(r,n,a,i,e[o+13],4,681279174),i=_(i,r,n,a,e[o],11,-358537222),a=_(a,i,r,n,e[o+3],16,-722521979),n=_(n,a,i,r,e[o+6],23,76029189),r=_(r,n,a,i,e[o+9],4,-640364487),i=_(i,r,n,a,e[o+12],11,-421815835),a=_(a,i,r,n,e[o+15],16,530742520),n=_(n,a,i,r,e[o+2],23,-995338651),r=A(r,n,a,i,e[o],6,-198630844),i=A(i,r,n,a,e[o+7],10,1126891415),a=A(a,i,r,n,e[o+14],15,-1416354905),n=A(n,a,i,r,e[o+5],21,-57434055),r=A(r,n,a,i,e[o+12],6,1700485571),i=A(i,r,n,a,e[o+3],10,-1894986606),a=A(a,i,r,n,e[o+10],15,-1051523),n=A(n,a,i,r,e[o+1],21,-2054922799),r=A(r,n,a,i,e[o+8],6,1873313359),i=A(i,r,n,a,e[o+15],10,-30611744),a=A(a,i,r,n,e[o+6],15,-1560198380),n=A(n,a,i,r,e[o+13],21,1309151649),r=A(r,n,a,i,e[o+4],6,-145523070),i=A(i,r,n,a,e[o+11],10,-1120210379),a=A(a,i,r,n,e[o+2],15,718787259),n=A(n,a,i,r,e[o+9],21,-343485551),r=b(r,s),n=b(n,c),a=b(a,l),i=b(i,d)}return[r,n,a,i]}(function(e){if(0===e.length)return[];for(var t=8*e.length,r=new Uint32Array(m(t)),n=0;n<t;n+=8)r[n>>5]|=(255&e[n/8])<<n%32;return r}(e),8*e.length))}),C=function(e,t,r){var n=(e=e||{}).random||(e.rng||s)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(var a=0;a<16;++a)t[r+a]=n[a];return t}return p(n)};function x(e,t){return e<<t|e>>>32-t}let T=w("v5",80,function(e){var t=[1518500249,1859775393,2400959708,3395469782],r=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var n=unescape(encodeURIComponent(e));e=[];for(var a=0;a<n.length;++a)e.push(n.charCodeAt(a))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=Math.ceil((e.length/4+2)/16),o=Array(i),s=0;s<i;++s){for(var c=new Uint32Array(16),l=0;l<16;++l)c[l]=e[64*s+4*l]<<24|e[64*s+4*l+1]<<16|e[64*s+4*l+2]<<8|e[64*s+4*l+3];o[s]=c}o[i-1][14]=(e.length-1)*8/4294967296,o[i-1][14]=Math.floor(o[i-1][14]),o[i-1][15]=(e.length-1)*8&4294967295;for(var d=0;d<i;++d){for(var u=new Uint32Array(80),p=0;p<16;++p)u[p]=o[d][p];for(var h=16;h<80;++h)u[h]=x(u[h-3]^u[h-8]^u[h-14]^u[h-16],1);for(var f=r[0],g=r[1],y=r[2],w=r[3],m=r[4],b=0;b<80;++b){var v=Math.floor(b/20),S=x(f,5)+function(e,t,r,n){switch(e){case 0:return t&r^~t&n;case 1:case 3:return t^r^n;case 2:return t&r^t&n^r&n}}(v,g,y,w)+m+t[v]+u[b]>>>0;m=w,w=y,y=x(g,30)>>>0,g=f,f=S}r[0]=r[0]+f>>>0,r[1]=r[1]+g>>>0,r[2]=r[2]+y>>>0,r[3]=r[3]+w>>>0,r[4]=r[4]+m>>>0}return[r[0]>>24&255,r[0]>>16&255,r[0]>>8&255,255&r[0],r[1]>>24&255,r[1]>>16&255,r[1]>>8&255,255&r[1],r[2]>>24&255,r[2]>>16&255,r[2]>>8&255,255&r[2],r[3]>>24&255,r[3]>>16&255,r[3]>>8&255,255&r[3],r[4]>>24&255,r[4]>>16&255,r[4]>>8&255,255&r[4]]}),R="00000000-0000-0000-0000-000000000000",O=function(e){if(!l(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}},476:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},728:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,hkdf:()=>o});let n=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},a=async(e,t,r,a,i)=>{let{crypto:{subtle:o}}=n();return new Uint8Array(await o.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:a},await o.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function i(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function o(e,t,r,n,o){return a(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=i(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),i(r,"salt"),function(e){let t=i(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(o,e))}},507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CompactEncrypt:()=>tp,CompactSign:()=>tg,EmbeddedJWK:()=>tA,EncryptJWT:()=>tv,FlattenedEncrypt:()=>e3,FlattenedSign:()=>tf,GeneralEncrypt:()=>e8,GeneralSign:()=>tw,SignJWT:()=>tb,UnsecuredJWT:()=>tN,base64url:()=>a,calculateJwkThumbprint:()=>tE,calculateJwkThumbprintUri:()=>t_,compactDecrypt:()=>eY,compactVerify:()=>tr,createLocalJWKSet:()=>tR,createRemoteJWKSet:()=>tI,cryptoRuntime:()=>tB,decodeJwt:()=>tW,decodeProtectedHeader:()=>tD,errors:()=>n,exportJWK:()=>e2,exportPKCS8:()=>e1,exportSPKI:()=>e0,flattenedDecrypt:()=>eX,flattenedVerify:()=>tt,generalDecrypt:()=>eZ,generalVerify:()=>tn,generateKeyPair:()=>tj,generateSecret:()=>tJ,importJWK:()=>eK,importPKCS8:()=>eW,importSPKI:()=>eM,importX509:()=>eD,jwtDecrypt:()=>tu,jwtVerify:()=>td});var n={};r.r(n),r.d(n,{JOSEAlgNotAllowed:()=>_,JOSEError:()=>v,JOSENotSupported:()=>A,JWEDecompressionFailed:()=>C,JWEDecryptionFailed:()=>P,JWEInvalid:()=>x,JWKInvalid:()=>O,JWKSInvalid:()=>k,JWKSMultipleMatchingKeys:()=>N,JWKSNoMatchingKey:()=>I,JWKSTimeout:()=>H,JWSInvalid:()=>T,JWSSignatureVerificationFailed:()=>M,JWTClaimValidationFailed:()=>S,JWTExpired:()=>E,JWTInvalid:()=>R});var a={};r.r(a),r.d(a,{decode:()=>tM,encode:()=>tH});let i=crypto,o=e=>e instanceof CryptoKey,s=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await i.subtle.digest(r,t))},c=new TextEncoder,l=new TextDecoder;function d(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}function u(e,t,r){if(t<0||t>=4294967296)throw RangeError(`value must be >= 0 and <= ${4294967296-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function p(e){let t=new Uint8Array(8);return u(t,Math.floor(e/4294967296),0),u(t,e%4294967296,4),t}function h(e){let t=new Uint8Array(4);return u(t,e),t}function f(e){return d(h(e.length),e)}async function g(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(h(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await s("sha256",n),32*t)}return a.slice(0,t>>3)}let y=e=>{let t=e;"string"==typeof t&&(t=c.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},w=e=>y(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),m=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},b=e=>{let t=e;t instanceof Uint8Array&&(t=l.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return m(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}};class v extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(e){var t;super(e),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null===(t=Error.captureStackTrace)||void 0===t||t.call(Error,this,this.constructor)}}class S extends v{static get code(){return"ERR_JWT_CLAIM_VALIDATION_FAILED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=t,this.reason=r}}class E extends v{static get code(){return"ERR_JWT_EXPIRED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_EXPIRED",this.claim=t,this.reason=r}}class _ extends v{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}static get code(){return"ERR_JOSE_ALG_NOT_ALLOWED"}}class A extends v{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}class P extends v{constructor(){super(...arguments),this.code="ERR_JWE_DECRYPTION_FAILED",this.message="decryption operation failed"}static get code(){return"ERR_JWE_DECRYPTION_FAILED"}}class C extends v{constructor(){super(...arguments),this.code="ERR_JWE_DECOMPRESSION_FAILED",this.message="decompression operation failed"}static get code(){return"ERR_JWE_DECOMPRESSION_FAILED"}}class x extends v{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}static get code(){return"ERR_JWE_INVALID"}}class T extends v{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}static get code(){return"ERR_JWS_INVALID"}}class R extends v{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}static get code(){return"ERR_JWT_INVALID"}}class O extends v{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}static get code(){return"ERR_JWK_INVALID"}}class k extends v{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}class I extends v{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}class N extends v{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator;class H extends v{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}class M extends v{constructor(){super(...arguments),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED",this.message="signature verification failed"}static get code(){return"ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}let D=i.getRandomValues.bind(i);function W(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new A(`Unsupported JWE Algorithm: ${e}`)}}let K=e=>D(new Uint8Array(W(e)>>3)),U=(e,t)=>{if(t.length<<3!==W(e))throw new x("Invalid Initialization Vector length")},L=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new x(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)},j=(e,t)=>{if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(e.length!==t.length)throw TypeError("Input buffers must have the same length");let r=e.length,n=0,a=-1;for(;++a<r;)n|=e[a]^t[a];return 0===n};function J(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function B(e,t){return e.name===t}function $(e){return parseInt(e.name.slice(4),10)}function V(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}function G(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!B(e.algorithm,"AES-GCM"))throw J("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw J(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!B(e.algorithm,"AES-KW"))throw J("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw J(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw J("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!B(e.algorithm,"PBKDF2"))throw J("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!B(e.algorithm,"RSA-OAEP"))throw J("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if($(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}V(e,r)}function q(e,t,...r){if(r.length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor&&t.constructor.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let F=(e,...t)=>q("Key must be ",e,...t);function z(e,t,...r){return q(`Key for the ${e} algorithm must be `,t,...r)}let X=e=>o(e),Y=["CryptoKey"];async function Z(e,t,r,n,a,o){let s,c;if(!(t instanceof Uint8Array))throw TypeError(F(t,"Uint8Array"));let l=parseInt(e.slice(1,4),10),u=await i.subtle.importKey("raw",t.subarray(l>>3),"AES-CBC",!1,["decrypt"]),h=await i.subtle.importKey("raw",t.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},!1,["sign"]),f=d(o,n,r,p(o.length<<3)),g=new Uint8Array((await i.subtle.sign("HMAC",h,f)).slice(0,l>>3));try{s=j(a,g)}catch(e){}if(!s)throw new P;try{c=new Uint8Array(await i.subtle.decrypt({iv:n,name:"AES-CBC"},u,r))}catch(e){}if(!c)throw new P;return c}async function Q(e,t,r,n,a,o){let s;t instanceof Uint8Array?s=await i.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(G(t,e,"decrypt"),s=t);try{return new Uint8Array(await i.subtle.decrypt({additionalData:o,iv:n,name:"AES-GCM",tagLength:128},s,d(r,a)))}catch(e){throw new P}}let ee=async(e,t,r,n,a,i)=>{if(!o(t)&&!(t instanceof Uint8Array))throw TypeError(F(t,...Y,"Uint8Array"));switch(U(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&L(t,parseInt(e.slice(-3),10)),Z(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&L(t,parseInt(e.slice(1,4),10)),Q(e,t,r,n,a,i);default:throw new A("Unsupported JWE Content Encryption Algorithm")}},et=async()=>{throw new A('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `inflateRaw` decrypt option to provide Inflate Raw implementation.')},er=async()=>{throw new A('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `deflateRaw` encrypt option to provide Deflate Raw implementation.')},en=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function ea(e){if(!("object"==typeof e&&null!==e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let ei=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]];function eo(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function es(e,t,r){if(o(e))return G(e,t,r),e;if(e instanceof Uint8Array)return i.subtle.importKey("raw",e,"AES-KW",!0,[r]);throw TypeError(F(e,...Y,"Uint8Array"))}let ec=async(e,t,r)=>{let n=await es(t,e,"wrapKey");eo(n,e);let a=await i.subtle.importKey("raw",r,...ei);return new Uint8Array(await i.subtle.wrapKey("raw",a,n,"AES-KW"))},el=async(e,t,r)=>{let n=await es(t,e,"unwrapKey");eo(n,e);let a=await i.subtle.unwrapKey("raw",r,n,"AES-KW",...ei);return new Uint8Array(await i.subtle.exportKey("raw",a))};async function ed(e,t,r,n,a=new Uint8Array(0),s=new Uint8Array(0)){let l;if(!o(e))throw TypeError(F(e,...Y));if(G(e,"ECDH"),!o(t))throw TypeError(F(t,...Y));G(t,"ECDH","deriveBits");let u=d(f(c.encode(r)),f(a),f(s),h(n));return l="X25519"===e.algorithm.name?256:"X448"===e.algorithm.name?448:Math.ceil(parseInt(e.algorithm.namedCurve.substr(-3),10)/8)<<3,g(new Uint8Array(await i.subtle.deriveBits({name:e.algorithm.name,public:e},t,l)),n,u)}async function eu(e){if(!o(e))throw TypeError(F(e,...Y));return i.subtle.generateKey(e.algorithm,!0,["deriveBits"])}function ep(e){if(!o(e))throw TypeError(F(e,...Y));return["P-256","P-384","P-521"].includes(e.algorithm.namedCurve)||"X25519"===e.algorithm.name||"X448"===e.algorithm.name}async function eh(e,t,r,n){!function(e){if(!(e instanceof Uint8Array)||e.length<8)throw new x("PBES2 Salt Input must be 8 or more octets")}(e);let a=d(c.encode(t),new Uint8Array([0]),e),s=parseInt(t.slice(13,16),10),l={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},u=await function(e,t){if(e instanceof Uint8Array)return i.subtle.importKey("raw",e,"PBKDF2",!1,["deriveBits"]);if(o(e))return G(e,t,"deriveBits","deriveKey"),e;throw TypeError(F(e,...Y,"Uint8Array"))}(n,t);if(u.usages.includes("deriveBits"))return new Uint8Array(await i.subtle.deriveBits(l,u,s));if(u.usages.includes("deriveKey"))return i.subtle.deriveKey(l,u,{length:s,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let ef=async(e,t,r,n=2048,a=D(new Uint8Array(16)))=>{let i=await eh(a,e,n,t);return{encryptedKey:await ec(e.slice(-6),i,r),p2c:n,p2s:w(a)}},eg=async(e,t,r,n,a)=>{let i=await eh(a,e,n,t);return el(e.slice(-6),i,r)};function ey(e){switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new A(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let ew=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},em=async(e,t,r)=>{if(!o(t))throw TypeError(F(t,...Y));if(G(t,e,"encrypt","wrapKey"),ew(e,t),t.usages.includes("encrypt"))return new Uint8Array(await i.subtle.encrypt(ey(e),t,r));if(t.usages.includes("wrapKey")){let n=await i.subtle.importKey("raw",r,...ei);return new Uint8Array(await i.subtle.wrapKey("raw",n,t,ey(e)))}throw TypeError('RSA-OAEP key "usages" must include "encrypt" or "wrapKey" for this operation')},eb=async(e,t,r)=>{if(!o(t))throw TypeError(F(t,...Y));if(G(t,e,"decrypt","unwrapKey"),ew(e,t),t.usages.includes("decrypt"))return new Uint8Array(await i.subtle.decrypt(ey(e),t,r));if(t.usages.includes("unwrapKey")){let n=await i.subtle.unwrapKey("raw",r,t,ey(e),...ei);return new Uint8Array(await i.subtle.exportKey("raw",n))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function ev(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new A(`Unsupported JWE Algorithm: ${e}`)}}let eS=e=>D(new Uint8Array(ev(e)>>3)),eE=(e,t)=>{let r=(e.match(/.{1,64}/g)||[]).join("\n");return`-----BEGIN ${t}-----
${r}
-----END ${t}-----`},e_=async(e,t,r)=>{if(!o(r))throw TypeError(F(r,...Y));if(!r.extractable)throw TypeError("CryptoKey is not extractable");if(r.type!==e)throw TypeError(`key is not a ${e} key`);return eE(y(new Uint8Array(await i.subtle.exportKey(t,r))),`${e.toUpperCase()} KEY`)},eA=e=>e_("public","spki",e),eP=e=>e_("private","pkcs8",e),eC=(e,t,r=0)=>{0===r&&(t.unshift(t.length),t.unshift(6));let n=e.indexOf(t[0],r);if(-1===n)return!1;let a=e.subarray(n,n+t.length);return a.length===t.length&&(a.every((e,r)=>e===t[r])||eC(e,t,n+1))},ex=e=>{switch(!0){case eC(e,[42,134,72,206,61,3,1,7]):return"P-256";case eC(e,[43,129,4,0,34]):return"P-384";case eC(e,[43,129,4,0,35]):return"P-521";case eC(e,[43,101,110]):return"X25519";case eC(e,[43,101,111]):return"X448";case eC(e,[43,101,112]):return"Ed25519";case eC(e,[43,101,113]):return"Ed448";default:throw new A("Invalid or unsupported EC Key Curve or OKP Key Sub Type")}},eT=async(e,t,r,n,a)=>{var o;let s,c;let l=new Uint8Array(atob(r.replace(e,"")).split("").map(e=>e.charCodeAt(0))),d="spki"===t;switch(n){case"PS256":case"PS384":case"PS512":s={name:"RSA-PSS",hash:`SHA-${n.slice(-3)}`},c=d?["verify"]:["sign"];break;case"RS256":case"RS384":case"RS512":s={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${n.slice(-3)}`},c=d?["verify"]:["sign"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s={name:"RSA-OAEP",hash:`SHA-${parseInt(n.slice(-3),10)||1}`},c=d?["encrypt","wrapKey"]:["decrypt","unwrapKey"];break;case"ES256":s={name:"ECDSA",namedCurve:"P-256"},c=d?["verify"]:["sign"];break;case"ES384":s={name:"ECDSA",namedCurve:"P-384"},c=d?["verify"]:["sign"];break;case"ES512":s={name:"ECDSA",namedCurve:"P-521"},c=d?["verify"]:["sign"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e=ex(l);s=e.startsWith("P-")?{name:"ECDH",namedCurve:e}:{name:e},c=d?[]:["deriveBits"];break}case"EdDSA":s={name:ex(l)},c=d?["verify"]:["sign"];break;default:throw new A('Invalid or unsupported "alg" (Algorithm) value')}return i.subtle.importKey(t,l,s,null!==(o=null==a?void 0:a.extractable)&&void 0!==o&&o,c)},eR=(e,t,r)=>eT(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\s)/g,"pkcs8",e,t,r),eO=(e,t,r)=>eT(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\s)/g,"spki",e,t,r);function ek(e){let t=[],r=0;for(;r<e.length;){let n=eI(e.subarray(r));t.push(n),r+=n.byteLength}return t}function eI(e){let t=0,r=31&e[0];if(t++,31===r){for(r=0;e[t]>=128;)r=128*r+e[t]-128,t++;r=128*r+e[t]-128,t++}let n=0;if(e[t]<128)n=e[t],t++;else if(128===n){for(n=0;0!==e[t+n]||0!==e[t+n+1];){if(n>e.byteLength)throw TypeError("invalid indefinite form length");n++}let r=t+n+2;return{byteLength:r,contents:e.subarray(t,t+n),raw:e.subarray(0,r)}}else{let r=127&e[t];t++,n=0;for(let a=0;a<r;a++)n=256*n+e[t],t++}let a=t+n;return{byteLength:a,contents:e.subarray(t,a),raw:e.subarray(0,a)}}let eN=(e,t,r)=>{let n;try{n=eE(function(e){let t=ek(ek(eI(e).contents)[0].contents);return y(t[160===t[0].raw[0]?6:5].raw)}(m(e.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\s)/g,""))),"PUBLIC KEY")}catch(e){throw TypeError("Failed to parse the X.509 certificate",{cause:e})}return eO(n,t,r)},eH=async e=>{var t,r;if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:n,keyUsages:a}=function(e){let t,r;switch(e.kty){case"oct":switch(e.alg){case"HS256":case"HS384":case"HS512":t={name:"HMAC",hash:`SHA-${e.alg.slice(-3)}`},r=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":throw new A(`${e.alg} keys cannot be imported as CryptoKey instances`);case"A128GCM":case"A192GCM":case"A256GCM":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":t={name:"AES-GCM"},r=["encrypt","decrypt"];break;case"A128KW":case"A192KW":case"A256KW":t={name:"AES-KW"},r=["wrapKey","unwrapKey"];break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":t={name:"PBKDF2"},r=["deriveBits"];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new A('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),o=[n,null!==(t=e.ext)&&void 0!==t&&t,null!==(r=e.key_ops)&&void 0!==r?r:a];if("PBKDF2"===n.name)return i.subtle.importKey("raw",b(e.k),...o);let s={...e};return delete s.alg,delete s.use,i.subtle.importKey("jwk",s,...o)};async function eM(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PUBLIC KEY-----"))throw TypeError('"spki" must be SPKI formatted string');return eO(e,t,r)}async function eD(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN CERTIFICATE-----"))throw TypeError('"x509" must be X.509 formatted string');return eN(e,t,r)}async function eW(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PRIVATE KEY-----"))throw TypeError('"pkcs8" must be PKCS#8 formatted string');return eR(e,t,r)}async function eK(e,t,r){var n;if(!ea(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=r||(r=!0!==e.ext),r)return eH({...e,alg:t,ext:null!==(n=e.ext)&&void 0!==n&&n});return b(e.k);case"RSA":if(void 0!==e.oth)throw new A('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return eH({...e,alg:t});default:throw new A('Unsupported "kty" (Key Type) Parameter value')}}let eU=(e,t)=>{if(!(t instanceof Uint8Array)){if(!X(t))throw TypeError(z(e,t,...Y,"Uint8Array"));if("secret"!==t.type)throw TypeError(`${Y.join(" or ")} instances for symmetric algorithms must be of type "secret"`)}},eL=(e,t,r)=>{if(!X(t))throw TypeError(z(e,t,...Y));if("secret"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm encryption must be of type "public"`)},ej=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?eU(e,t):eL(e,t,r)};async function eJ(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(F(r,"Uint8Array"));let o=parseInt(e.slice(1,4),10),s=await i.subtle.importKey("raw",r.subarray(o>>3),"AES-CBC",!1,["encrypt"]),c=await i.subtle.importKey("raw",r.subarray(0,o>>3),{hash:`SHA-${o<<1}`,name:"HMAC"},!1,["sign"]),l=new Uint8Array(await i.subtle.encrypt({iv:n,name:"AES-CBC"},s,t)),u=d(a,n,l,p(a.length<<3));return{ciphertext:l,tag:new Uint8Array((await i.subtle.sign("HMAC",c,u)).slice(0,o>>3))}}async function eB(e,t,r,n,a){let o;r instanceof Uint8Array?o=await i.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(G(r,e,"encrypt"),o=r);let s=new Uint8Array(await i.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,t)),c=s.slice(-16);return{ciphertext:s.slice(0,-16),tag:c}}let e$=async(e,t,r,n,a)=>{if(!o(r)&&!(r instanceof Uint8Array))throw TypeError(F(r,...Y,"Uint8Array"));switch(U(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&L(r,parseInt(e.slice(-3),10)),eJ(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&L(r,parseInt(e.slice(1,4),10)),eB(e,t,r,n,a);default:throw new A("Unsupported JWE Content Encryption Algorithm")}};async function eV(e,t,r,n){let a=e.slice(0,7);n||(n=K(a));let{ciphertext:i,tag:o}=await e$(a,r,t,n,new Uint8Array(0));return{encryptedKey:i,iv:w(n),tag:w(o)}}async function eG(e,t,r,n,a){return ee(e.slice(0,7),t,r,n,a,new Uint8Array(0))}async function eq(e,t,r,n,a){switch(ej(e,t,"decrypt"),e){case"dir":if(void 0!==r)throw new x("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new x("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!ea(n.epk))throw new x('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!ep(t))throw new A("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await eK(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new x('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=b(n.apu)}catch(e){throw new x("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new x('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=b(n.apv)}catch(e){throw new x("Failed to base64url decode the apv")}}let s=await ed(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?ev(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new x("JWE Encrypted Key missing");return el(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new x("JWE Encrypted Key missing");return eb(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new x("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new x('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=(null==a?void 0:a.maxPBES2Count)||1e4;if(n.p2c>o)throw new x('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new x('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=b(n.p2s)}catch(e){throw new x("Failed to base64url decode the p2s")}return eg(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new x("JWE Encrypted Key missing");return el(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new x("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new x('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new x('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=b(n.iv)}catch(e){throw new x("Failed to base64url decode the iv")}try{i=b(n.tag)}catch(e){throw new x("Failed to base64url decode the tag")}return eG(e,t,r,a,i)}default:throw new A('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let eF=function(e,t,r,n,a){let i;if(void 0!==a.crit&&void 0===n.crit)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new A(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},ez=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function eX(e,t,r){var n;let a,i,o,s,u,p,h;if(!ea(e))throw new x("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new x("JOSE Header missing");if("string"!=typeof e.iv)throw new x("JWE Initialization Vector missing or incorrect type");if("string"!=typeof e.ciphertext)throw new x("JWE Ciphertext missing or incorrect type");if("string"!=typeof e.tag)throw new x("JWE Authentication Tag missing or incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new x("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new x("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new x("JWE AAD incorrect type");if(void 0!==e.header&&!ea(e.header))throw new x("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!ea(e.unprotected))throw new x("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=b(e.protected);a=JSON.parse(l.decode(t))}catch(e){throw new x("JWE Protected Header is invalid")}if(!en(a,e.header,e.unprotected))throw new x("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let f={...a,...e.header,...e.unprotected};if(eF(x,new Map,null==r?void 0:r.crit,a,f),void 0!==f.zip){if(!a||!a.zip)throw new x('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==f.zip)throw new A('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:g,enc:y}=f;if("string"!=typeof g||!g)throw new x("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof y||!y)throw new x("missing JWE Encryption Algorithm (enc) in JWE Header");let w=r&&ez("keyManagementAlgorithms",r.keyManagementAlgorithms),m=r&&ez("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(w&&!w.has(g))throw new _('"alg" (Algorithm) Header Parameter not allowed');if(m&&!m.has(y))throw new _('"enc" (Encryption Algorithm) Header Parameter not allowed');if(void 0!==e.encrypted_key)try{i=b(e.encrypted_key)}catch(e){throw new x("Failed to base64url decode the encrypted_key")}let v=!1;"function"==typeof t&&(t=await t(a,e),v=!0);try{o=await eq(g,t,i,f,r)}catch(e){if(e instanceof TypeError||e instanceof x||e instanceof A)throw e;o=eS(y)}try{s=b(e.iv)}catch(e){throw new x("Failed to base64url decode the iv")}try{u=b(e.tag)}catch(e){throw new x("Failed to base64url decode the tag")}let S=c.encode(null!==(n=e.protected)&&void 0!==n?n:"");p=void 0!==e.aad?d(S,c.encode("."),c.encode(e.aad)):S;try{h=b(e.ciphertext)}catch(e){throw new x("Failed to base64url decode the ciphertext")}let E=await ee(y,o,h,s,u,p);"DEF"===f.zip&&(E=await ((null==r?void 0:r.inflateRaw)||et)(E));let P={plaintext:E};if(void 0!==e.protected&&(P.protectedHeader=a),void 0!==e.aad)try{P.additionalAuthenticatedData=b(e.aad)}catch(e){throw new x("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(P.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(P.unprotectedHeader=e.header),v)?{...P,key:t}:P}async function eY(e,t,r){if(e instanceof Uint8Array&&(e=l.decode(e)),"string"!=typeof e)throw new x("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new x("Invalid Compact JWE");let d=await eX({ciphertext:o,iv:i||void 0,protected:n||void 0,tag:s||void 0,encrypted_key:a||void 0},t,r),u={plaintext:d.plaintext,protectedHeader:d.protectedHeader};return"function"==typeof t?{...u,key:d.key}:u}async function eZ(e,t,r){if(!ea(e))throw new x("General JWE must be an object");if(!Array.isArray(e.recipients)||!e.recipients.every(ea))throw new x("JWE Recipients missing or incorrect type");if(!e.recipients.length)throw new x("JWE Recipients has no members");for(let n of e.recipients)try{return await eX({aad:e.aad,ciphertext:e.ciphertext,encrypted_key:n.encrypted_key,header:n.header,iv:e.iv,protected:e.protected,tag:e.tag,unprotected:e.unprotected},t,r)}catch(e){}throw new P}let eQ=async e=>{if(e instanceof Uint8Array)return{kty:"oct",k:w(e)};if(!o(e))throw TypeError(F(e,...Y,"Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...s}=await i.subtle.exportKey("jwk",e);return s};async function e0(e){return eA(e)}async function e1(e){return eP(e)}async function e2(e){return eQ(e)}async function e5(e,t,r,n,a={}){let i,o,s;switch(ej(e,r,"encrypt"),e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!ep(r))throw new A("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:l}=a,{epk:d}=a;d||(d=(await eu(r)).privateKey);let{x:u,y:p,crv:h,kty:f}=await e2(d),g=await ed(r,d,"ECDH-ES"===e?t:e,"ECDH-ES"===e?ev(t):parseInt(e.slice(-5,-2),10),c,l);if(o={epk:{x:u,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),c&&(o.apu=w(c)),l&&(o.apv=w(l)),"ECDH-ES"===e){s=g;break}s=n||eS(t);let y=e.slice(-6);i=await ec(y,g,s);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||eS(t),i=await em(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||eS(t);let{p2c:c,p2s:l}=a;({encryptedKey:i,...o}=await ef(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||eS(t),i=await ec(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||eS(t);let{iv:c}=a;({encryptedKey:i,...o}=await eV(e,r,s,c));break}default:throw new A('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}}let e4=Symbol();class e3{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,n,a,i,o,s,u;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new x("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!en(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new x("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let p={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(eF(x,new Map,null==t?void 0:t.crit,this._protectedHeader,p),void 0!==p.zip){if(!this._protectedHeader||!this._protectedHeader.zip)throw new x('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==p.zip)throw new A('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:h,enc:f}=p;if("string"!=typeof h||!h)throw new x('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof f||!f)throw new x('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if("dir"===h){if(this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Encryption")}else if("ECDH-ES"===h&&this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Key Agreement");{let a;({cek:n,encryptedKey:r,parameters:a}=await e5(h,f,e,this._cek,this._keyManagementParameters)),a&&(t&&e4 in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...a}:this.setUnprotectedHeader(a):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...a}:this.setProtectedHeader(a))}if(this._iv||(this._iv=K(f)),i=this._protectedHeader?c.encode(w(JSON.stringify(this._protectedHeader))):c.encode(""),this._aad?(o=w(this._aad),a=d(i,c.encode("."),c.encode(o))):a=i,"DEF"===p.zip){let e=await ((null==t?void 0:t.deflateRaw)||er)(this._plaintext);({ciphertext:s,tag:u}=await e$(f,e,n,this._iv,a))}else({ciphertext:s,tag:u}=await e$(f,this._plaintext,n,this._iv,a));let g={ciphertext:w(s),iv:w(this._iv),tag:w(u)};return r&&(g.encrypted_key=w(r)),o&&(g.aad=o),this._protectedHeader&&(g.protected=l.decode(i)),this._sharedUnprotectedHeader&&(g.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(g.header=this._unprotectedHeader),g}}class e6{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addRecipient(...e){return this.parent.addRecipient(...e)}encrypt(...e){return this.parent.encrypt(...e)}done(){return this.parent}}class e8{constructor(e){this._recipients=[],this._plaintext=e}addRecipient(e,t){let r=new e6(this,e,{crit:null==t?void 0:t.crit});return this._recipients.push(r),r}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}async encrypt(e){var t,r,n;let a;if(!this._recipients.length)throw new x("at least one recipient must be added");if(e={deflateRaw:null==e?void 0:e.deflateRaw},1===this._recipients.length){let[t]=this._recipients,r=await new e3(this._plaintext).setAdditionalAuthenticatedData(this._aad).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(t.unprotectedHeader).encrypt(t.key,{...t.options,...e}),n={ciphertext:r.ciphertext,iv:r.iv,recipients:[{}],tag:r.tag};return r.aad&&(n.aad=r.aad),r.protected&&(n.protected=r.protected),r.unprotected&&(n.unprotected=r.unprotected),r.encrypted_key&&(n.recipients[0].encrypted_key=r.encrypted_key),r.header&&(n.recipients[0].header=r.header),n}for(let e=0;e<this._recipients.length;e++){let t=this._recipients[e];if(!en(this._protectedHeader,this._unprotectedHeader,t.unprotectedHeader))throw new x("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let r={...this._protectedHeader,...this._unprotectedHeader,...t.unprotectedHeader},{alg:n}=r;if("string"!=typeof n||!n)throw new x('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("dir"===n||"ECDH-ES"===n)throw new x('"dir" and "ECDH-ES" alg may only be used with a single recipient');if("string"!=typeof r.enc||!r.enc)throw new x('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(a){if(a!==r.enc)throw new x('JWE "enc" (Encryption Algorithm) Header Parameter must be the same for all recipients')}else a=r.enc;if(eF(x,new Map,t.options.crit,this._protectedHeader,r),void 0!==r.zip&&(!this._protectedHeader||!this._protectedHeader.zip))throw new x('JWE "zip" (Compression Algorithm) Header MUST be integrity protected')}let i=eS(a),o={ciphertext:"",iv:"",recipients:[],tag:""};for(let s=0;s<this._recipients.length;s++){let c=this._recipients[s],l={};o.recipients.push(l);let d=({...this._protectedHeader,...this._unprotectedHeader,...c.unprotectedHeader}).alg.startsWith("PBES2")?2048+s:void 0;if(0===s){let t=await new e3(this._plaintext).setAdditionalAuthenticatedData(this._aad).setContentEncryptionKey(i).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(c.unprotectedHeader).setKeyManagementParameters({p2c:d}).encrypt(c.key,{...c.options,...e,[e4]:!0});o.ciphertext=t.ciphertext,o.iv=t.iv,o.tag=t.tag,t.aad&&(o.aad=t.aad),t.protected&&(o.protected=t.protected),t.unprotected&&(o.unprotected=t.unprotected),l.encrypted_key=t.encrypted_key,t.header&&(l.header=t.header);continue}let{encryptedKey:u,parameters:p}=await e5((null===(t=c.unprotectedHeader)||void 0===t?void 0:t.alg)||(null===(r=this._protectedHeader)||void 0===r?void 0:r.alg)||(null===(n=this._unprotectedHeader)||void 0===n?void 0:n.alg),a,c.key,i,{p2c:d});l.encrypted_key=w(u),(c.unprotectedHeader||p)&&(l.header={...c.unprotectedHeader,...p})}return o}}function e9(e,t){let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:e.slice(-3)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"EdDSA":return{name:t.name};default:throw new A(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}function e7(e,t,r){if(o(t))return!function(e,t,...r){switch(t){case"HS256":case"HS384":case"HS512":{if(!B(e.algorithm,"HMAC"))throw J("HMAC");let r=parseInt(t.slice(2),10);if($(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!B(e.algorithm,"RSASSA-PKCS1-v1_5"))throw J("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if($(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!B(e.algorithm,"RSA-PSS"))throw J("RSA-PSS");let r=parseInt(t.slice(2),10);if($(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw J("Ed25519 or Ed448");break;case"ES256":case"ES384":case"ES512":{if(!B(e.algorithm,"ECDSA"))throw J("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw J(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}V(e,r)}(t,e,r),t;if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(F(t,...Y));return i.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}throw TypeError(F(t,...Y,"Uint8Array"))}let te=async(e,t,r,n)=>{let a=await e7(e,t,"verify");ew(e,a);let o=e9(e,a.algorithm);try{return await i.subtle.verify(o,a,r,n)}catch(e){return!1}};async function tt(e,t,r){var n;let a,i;if(!ea(e))throw new T("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new T('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new T("JWS Protected Header incorrect type");if(void 0===e.payload)throw new T("JWS Payload missing");if("string"!=typeof e.signature)throw new T("JWS Signature missing or incorrect type");if(void 0!==e.header&&!ea(e.header))throw new T("JWS Unprotected Header incorrect type");let o={};if(e.protected)try{let t=b(e.protected);o=JSON.parse(l.decode(t))}catch(e){throw new T("JWS Protected Header is invalid")}if(!en(o,e.header))throw new T("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let s={...o,...e.header},u=eF(T,new Map([["b64",!0]]),null==r?void 0:r.crit,o,s),p=!0;if(u.has("b64")&&"boolean"!=typeof(p=o.b64))throw new T('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:h}=s;if("string"!=typeof h||!h)throw new T('JWS "alg" (Algorithm) Header Parameter missing or invalid');let f=r&&ez("algorithms",r.algorithms);if(f&&!f.has(h))throw new _('"alg" (Algorithm) Header Parameter not allowed');if(p){if("string"!=typeof e.payload)throw new T("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new T("JWS Payload must be a string or an Uint8Array instance");let g=!1;"function"==typeof t&&(t=await t(o,e),g=!0),ej(h,t,"verify");let y=d(c.encode(null!==(n=e.protected)&&void 0!==n?n:""),c.encode("."),"string"==typeof e.payload?c.encode(e.payload):e.payload);try{a=b(e.signature)}catch(e){throw new T("Failed to base64url decode the signature")}if(!await te(h,t,a,y))throw new M;if(p)try{i=b(e.payload)}catch(e){throw new T("Failed to base64url decode the payload")}else i="string"==typeof e.payload?c.encode(e.payload):e.payload;let w={payload:i};return(void 0!==e.protected&&(w.protectedHeader=o),void 0!==e.header&&(w.unprotectedHeader=e.header),g)?{...w,key:t}:w}async function tr(e,t,r){if(e instanceof Uint8Array&&(e=l.decode(e)),"string"!=typeof e)throw new T("Compact JWS must be a string or Uint8Array");let{0:n,1:a,2:i,length:o}=e.split(".");if(3!==o)throw new T("Invalid Compact JWS");let s=await tt({payload:a,protected:n,signature:i},t,r),c={payload:s.payload,protectedHeader:s.protectedHeader};return"function"==typeof t?{...c,key:s.key}:c}async function tn(e,t,r){if(!ea(e))throw new T("General JWS must be an object");if(!Array.isArray(e.signatures)||!e.signatures.every(ea))throw new T("JWS Signatures missing or incorrect type");for(let n of e.signatures)try{return await tt({header:n.header,payload:e.payload,protected:n.protected,signature:n.signature},t,r)}catch(e){}throw new M}let ta=e=>Math.floor(e.getTime()/1e3),ti=/^(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i,to=e=>{let t=ti.exec(e);if(!t)throw TypeError("Invalid time period format");let r=parseFloat(t[1]);switch(t[2].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":return Math.round(r);case"minute":case"minutes":case"min":case"mins":case"m":return Math.round(60*r);case"hour":case"hours":case"hr":case"hrs":case"h":return Math.round(3600*r);case"day":case"days":case"d":return Math.round(86400*r);case"week":case"weeks":case"w":return Math.round(604800*r);default:return Math.round(31557600*r)}},ts=e=>e.toLowerCase().replace(/^application\//,""),tc=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),tl=(e,t,r={})=>{let n,a;let{typ:i}=r;if(i&&("string"!=typeof e.typ||ts(e.typ)!==ts(i)))throw new S('unexpected "typ" JWT header value',"typ","check_failed");try{n=JSON.parse(l.decode(t))}catch(e){}if(!ea(n))throw new R("JWT Claims Set must be a top-level JSON object");let{requiredClaims:o=[],issuer:s,subject:c,audience:d,maxTokenAge:u}=r;for(let e of(void 0!==u&&o.push("iat"),void 0!==d&&o.push("aud"),void 0!==c&&o.push("sub"),void 0!==s&&o.push("iss"),new Set(o.reverse())))if(!(e in n))throw new S(`missing required "${e}" claim`,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new S('unexpected "iss" claim value',"iss","check_failed");if(c&&n.sub!==c)throw new S('unexpected "sub" claim value',"sub","check_failed");if(d&&!tc(n.aud,"string"==typeof d?[d]:d))throw new S('unexpected "aud" claim value',"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=to(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=ta(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new S('"iat" claim must be a number',"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new S('"nbf" claim must be a number',"nbf","invalid");if(n.nbf>h+a)throw new S('"nbf" claim timestamp check failed',"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new S('"exp" claim must be a number',"exp","invalid");if(n.exp<=h-a)throw new E('"exp" claim timestamp check failed',"exp","check_failed")}if(u){let e=h-n.iat;if(e-a>("number"==typeof u?u:to(u)))throw new E('"iat" claim timestamp check failed (too far in the past)',"iat","check_failed");if(e<0-a)throw new S('"iat" claim timestamp check failed (it should be in the past)',"iat","check_failed")}return n};async function td(e,t,r){var n;let a=await tr(e,t,r);if((null===(n=a.protectedHeader.crit)||void 0===n?void 0:n.includes("b64"))&&!1===a.protectedHeader.b64)throw new R("JWTs MUST NOT use unencoded payload");let i={payload:tl(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...i,key:a.key}:i}async function tu(e,t,r){let n=await eY(e,t,r),a=tl(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new S('replicated "iss" claim header parameter mismatch',"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new S('replicated "sub" claim header parameter mismatch',"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new S('replicated "aud" claim header parameter mismatch',"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}class tp{constructor(e){this._flattened=new e3(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let th=async(e,t,r)=>{let n=await e7(e,t,"sign");return ew(e,n),new Uint8Array(await i.subtle.sign(e9(e,n.algorithm),n,r))};class tf{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new T("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!en(this._protectedHeader,this._unprotectedHeader))throw new T("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let n={...this._protectedHeader,...this._unprotectedHeader},a=eF(T,new Map([["b64",!0]]),null==t?void 0:t.crit,this._protectedHeader,n),i=!0;if(a.has("b64")&&"boolean"!=typeof(i=this._protectedHeader.b64))throw new T('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:o}=n;if("string"!=typeof o||!o)throw new T('JWS "alg" (Algorithm) Header Parameter missing or invalid');ej(o,e,"sign");let s=this._payload;i&&(s=c.encode(w(s)));let u=d(r=this._protectedHeader?c.encode(w(JSON.stringify(this._protectedHeader))):c.encode(""),c.encode("."),s),p={signature:w(await th(o,e,u)),payload:""};return i&&(p.payload=l.decode(s)),this._unprotectedHeader&&(p.header=this._unprotectedHeader),this._protectedHeader&&(p.protected=l.decode(r)),p}}class tg{constructor(e){this._flattened=new tf(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}class ty{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setProtectedHeader(e){if(this.protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this.protectedHeader=e,this}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addSignature(...e){return this.parent.addSignature(...e)}sign(...e){return this.parent.sign(...e)}done(){return this.parent}}class tw{constructor(e){this._signatures=[],this._payload=e}addSignature(e,t){let r=new ty(this,e,t);return this._signatures.push(r),r}async sign(){if(!this._signatures.length)throw new T("at least one signature must be added");let e={signatures:[],payload:""};for(let t=0;t<this._signatures.length;t++){let r=this._signatures[t],n=new tf(this._payload);n.setProtectedHeader(r.protectedHeader),n.setUnprotectedHeader(r.unprotectedHeader);let{payload:a,...i}=await n.sign(r.key,r.options);if(0===t)e.payload=a;else if(e.payload!==a)throw new T("inconsistent use of JWS Unencoded Payload (RFC7797)");e.signatures.push(i)}return e}}class tm{constructor(e){if(!ea(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:e}:this._payload={...this._payload,nbf:ta(new Date)+to(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:e}:this._payload={...this._payload,exp:ta(new Date)+to(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:ta(new Date)}:this._payload={...this._payload,iat:e},this}}class tb extends tm{setProtectedHeader(e){return this._protectedHeader=e,this}async sign(e,t){var r;let n=new tg(c.encode(JSON.stringify(this._payload)));if(n.setProtectedHeader(this._protectedHeader),Array.isArray(null===(r=this._protectedHeader)||void 0===r?void 0:r.crit)&&this._protectedHeader.crit.includes("b64")&&!1===this._protectedHeader.b64)throw new R("JWTs MUST NOT use unencoded payload");return n.sign(e,t)}}class tv extends tm{setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new tp(c.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let tS=(e,t)=>{if("string"!=typeof e||!e)throw new O(`${t} missing or invalid`)};async function tE(e,t){let r;if(!ea(e))throw TypeError("JWK must be an object");if(null!=t||(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":tS(e.crv,'"crv" (Curve) Parameter'),tS(e.x,'"x" (X Coordinate) Parameter'),tS(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":tS(e.crv,'"crv" (Subtype of Key Pair) Parameter'),tS(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":tS(e.e,'"e" (Exponent) Parameter'),tS(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":tS(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new A('"kty" (Key Type) Parameter missing or unsupported')}let n=c.encode(JSON.stringify(r));return w(await s(t,n))}async function t_(e,t){null!=t||(t="sha256");let r=await tE(e,t);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${t.slice(-3)}:${r}`}async function tA(e,t){let r={...e,...null==t?void 0:t.header};if(!ea(r.jwk))throw new T('"jwk" (JSON Web Key) Header Parameter must be a JSON object');let n=await eK({...r.jwk,ext:!0},r.alg,!0);if(n instanceof Uint8Array||"public"!==n.type)throw new T('"jwk" (JSON Web Key) Header Parameter must be a public key');return n}function tP(e){return e&&"object"==typeof e&&Array.isArray(e.keys)&&e.keys.every(tC)}function tC(e){return ea(e)}class tx{constructor(e){if(this._cached=new WeakMap,!tP(e))throw new k("JSON Web Key Set malformed");this._jwks=function(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e)}async getKey(e,t){let{alg:r,kid:n}={...e,...null==t?void 0:t.header},a=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new A('Unsupported "alg" value for a JSON Web Key Set')}}(r),i=this._jwks.keys.filter(e=>{let t=a===e.kty;if(t&&"string"==typeof n&&(t=n===e.kid),t&&"string"==typeof e.alg&&(t=r===e.alg),t&&"string"==typeof e.use&&(t="sig"===e.use),t&&Array.isArray(e.key_ops)&&(t=e.key_ops.includes("verify")),t&&"EdDSA"===r&&(t="Ed25519"===e.crv||"Ed448"===e.crv),t)switch(r){case"ES256":t="P-256"===e.crv;break;case"ES256K":t="secp256k1"===e.crv;break;case"ES384":t="P-384"===e.crv;break;case"ES512":t="P-521"===e.crv}return t}),{0:o,length:s}=i;if(0===s)throw new I;if(1!==s){let e=new N,{_cached:t}=this;throw e[Symbol.asyncIterator]=async function*(){for(let e of i)try{yield await tT(t,e,r)}catch(e){continue}},e}return tT(this._cached,o,r)}}async function tT(e,t,r){let n=e.get(t)||e.set(t,{}).get(t);if(void 0===n[r]){let e=await eK({...t,ext:!0},r);if(e instanceof Uint8Array||"public"!==e.type)throw new k("JSON Web Key Set members must be public keys");n[r]=e}return n[r]}function tR(e){let t=new tx(e);return async function(e,r){return t.getKey(e,r)}}let tO=async(e,t,r)=>{let n,a;let i=!1;"function"==typeof AbortController&&(n=new AbortController,a=setTimeout(()=>{i=!0,n.abort()},t));let o=await fetch(e.href,{signal:n?n.signal:void 0,redirect:"manual",headers:r.headers}).catch(e=>{if(i)throw new H;throw e});if(void 0!==a&&clearTimeout(a),200!==o.status)throw new v("Expected 200 OK from the JSON Web Key Set HTTP response");try{return await o.json()}catch(e){throw new v("Failed to parse the JSON Web Key Set HTTP response as JSON")}};class tk extends tx{constructor(e,t){if(super({keys:[]}),this._jwks=void 0,!(e instanceof URL))throw TypeError("url must be an instance of URL");this._url=new URL(e.href),this._options={agent:null==t?void 0:t.agent,headers:null==t?void 0:t.headers},this._timeoutDuration="number"==typeof(null==t?void 0:t.timeoutDuration)?null==t?void 0:t.timeoutDuration:5e3,this._cooldownDuration="number"==typeof(null==t?void 0:t.cooldownDuration)?null==t?void 0:t.cooldownDuration:3e4,this._cacheMaxAge="number"==typeof(null==t?void 0:t.cacheMaxAge)?null==t?void 0:t.cacheMaxAge:6e5}coolingDown(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cooldownDuration}fresh(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cacheMaxAge}async getKey(e,t){this._jwks&&this.fresh()||await this.reload();try{return await super.getKey(e,t)}catch(r){if(r instanceof I&&!1===this.coolingDown())return await this.reload(),super.getKey(e,t);throw r}}async reload(){this._pendingFetch&&("undefined"!=typeof WebSocketPair||"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent)&&(this._pendingFetch=void 0),this._pendingFetch||(this._pendingFetch=tO(this._url,this._timeoutDuration,this._options).then(e=>{if(!tP(e))throw new k("JSON Web Key Set malformed");this._jwks={keys:e.keys},this._jwksTimestamp=Date.now(),this._pendingFetch=void 0}).catch(e=>{throw this._pendingFetch=void 0,e})),await this._pendingFetch}}function tI(e,t){let r=new tk(e,t);return async function(e,t){return r.getKey(e,t)}}class tN extends tm{encode(){let e=w(JSON.stringify({alg:"none"})),t=w(JSON.stringify(this._payload));return`${e}.${t}.`}static decode(e,t){let r;if("string"!=typeof e)throw new R("Unsecured JWT must be a string");let{0:n,1:a,2:i,length:o}=e.split(".");if(3!==o||""!==i)throw new R("Invalid Unsecured JWT");try{if(r=JSON.parse(l.decode(b(n))),"none"!==r.alg)throw Error()}catch(e){throw new R("Invalid Unsecured JWT")}return{payload:tl(r,b(a),t),header:r}}}let tH=w,tM=b;function tD(e){let t;if("string"==typeof e){let r=e.split(".");(3===r.length||5===r.length)&&([t]=r)}else if("object"==typeof e&&e){if("protected"in e)t=e.protected;else throw TypeError("Token does not contain a Protected Header")}try{if("string"!=typeof t||!t)throw Error();let e=JSON.parse(l.decode(tM(t)));if(!ea(e))throw Error();return e}catch(e){throw TypeError("Invalid Token or Protected Header formatting")}}function tW(e){let t,r;if("string"!=typeof e)throw new R("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new R("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new R("Invalid JWT");if(!n)throw new R("JWTs must contain a payload");try{t=tM(n)}catch(e){throw new R("Failed to base64url decode the payload")}try{r=JSON.parse(l.decode(t))}catch(e){throw new R("Failed to parse the decoded payload as JSON")}if(!ea(r))throw new R("Invalid JWT Claims Set");return r}async function tK(e,t){var r;let n,a,o;switch(e){case"HS256":case"HS384":case"HS512":n=parseInt(e.slice(-3),10),a={name:"HMAC",hash:`SHA-${n}`,length:n},o=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return D(new Uint8Array((n=parseInt(e.slice(-3),10))>>3));case"A128KW":case"A192KW":case"A256KW":a={name:"AES-KW",length:n=parseInt(e.slice(1,4),10)},o=["wrapKey","unwrapKey"];break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":case"A128GCM":case"A192GCM":case"A256GCM":a={name:"AES-GCM",length:n=parseInt(e.slice(1,4),10)},o=["encrypt","decrypt"];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return i.subtle.generateKey(a,null!==(r=null==t?void 0:t.extractable)&&void 0!==r&&r,o)}function tU(e){var t;let r=null!==(t=null==e?void 0:e.modulusLength)&&void 0!==t?t:2048;if("number"!=typeof r||r<2048)throw new A("Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used");return r}async function tL(e,t){var r,n,a;let o,s;switch(e){case"PS256":case"PS384":case"PS512":o={name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tU(t)},s=["sign","verify"];break;case"RS256":case"RS384":case"RS512":o={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tU(t)},s=["sign","verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":o={name:"RSA-OAEP",hash:`SHA-${parseInt(e.slice(-3),10)||1}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tU(t)},s=["decrypt","unwrapKey","encrypt","wrapKey"];break;case"ES256":o={name:"ECDSA",namedCurve:"P-256"},s=["sign","verify"];break;case"ES384":o={name:"ECDSA",namedCurve:"P-384"},s=["sign","verify"];break;case"ES512":o={name:"ECDSA",namedCurve:"P-521"},s=["sign","verify"];break;case"EdDSA":s=["sign","verify"];let c=null!==(r=null==t?void 0:t.crv)&&void 0!==r?r:"Ed25519";switch(c){case"Ed25519":case"Ed448":o={name:c};break;default:throw new A("Invalid or unsupported crv option provided")}break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{s=["deriveKey","deriveBits"];let e=null!==(n=null==t?void 0:t.crv)&&void 0!==n?n:"P-256";switch(e){case"P-256":case"P-384":case"P-521":o={name:"ECDH",namedCurve:e};break;case"X25519":case"X448":o={name:e};break;default:throw new A("Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448")}break}default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return i.subtle.generateKey(o,null!==(a=null==t?void 0:t.extractable)&&void 0!==a&&a,s)}async function tj(e,t){return tL(e,t)}async function tJ(e,t){return tK(e,t)}let tB="WebCryptoAPI"}},e=>{var t=e(e.s=806);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map