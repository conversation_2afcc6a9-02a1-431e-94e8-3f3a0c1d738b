(()=>{var e={};e.id=908,e.ids=[908],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},34228:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d}),t(26647),t(39285),t(35866);var r=t(23191),a=t(88716),n=t(37922),l=t.n(n),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["courses",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26647)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\courses\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\courses\\[slug]\\page.tsx"],x="/courses/[slug]/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/courses/[slug]/page",pathname:"/courses/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9283:(e,s,t)=>{Promise.resolve().then(t.bind(t,73628))},33265:(e,s,t)=>{"use strict";t.d(s,{default:()=>a.a});var r=t(43353),a=t.n(r)},43353:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"default",{enumerable:!0,get:function(){return n}});let r=t(91174);t(10326),t(17577);let a=r._(t(77028));function n(e,s){var t;let r={loading:e=>{let{error:s,isLoading:t,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let n={...r,...s};return(0,a.default)({...n,modules:null==(t=n.loadableGenerated)?void 0:t.modules})}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},933:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let r=t(94129);function a(e){let{reason:s,children:t}=e;throw new r.BailoutToCSRError(s)}},77028:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"default",{enumerable:!0,get:function(){return d}});let r=t(10326),a=t(17577),n=t(933),l=t(46618);function i(e){return{default:e&&"default"in e?e.default:e}}let c={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let s={...c,...e},t=(0,a.lazy)(()=>s.loader().then(i)),d=s.loading;function o(e){let i=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,c=s.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.PreloadCss,{moduleIds:s.modules}),(0,r.jsx)(t,{...e})]}):(0,r.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(t,{...e})});return(0,r.jsx)(a.Suspense,{fallback:i,children:c})}return o.displayName="LoadableComponent",o}},46618:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"PreloadCss",{enumerable:!0,get:function(){return n}});let r=t(10326),a=t(54580);function n(e){let{moduleIds:s}=e,t=(0,a.getExpectedRequestStore)("next/dynamic css"),n=[];if(t.reactLoadableManifest&&s){let e=t.reactLoadableManifest;for(let t of s){if(!e[t])continue;let s=e[t].files.filter(e=>e.endsWith(".css"));n.push(...s)}}return 0===n.length?null:(0,r.jsx)(r.Fragment,{children:n.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:t.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},73628:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(10326),a=t(17577),n=t(77109),l=t(35047),i=t(46226),c=t(90434),d=t(99837),o=t(47375),x=t(36792),m=t(16545),u=t(60962),h=t(79360),p=t(51223);let g=(0,h.j)("relative bg-white rounded-lg shadow-xl transform transition-all",{variants:{variant:{default:"bg-white",destructive:"bg-white border-l-4 border-red-500",success:"bg-white border-l-4 border-green-500",warning:"bg-white border-l-4 border-yellow-500",info:"bg-white border-l-4 border-blue-500"},size:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl"}},defaultVariants:{variant:"default",size:"md"}});function f({isOpen:e,onClose:s,title:t,children:n,variant:l,size:i,className:c,closeOnOverlayClick:d=!0,showCloseButton:o=!0}){let[x,m]=a.useState(!1);if(a.useEffect(()=>(m(!0),()=>m(!1)),[]),a.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),a.useEffect(()=>{let t=e=>{"Escape"===e.key&&s()};return e&&document.addEventListener("keydown",t),()=>{document.removeEventListener("keydown",t)}},[e,s]),!x||!e)return null;let h=(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:d?s:void 0,"aria-hidden":"true"}),(0,r.jsxs)("div",{className:(0,p.cn)(g({variant:l,size:i}),"w-full",c),role:"dialog","aria-modal":"true","aria-labelledby":t?"modal-title":void 0,children:[(t||o)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[t&&r.jsx("h2",{id:"modal-title",className:"text-lg font-semibold text-gray-900",children:t}),o&&r.jsx("button",{onClick:s,className:"p-1 rounded-full hover:bg-gray-100 transition-colors","aria-label":"Đ\xf3ng modal",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsx("div",{className:"p-6",children:n})]})]});return(0,u.createPortal)(h,document.body)}var b=t(20603);let j=(0,t(33265).default)(async()=>{},{loadableGenerated:{modules:["app\\courses\\[slug]\\page.tsx -> @/components/payment/PaymentForm"]},loading:()=>r.jsx(m.gb,{text:"Đang tải form thanh to\xe1n..."}),ssr:!1});function v({params:e}){var s,t;let{data:u}=(0,n.useSession)(),h=(0,l.useRouter)(),{addToast:p}=(0,b.pm)(),[g,v]=(0,a.useState)(null),[y,N]=(0,a.useState)(!0),[w,D]=(0,a.useState)(null),[k,C]=(0,a.useState)(!1),[P,_]=(0,a.useState)(!1),[M,S]=(0,a.useState)("overview"),E=async(e,s)=>{if(!u){h.push("/auth/signin");return}try{C(!0);let t=await fetch(`/api/courses/${g?._id}/enroll`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e,...s})}),r=await t.json();r.success?(p({message:"Đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng!",variant:"success"}),window.location.reload()):p({message:r.error||"Lỗi khi đăng k\xfd kh\xf3a học",variant:"error"})}catch(e){p({message:"Lỗi kết nối server",variant:"error"})}finally{C(!1),_(!1)}},z=e=>{let s=Math.floor(e/60),t=e%60;return s>0?`${s}h ${t}m`:`${t}m`};return y?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(m.gb,{size:"lg"})}):w||!g?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDE1E"}),r.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Kh\xf4ng t\xecm thấy kh\xf3a học"}),r.jsx("p",{className:"text-gray-600 mb-4",children:w}),r.jsx(c.default,{href:"/courses",children:r.jsx(d.z,{children:"Về danh s\xe1ch kh\xf3a học"})})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"bg-white shadow-sm",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[r.jsx(x.C,{variant:"secondary",children:g.category.name}),r.jsx(x.C,{variant:"outline",children:g.level}),r.jsx(x.C,{variant:"outline",children:g.language})]}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:g.title}),r.jsx("p",{className:"text-lg text-gray-600 mb-6",children:g.shortDescription}),(0,r.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:"\uD83D\uDC68‍\uD83C\uDFEB"}),(0,r.jsxs)("span",{children:[g.instructor.profile.firstName," ",g.instructor.profile.lastName]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:"\uD83D\uDC65"}),(0,r.jsxs)("span",{children:[g.stats.totalStudents," học vi\xean"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:"\uD83D\uDCDA"}),(0,r.jsxs)("span",{children:[g.stats.totalLessons," b\xe0i học"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:"⏱️"}),r.jsx("span",{children:z(g.stats.totalDuration)})]})]}),g.stats.totalRatings>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("span",{className:"text-yellow-400",children:"⭐"}),r.jsx("span",{className:"font-semibold ml-1",children:g.stats.averageRating.toFixed(1)})]}),(0,r.jsxs)("span",{className:"text-gray-500",children:["(",g.stats.totalRatings," đ\xe1nh gi\xe1)"]})]})]}),r.jsx("div",{className:"lg:col-span-1",children:(0,r.jsxs)(o.Zb,{className:"sticky top-4",children:[r.jsx("div",{className:"aspect-video bg-gray-200 rounded-t-lg relative overflow-hidden",children:g.thumbnail?r.jsx(i.default,{src:g.thumbnail,alt:g.title,fill:!0,className:"object-cover",priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):r.jsx("div",{className:"flex items-center justify-center h-full text-gray-400 text-4xl",children:"\uD83D\uDCDA"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-3xl font-bold text-primary mb-2",children:(s=g.pricing.basePrice,t=g.pricing.currency,0===s?"Miễn ph\xed":new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"===t?"VND":"USD"}).format(s))}),g.pricing.basePrice>0&&r.jsx("div",{className:"text-sm text-gray-500",children:"Thanh to\xe1n một lần"})]}),g.enrollment?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-green-800 mb-2",children:[r.jsx("span",{children:"✅"}),r.jsx("span",{className:"font-semibold",children:"Đ\xe3 đăng k\xfd"})]}),(0,r.jsxs)("div",{className:"text-sm text-green-700",children:["Tiến độ: ",g.enrollment.progress.completionPercentage,"% (",g.enrollment.progress.completedLessons,"/",g.enrollment.progress.totalLessons," b\xe0i học)"]})]}),r.jsx(d.z,{className:"w-full",onClick:()=>h.push(`/learn/${g.slug}`),children:"Tiếp tục học"})]}):g.canEnroll?(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(d.z,{className:"w-full",onClick:()=>{0===g.pricing.basePrice?E("free"):_(!0)},disabled:k,children:k?r.jsx(m.gb,{size:"sm"}):"Đăng k\xfd ngay"}),g.pricing.basePrice>0&&r.jsx("div",{className:"text-center",children:r.jsx("button",{onClick:()=>_(!0),className:"text-sm text-primary hover:underline",children:"C\xf3 m\xe3 k\xedch hoạt?"})})]}):r.jsx("div",{className:"text-center text-gray-500",children:"Kh\xf3a học kh\xf4ng khả dụng"}),g.canEdit&&r.jsx("div",{className:"mt-4 pt-4 border-t",children:r.jsx(c.default,{href:`/instructor/courses/${g.slug}/edit`,children:r.jsx(d.z,{variant:"outline",className:"w-full",children:"Chỉnh sửa kh\xf3a học"})})})]})]})})]})})}),r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[r.jsx("div",{className:"border-b border-gray-200",children:r.jsx("nav",{className:"flex space-x-8 px-6",children:[{id:"overview",label:"Tổng quan"},{id:"curriculum",label:"Chương tr\xecnh học"},{id:"instructor",label:"Giảng vi\xean"},{id:"reviews",label:"Đ\xe1nh gi\xe1"}].map(e=>r.jsx("button",{onClick:()=>S(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm ${M===e.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:e.label},e.id))})}),(0,r.jsxs)("div",{className:"p-6",children:["overview"===M&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"M\xf4 tả kh\xf3a học"}),r.jsx("div",{className:"prose max-w-none",children:r.jsx("p",{className:"text-gray-700 leading-relaxed",children:g.content.description})})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Mục ti\xeau học tập"}),r.jsx("ul",{className:"space-y-2",children:g.content.objectives.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start gap-3",children:[r.jsx("span",{className:"text-green-500 mt-1",children:"✓"}),r.jsx("span",{className:"text-gray-700",children:e})]},s))})]}),g.content.prerequisites.length>0&&(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Y\xeau cầu trước khi học"}),r.jsx("ul",{className:"space-y-2",children:g.content.prerequisites.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start gap-3",children:[r.jsx("span",{className:"text-blue-500 mt-1",children:"•"}),r.jsx("span",{className:"text-gray-700",children:e})]},s))})]})]}),"curriculum"===M&&(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("h3",{className:"text-xl font-semibold",children:"Chương tr\xecnh học"}),g.content.syllabus.map((e,s)=>(0,r.jsxs)(o.Zb,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("h4",{className:"font-semibold text-lg",children:["Tuần ",e.week,": ",e.title]}),r.jsx(x.C,{variant:"outline",children:z(e.duration)})]}),r.jsx("ul",{className:"space-y-1",children:e.topics.map((e,s)=>(0,r.jsxs)("li",{className:"text-gray-600 text-sm",children:["• ",e]},s))})]},s)),g.lessons.length>0&&(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h4",{className:"font-semibold text-lg mb-4",children:"Danh s\xe1ch b\xe0i học"}),r.jsx("div",{className:"space-y-2",children:g.lessons.map((e,s)=>(0,r.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border ${e.canAccess?"bg-white":"bg-gray-50"}`,children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx("span",{className:"text-sm text-gray-500 w-8",children:e.order}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:`font-medium ${e.canAccess?"text-gray-900":"text-gray-500"}`,children:e.title}),r.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(x.C,{variant:"outline",size:"sm",children:e.type}),e.settings.isPreview&&r.jsx(x.C,{variant:"secondary",size:"sm",children:"Xem trước"}),!e.canAccess&&r.jsx("span",{className:"text-gray-400",children:"\uD83D\uDD12"})]})]},e._id))})]})]}),"instructor"===M&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center",children:g.instructor.profile.avatar?r.jsx(i.default,{src:g.instructor.profile.avatar,alt:`${g.instructor.profile.firstName} ${g.instructor.profile.lastName}`,width:64,height:64,className:"rounded-full"}):r.jsx("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold",children:[g.instructor.profile.firstName," ",g.instructor.profile.lastName]}),r.jsx("p",{className:"text-gray-600",children:"Giảng vi\xean"})]})]}),g.instructor.profile.bio&&(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-2",children:"Giới thiệu"}),r.jsx("p",{className:"text-gray-700 leading-relaxed",children:g.instructor.profile.bio})]})]}),"reviews"===M&&r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"text-4xl mb-4",children:"⭐"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Đ\xe1nh gi\xe1 sẽ sớm c\xf3 mặt"}),r.jsx("p",{className:"text-gray-600",children:"T\xednh năng đ\xe1nh gi\xe1 đang được ph\xe1t triển"})]})})]})]})}),r.jsx(f,{isOpen:P,onClose:()=>_(!1),title:"Đăng k\xfd kh\xf3a học",size:"lg",children:r.jsx(a.Suspense,{fallback:r.jsx(m.gb,{text:"Đang tải form thanh to\xe1n..."}),children:r.jsx(j,{courseId:g._id,courseTitle:g.title,amount:g.pricing.basePrice,currency:g.pricing.currency,onSuccess:e=>{p({message:"Đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng!",variant:"success"}),_(!1),window.location.reload()},onError:e=>{p({message:e,variant:"error"})},onCancel:()=>_(!1)})})})]})}},36792:(e,s,t)=>{"use strict";t.d(s,{C:()=>i});var r=t(10326);t(17577);var a=t(79360),n=t(51223);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...t}){return r.jsx("div",{className:(0,n.cn)(l({variant:s}),e),...t})}},47375:(e,s,t)=>{"use strict";t.d(s,{Zb:()=>c});var r=t(10326),a=t(17577),n=t(79360),l=t(51223);let i=(0,n.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),c=a.forwardRef(({className:e,variant:s,size:t,...a},n)=>r.jsx("div",{ref:n,className:(0,l.cn)(i({variant:s,size:t}),e),...a}));c.displayName="Card",a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription",a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s})).displayName="CardContent",a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},16545:(e,s,t)=>{"use strict";t.d(s,{gb:()=>i});var r=t(10326),a=t(79360),n=t(51223);let l=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function i({variant:e,size:s,className:t,text:a}){return r.jsx("div",{className:(0,n.cn)("flex items-center justify-center",t),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[r.jsx("div",{className:(0,n.cn)(l({variant:e,size:s}))}),a&&r.jsx("p",{className:"text-sm text-gray-600",children:a})]})})}},26647:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\courses\[slug]\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,105,226,826],()=>t(34228));module.exports=r})();