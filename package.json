{"name": "webta-lms", "version": "0.1.0", "private": true, "description": "<PERSON><PERSON> thống LMS Next.js fullstack cho vi<PERSON><PERSON> bán và quản lý khóa học ngoại ngữ với tính năng đánh giá tự động bằng AI", "keywords": ["lms", "nextjs", "typescript", "mongodb", "ai", "education"], "author": "WebTA Team", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "cross-env ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "tsx scripts/seed-database.ts", "db:reset": "tsx scripts/reset.ts", "verify:phase-0": "node scripts/verify-phase-0.js", "setup": "npm install && npm run verify:phase-0"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next-auth/mongodb-adapter": "^1.1.3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.14.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "cloudinary": "^1.41.0", "clsx": "^2.0.0", "dotenv": "^16.5.0", "lucide-react": "^0.294.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "next": "^14.0.4", "next-auth": "^4.24.5", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "stripe": "^14.9.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/multer": "^1.4.11", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3", "tsx": "^4.6.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}