"use strict";(()=>{var e={};e.id=882,e.ids=[882,544],e.modules={38013:e=>{e.exports=require("mongodb")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},35643:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>S,patchFetch:()=>w,requestAsyncStorage:()=>E,routeModule:()=>y,serverHooks:()=>b,staticGenerationAsyncStorage:()=>v});var r={};s.r(r),s.d(r,{GET:()=>g});var i=s(49303),n=s(88716),a=s(60670),o=s(87070),l=s(75571),u=s(95456),c=s(14184),d=s(89332),p=s(66820),m=s(93330);async function g(e){try{await (0,c.ZP)();let e=await (0,l.getServerSession)(u.L);if(!e?.user?.id)return o.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});let t=await m.ZP.findById(e.user.id);if(!t||t.role!==m.i4.INSTRUCTOR&&t.role!==m.i4.ADMIN)return o.NextResponse.json({success:!1,error:"Bạn kh\xf4ng c\xf3 quyền truy cập trang n\xe0y"},{status:403});let s=await d.ZP.find({instructor:e.user.id}).select("title slug status thumbnail stats pricing createdAt publishedAt").sort({createdAt:-1}).lean(),r={totalCourses:s.length,publishedCourses:s.filter(e=>e.status===d.D4.PUBLISHED).length,totalStudents:s.reduce((e,t)=>e+(t.stats?.totalStudents||0),0),totalRevenue:0,averageRating:0,totalRatings:0},i=s.filter(e=>e.stats?.totalRatings>0);if(i.length>0){let e=i.reduce((e,t)=>e+t.stats.averageRating*t.stats.totalRatings,0);r.totalRatings=i.reduce((e,t)=>e+t.stats.totalRatings,0),r.averageRating=r.totalRatings>0?e/r.totalRatings:0}let n=s.map(e=>e._id),a=await p.default.find({courseId:{$in:n},status:{$in:[p.mh.ACTIVE,p.mh.COMPLETED]}}).select("payment.amount").lean();r.totalRevenue=a.reduce((e,t)=>e+(t.payment?.amount||0),0);let g=new Date(Date.now()-2592e6),y=await p.default.find({courseId:{$in:n},createdAt:{$gte:g}}).populate("userId","profile.firstName profile.lastName").populate("courseId","title").sort({createdAt:-1}).limit(20).lean(),E=await p.default.find({courseId:{$in:n},"progress.status":"completed",updatedAt:{$gte:g}}).populate("userId","profile.firstName profile.lastName").populate("courseId","title").sort({updatedAt:-1}).limit(20).lean(),v=[...y.map(e=>({type:"enrollment",studentName:`${e.userId.profile.firstName} ${e.userId.profile.lastName}`,courseName:e.courseId.title,timestamp:e.createdAt,details:{enrollmentId:e._id,amount:e.payment?.amount||0}})),...E.map(e=>({type:"completion",studentName:`${e.userId.profile.firstName} ${e.userId.profile.lastName}`,courseName:e.courseId.title,timestamp:e.updatedAt,details:{enrollmentId:e._id,completionPercentage:e.progress.completionPercentage}}))].sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),b=await h(n),S=s.filter(e=>e.status===d.D4.PUBLISHED).sort((e,t)=>(t.stats?.totalStudents||0)-(e.stats?.totalStudents||0)).slice(0,5),w=await f(n);return o.NextResponse.json({success:!0,data:{courses:s,stats:r,recentActivity:v.slice(0,20),monthlyRevenue:b,topCourses:S,courseMetrics:w}})}catch(e){return console.error("Error fetching instructor dashboard:",e),o.NextResponse.json({success:!1,error:"Lỗi server khi tải dashboard"},{status:500})}}async function h(e){try{let t=new Date;t.setMonth(t.getMonth()-12);let s=await p.default.aggregate([{$match:{courseId:{$in:e},"payment.paidAt":{$gte:t},status:{$in:[p.mh.ACTIVE,p.mh.COMPLETED]}}},{$group:{_id:{year:{$year:"$payment.paidAt"},month:{$month:"$payment.paidAt"}},revenue:{$sum:"$payment.amount"},enrollments:{$sum:1}}},{$sort:{"_id.year":1,"_id.month":1}}]),r=[],i=new Date;for(let e=11;e>=0;e--){let t=new Date(i.getFullYear(),i.getMonth()-e,1),n=t.getFullYear(),a=t.getMonth()+1,o=s.find(e=>e._id.year===n&&e._id.month===a);r.push({month:t.toLocaleDateString("vi-VN",{month:"short",year:"numeric"}),revenue:o?.revenue||0,enrollments:o?.enrollments||0})}return r}catch(e){return console.error("Error getting monthly revenue:",e),[]}}async function f(e){try{return await p.default.aggregate([{$match:{courseId:{$in:e}}},{$group:{_id:"$courseId",totalEnrollments:{$sum:1},activeEnrollments:{$sum:{$cond:[{$eq:["$status","active"]},1,0]}},completedEnrollments:{$sum:{$cond:[{$eq:["$status","completed"]},1,0]}},averageProgress:{$avg:"$progress.completionPercentage"},totalRevenue:{$sum:"$payment.amount"},averageWatchTime:{$avg:"$progress.totalWatchTime"}}},{$lookup:{from:"courses",localField:"_id",foreignField:"_id",as:"course"}},{$unwind:"$course"},{$project:{courseTitle:"$course.title",courseSlug:"$course.slug",totalEnrollments:1,activeEnrollments:1,completedEnrollments:1,completionRate:{$cond:[{$gt:["$totalEnrollments",0]},{$multiply:[{$divide:["$completedEnrollments","$totalEnrollments"]},100]},0]},averageProgress:{$round:["$averageProgress",1]},totalRevenue:1,averageWatchTime:{$round:[{$divide:["$averageWatchTime",60]},1]}}},{$sort:{totalEnrollments:-1}}])}catch(e){return console.error("Error getting course metrics:",e),[]}}let y=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/dashboard/instructor/route",pathname:"/api/dashboard/instructor",filename:"route",bundlePath:"app/api/dashboard/instructor/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\dashboard\\instructor\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:E,staticGenerationAsyncStorage:v,serverHooks:b}=y,S="/api/dashboard/instructor/route";function w(){return(0,a.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:v})}},95456:(e,t,s)=>{s.d(t,{L:()=>c});var r=s(53797),i=s(77234),n=s(41017),a=s(38013),o=s(14184),l=s(93330);let u=new a.MongoClient(process.env.MONGODB_URI).connect(),c={adapter:(0,n.dJ)(u),secret:process.env.NEXTAUTH_SECRET,providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email v\xe0 mật khẩu l\xe0 bắt buộc");try{await (0,o.ZP)();let t=await l.ZP.findOne({email:e.email.toLowerCase()}).select("+password");if(!t)throw Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");if(t.isLocked())throw Error("T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần");if(t.status!==l.J0.ACTIVE)throw Error("T\xe0i khoản chưa được k\xedch hoạt");if(!await t.comparePassword(e.password))throw await t.incrementLoginAttempts(),Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");return t.loginAttempts>0&&await t.updateOne({$unset:{loginAttempts:1,lockUntil:1}}),await t.updateOne({lastLogin:new Date}),{id:t._id.toString(),email:t.email,name:t.profile.fullName,role:t.role,status:t.status,emailVerified:t.emailVerified,image:t.profile.avatar}}catch(e){throw Error(e.message||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập")}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:e,user:t,account:s})=>(s&&t&&(e.role=t.role,e.status=t.status,e.emailVerified=t.emailVerified),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.status=t.status,e.user.emailVerified=t.emailVerified),e),signIn:async({user:e,account:t,profile:s})=>t?.provider!=="credentials"||!0===e.emailVerified,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:s,isNewUser:r}){console.log(`User ${e.email} signed in with ${t?.provider}`)},async signOut({session:e,token:t}){console.log("User signed out")},async createUser({user:e}){console.log(`New user created: ${e.email}`)}},debug:!1}},14184:(e,t,s)=>{s.d(t,{ZP:()=>o});var r=s(11185),i=s.n(r);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=i().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},89332:(e,t,s)=>{s.d(t,{D4:()=>r,ZP:()=>u,f:()=>i,ij:()=>n});var r,i,n,a=s(11185),o=s.n(a);(function(e){e.DRAFT="draft",e.PUBLISHED="published",e.ARCHIVED="archived",e.SUSPENDED="suspended"})(r||(r={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(i||(i={})),function(e){e.ENGLISH="english",e.VIETNAMESE="vietnamese",e.CHINESE="chinese",e.JAPANESE="japanese",e.KOREAN="korean",e.FRENCH="french",e.GERMAN="german",e.SPANISH="spanish"}(n||(n={}));let l=new a.Schema({title:{type:String,required:[!0,"Ti\xeau đề kh\xf3a học l\xe0 bắt buộc"],trim:!0,maxlength:[200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"]},slug:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},shortDescription:{type:String,required:[!0,"M\xf4 tả ngắn l\xe0 bắt buộc"],maxlength:[500,"M\xf4 tả ngắn kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},content:{description:{type:String,required:[!0,"M\xf4 tả chi tiết l\xe0 bắt buộc"]},objectives:[{type:String,required:!0}],prerequisites:[String],syllabus:[{week:{type:Number,required:!0},title:{type:String,required:!0},topics:[String],duration:{type:Number,required:!0}}]},instructor:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"Giảng vi\xean l\xe0 bắt buộc"]},category:{type:a.Schema.Types.ObjectId,ref:"Category",required:[!0,"Danh mục l\xe0 bắt buộc"]},subcategory:{type:a.Schema.Types.ObjectId,ref:"Category"},language:{type:String,enum:Object.values(n),required:[!0,"Ng\xf4n ngữ kh\xf3a học l\xe0 bắt buộc"]},level:{type:String,enum:Object.values(i),required:[!0,"Cấp độ kh\xf3a học l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"draft"},thumbnail:String,previewVideo:String,tags:[String],pricing:{basePrice:{type:Number,required:[!0,"Gi\xe1 kh\xf3a học l\xe0 bắt buộc"],min:[0,"Gi\xe1 kh\xf4ng được \xe2m"]},currency:{type:String,default:"VND"},discountPrice:{type:Number,min:[0,"Gi\xe1 giảm kh\xf4ng được \xe2m"]},discountValidUntil:Date,installmentOptions:{enabled:{type:Boolean,default:!1},plans:[{months:{type:Number,required:!0},monthlyAmount:{type:Number,required:!0}}]}},settings:{maxStudents:{type:Number,min:[1,"Số học vi\xean tối đa phải \xedt nhất l\xe0 1"]},allowComments:{type:Boolean,default:!0},allowRatings:{type:Boolean,default:!0},certificateEnabled:{type:Boolean,default:!0},downloadableResources:{type:Boolean,default:!0},mobileAccess:{type:Boolean,default:!0},lifetimeAccess:{type:Boolean,default:!0},accessDuration:{type:Number,min:[1,"Thời gian truy cập phải \xedt nhất 1 ng\xe0y"]}},stats:{totalStudents:{type:Number,default:0},totalLessons:{type:Number,default:0},totalDuration:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRatings:{type:Number,default:0},completionRate:{type:Number,default:0,min:0,max:100}},seo:{metaTitle:{type:String,maxlength:[60,"Meta title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},metaDescription:{type:String,maxlength:[160,"Meta description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},publishedAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});l.index({slug:1}),l.index({instructor:1}),l.index({category:1,subcategory:1}),l.index({language:1,level:1}),l.index({status:1,publishedAt:-1}),l.index({"stats.averageRating":-1}),l.index({"stats.totalStudents":-1}),l.index({tags:1}),l.index({"pricing.basePrice":1}),l.virtual("isOnSale").get(function(){return!!(this.pricing.discountPrice&&this.pricing.discountValidUntil&&this.pricing.discountValidUntil>new Date)}),l.virtual("currentPrice").get(function(){return this.isOnSale?this.pricing.discountPrice:this.pricing.basePrice}),l.pre("save",function(e){this.isModified("title")&&!this.slug&&(this.slug=this.generateSlug()),e()}),l.methods.generateSlug=function(){return this.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},l.methods.updateStats=async function(){console.log("Updating course stats...")},l.methods.isPublished=function(){return"published"===this.status&&!!this.publishedAt},l.methods.canEnroll=function(){return!!this.isPublished()&&(!this.settings.maxStudents||!(this.stats.totalStudents>=this.settings.maxStudents))};let u=o().models.Course||o().model("Course",l)},66820:(e,t,s)=>{s.d(t,{default:()=>c,mh:()=>r,xM:()=>i});var r,i,n,a=s(11185),o=s.n(a);(function(e){e.ACTIVE="active",e.COMPLETED="completed",e.SUSPENDED="suspended",e.EXPIRED="expired",e.REFUNDED="refunded"})(r||(r={})),function(e){e.PAID="paid",e.FREE="free",e.ACTIVATION_CODE="activation_code",e.ADMIN_GRANTED="admin_granted"}(i||(i={})),function(e){e.NOT_STARTED="not_started",e.IN_PROGRESS="in_progress",e.COMPLETED="completed"}(n||(n={}));let l=new a.Schema({lessonId:{type:a.Schema.Types.ObjectId,ref:"Lesson",required:!0},status:{type:String,enum:Object.values(n),default:"not_started"},startedAt:Date,completedAt:Date,watchTime:{type:Number,default:0,min:0},lastPosition:{type:Number,default:0,min:0},attempts:{type:Number,default:0,min:0},score:{type:Number,min:0,max:100},notes:String},{_id:!1}),u=new a.Schema({userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID l\xe0 bắt buộc"]},courseId:{type:a.Schema.Types.ObjectId,ref:"Course",required:[!0,"Course ID l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"active"},type:{type:String,enum:Object.values(i),required:[!0,"Loại đăng k\xfd l\xe0 bắt buộc"]},progress:{status:{type:String,enum:Object.values(n),default:"not_started"},completedLessons:{type:Number,default:0,min:0},totalLessons:{type:Number,default:0,min:0},completionPercentage:{type:Number,default:0,min:0,max:100},totalWatchTime:{type:Number,default:0,min:0},lastAccessedAt:{type:Date,default:Date.now},estimatedCompletionDate:Date,lessons:[l]},payment:{amount:{type:Number,required:!0,min:0},currency:{type:String,default:"VND"},method:{type:String,enum:["stripe","paypal","activation_code","free"],required:!0},transactionId:String,activationCode:String,paidAt:Date,refundedAt:Date,refundAmount:{type:Number,min:0}},certificate:{issued:{type:Boolean,default:!1},issuedAt:Date,certificateId:String,downloadUrl:String,validUntil:Date},accessGrantedAt:{type:Date,default:Date.now},accessExpiresAt:Date,lastAccessedAt:{type:Date,default:Date.now},enrolledBy:{type:a.Schema.Types.ObjectId,ref:"User"},notes:String},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});u.index({userId:1,courseId:1},{unique:!0}),u.index({userId:1,status:1}),u.index({courseId:1,status:1}),u.index({status:1,accessExpiresAt:1}),u.index({"payment.method":1,"payment.paidAt":-1}),u.index({"progress.status":1}),u.index({"progress.completionPercentage":-1}),u.index({lastAccessedAt:-1}),u.virtual("isActive").get(function(){return"active"===this.status&&this.canAccess()}),u.virtual("daysRemaining").get(function(){if(!this.accessExpiresAt)return null;let e=new Date;return Math.ceil((this.accessExpiresAt.getTime()-e.getTime())/864e5)}),u.pre("save",async function(e){this.isModified("progress.lessons")&&(this.progress.completedLessons=this.progress.lessons.filter(e=>"completed"===e.status).length,this.progress.completionPercentage=this.calculateCompletionPercentage(),0===this.progress.completionPercentage?this.progress.status="not_started":100===this.progress.completionPercentage?(this.progress.status="completed",this.status="completed"):this.progress.status="in_progress"),e()}),u.methods.updateProgress=async function(){let e=o().model("Lesson"),t=await e.find({courseId:this.courseId,status:"published"}).sort({order:1});for(let e of(this.progress.totalLessons=t.length,t))this.progress.lessons.find(t=>t.lessonId.toString()===e._id.toString())||this.progress.lessons.push({lessonId:e._id,status:"not_started",watchTime:0,lastPosition:0,attempts:0});this.progress.totalWatchTime=this.progress.lessons.reduce((e,t)=>e+t.watchTime,0),await this.save()},u.methods.calculateCompletionPercentage=function(){return 0===this.progress.totalLessons?0:Math.round(this.progress.completedLessons/this.progress.totalLessons*100)},u.methods.isExpired=function(){return!!this.accessExpiresAt&&new Date>this.accessExpiresAt},u.methods.canAccess=function(){return!("active"!==this.status||this.isExpired())},u.methods.generateCertificate=async function(){if("completed"!==this.progress.status)throw Error("Kh\xf3a học chưa ho\xe0n th\xe0nh");if(this.certificate.issued)throw Error("Chứng chỉ đ\xe3 được cấp");let e=`CERT-${this.courseId}-${this.userId}-${Date.now()}`;this.certificate={issued:!0,issuedAt:new Date,certificateId:e,downloadUrl:`/api/certificates/${e}`,validUntil:new Date(Date.now()+31536e6)},await this.save()},u.methods.extendAccess=async function(e){this.accessExpiresAt?this.accessExpiresAt=new Date(this.accessExpiresAt.getTime()+864e5*e):this.accessExpiresAt=new Date(Date.now()+864e5*e),await this.save()},u.statics.getActiveEnrollments=function(e){return this.find({userId:e,status:"active",$or:[{accessExpiresAt:{$exists:!1}},{accessExpiresAt:{$gt:new Date}}]}).populate("courseId")},u.statics.getCourseStats=async function(e){return(await this.aggregate([{$match:{courseId:new(o()).Types.ObjectId(e)}},{$group:{_id:null,totalEnrollments:{$sum:1},activeEnrollments:{$sum:{$cond:[{$eq:["$status","active"]},1,0]}},completedEnrollments:{$sum:{$cond:[{$eq:["$status","completed"]},1,0]}},averageProgress:{$avg:"$progress.completionPercentage"},totalRevenue:{$sum:"$payment.amount"}}}]))[0]||{totalEnrollments:0,activeEnrollments:0,completedEnrollments:0,averageProgress:0,totalRevenue:0}};let c=o().models.Enrollment||o().model("Enrollment",u)},93330:(e,t,s)=>{s.d(t,{J0:()=>i,ZP:()=>d,i4:()=>r});var r,i,n,a=s(11185),o=s.n(a),l=s(42023),u=s.n(l);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(r||(r={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(i||(i={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(n||(n={}));let c=new a.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(r),default:"student"},status:{type:String,enum:Object.values(i),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(n)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});c.index({"profile.firstName":1,"profile.lastName":1}),c.index({role:1,status:1}),c.index({createdAt:-1}),c.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),c.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),c.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await u().genSalt(12);this.password=await u().hash(this.password,t),e()}catch(t){e(t)}}),c.methods.comparePassword=async function(e){return u().compare(e,this.password)},c.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},c.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},c.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},c.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let d=o().models.User||o().model("User",c)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,242,70,799],()=>s(35643));module.exports=r})();