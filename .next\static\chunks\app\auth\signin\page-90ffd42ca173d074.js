(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[98],{1382:function(e,r,t){Promise.resolve().then(t.bind(t,3))},9376:function(e,r,t){"use strict";var s=t(5475);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},3:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return f}});var s=t(7437),n=t(2265),a=t(605),i=t(9376),l=t(7648),o=t(6334),c=t(2827),d=t(9442),u=t(8629),m=t(1215);function f(){let e=(0,i.useRouter)(),r=(0,i.useSearchParams)(),t=r.get("callbackUrl")||"/",f=r.get("error"),[h,x]=(0,n.useState)({email:"",password:""}),[g,p]=(0,n.useState)({}),[b,v]=(0,n.useState)(!1),j=e=>{let{name:r,value:t}=e.target;x(e=>({...e,[r]:t})),g[r]&&p(e=>({...e,[r]:""}))},y=()=>{let e={};return h.email?/\S+@\S+\.\S+/.test(h.email)||(e.email="Email kh\xf4ng hợp lệ"):e.email="Email l\xe0 bắt buộc",h.password||(e.password="Mật khẩu l\xe0 bắt buộc"),p(e),0===Object.keys(e).length},w=async r=>{if(r.preventDefault(),y()){v(!0);try{let r=await (0,a.signIn)("credentials",{email:h.email,password:h.password,redirect:!1});(null==r?void 0:r.error)?p({general:r.error}):(await (0,a.getSession)(),e.push(t),e.refresh())}catch(e){p({general:"Đ\xe3 xảy ra lỗi kh\xf4ng mong muốn"})}finally{v(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Đăng nhập v\xe0o t\xe0i khoản"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Hoặc"," ",(0,s.jsx)(l.default,{href:"/auth/signup",className:"font-medium text-primary hover:text-primary/80",children:"tạo t\xe0i khoản mới"})]})]}),(0,s.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg",children:[f&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(u.g7,{variant:"error",message:(e=>{switch(e){case"CredentialsSignin":return"Email hoặc mật khẩu kh\xf4ng đ\xfang";case"EmailNotVerified":return"Vui l\xf2ng x\xe1c thực email trước khi đăng nhập";case"AccountLocked":return"T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần";default:return"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập"}})(f)})}),g.general&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(u.g7,{variant:"error",message:g.general})}),(0,s.jsxs)(d.l0,{onSubmit:w,children:[(0,s.jsxs)(d.Wi,{children:[(0,s.jsx)(d.lX,{htmlFor:"email",required:!0,children:"Email"}),(0,s.jsx)(c.I,{id:"email",name:"email",type:"email",autoComplete:"email",value:h.email,onChange:j,error:!!g.email,placeholder:"Nhập email của bạn"}),(0,s.jsx)(d.Xq,{message:g.email})]}),(0,s.jsxs)(d.Wi,{children:[(0,s.jsx)(d.lX,{htmlFor:"password",required:!0,children:"Mật khẩu"}),(0,s.jsx)(c.I,{id:"password",name:"password",type:"password",autoComplete:"current-password",value:h.password,onChange:j,error:!!g.password,placeholder:"Nhập mật khẩu"}),(0,s.jsx)(d.Xq,{message:g.password})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(l.default,{href:"/auth/forgot-password",className:"font-medium text-primary hover:text-primary/80",children:"Qu\xean mật khẩu?"})})}),(0,s.jsx)(o.z,{type:"submit",className:"w-full",disabled:b,children:b?(0,s.jsx)(m.gb,{size:"sm"}):"Đăng nhập"})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Hoặc tiếp tục với"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)(o.z,{type:"button",variant:"outline",className:"w-full",onClick:()=>{(0,a.signIn)("google",{callbackUrl:t})},disabled:b,children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})})]})]})]})})}},8629:function(e,r,t){"use strict";t.d(r,{g7:function(){return m}});var s=t(7437),n=t(2265),a=t(535),i=t(3448);let l=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),o=n.forwardRef((e,r)=>{let{className:t,variant:n,...a}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(l({variant:n}),t),...a})});o.displayName="Alert";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...n})});c.displayName="AlertTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...n})});d.displayName="AlertDescription";let u={success:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function m(e){let{title:r,message:t,variant:n="info",onClose:a}=e;return(0,s.jsxs)(o,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[n],className:"relative",children:[u[n],(0,s.jsxs)("div",{className:"flex-1",children:[r&&(0,s.jsx)(c,{children:r}),(0,s.jsx)(d,{children:t})]}),a&&(0,s.jsx)("button",{onClick:a,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},6334:function(e,r,t){"use strict";t.d(r,{z:function(){return o}});var s=t(7437),n=t(2265),a=t(535),i=t(3448);let l=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,r)=>{let{className:t,variant:n,size:a,asChild:o=!1,...c}=e;return(0,s.jsx)("button",{className:(0,i.cn)(l({variant:n,size:a,className:t})),ref:r,...c})});o.displayName="Button"},9442:function(e,r,t){"use strict";t.d(r,{Ee:function(){return u},Wi:function(){return l},Xq:function(){return c},l0:function(){return i},lX:function(){return o},yv:function(){return d}});var s=t(7437),n=t(2265),a=t(3448);function i(e){let{className:r,children:t,...n}=e;return(0,s.jsx)("form",{className:(0,a.cn)("space-y-6",r),...n,children:t})}function l(e){let{children:r,className:t}=e;return(0,s.jsx)("div",{className:(0,a.cn)("space-y-2",t),children:r})}function o(e){let{className:r,children:t,required:n,...i}=e;return(0,s.jsxs)("label",{className:(0,a.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",r),...i,children:[t,n&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]})}function c(e){let{message:r,className:t}=e;return r?(0,s.jsx)("p",{className:(0,a.cn)("text-sm text-red-600",t),children:r}):null}function d(e){let{children:r,className:t}=e;return(0,s.jsx)("p",{className:(0,a.cn)("text-sm text-gray-500",t),children:r})}function u(e){let{name:r,options:t,value:n,onChange:i,error:l,className:o}=e;return(0,s.jsx)("div",{className:(0,a.cn)("space-y-2",o),children:t.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",id:"".concat(r,"-").concat(e.value),name:r,value:e.value,checked:n===e.value,onChange:e=>null==i?void 0:i(e.target.value),disabled:e.disabled,className:(0,a.cn)("h-4 w-4 text-primary focus:ring-primary focus:ring-2",l&&"border-red-500")}),(0,s.jsx)("label",{htmlFor:"".concat(r,"-").concat(e.value),className:(0,a.cn)("text-sm font-medium leading-none",e.disabled&&"opacity-50 cursor-not-allowed"),children:e.label})]},e.value))})}n.forwardRef((e,r)=>{let{className:t,error:n,...i}=e;return(0,s.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus-visible:ring-red-500",t),ref:r,...i})}).displayName="Textarea",n.forwardRef((e,r)=>{let{className:t,error:n,placeholder:i,children:l,...o}=e;return(0,s.jsxs)("select",{className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus-visible:ring-red-500",t),ref:r,...o,children:[i&&(0,s.jsx)("option",{value:"",disabled:!0,children:i}),l]})}).displayName="Select",n.forwardRef((e,r)=>{let{className:t,label:n,error:i,id:l,...o}=e,c=l||"checkbox-".concat(Math.random().toString(36).substr(2,9));return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",id:c,className:(0,a.cn)("h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary focus:ring-2",i&&"border-red-500",t),ref:r,...o}),n&&(0,s.jsx)("label",{htmlFor:c,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:n})]})}).displayName="Checkbox"},2827:function(e,r,t){"use strict";t.d(r,{I:function(){return i}});var s=t(7437),n=t(2265),a=t(3448);let i=n.forwardRef((e,r)=>{let{className:t,type:n,...i}=e;return(0,s.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},1215:function(e,r,t){"use strict";t.d(r,{gb:function(){return l}});var s=t(7437),n=t(535),a=t(3448);let i=(0,n.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function l(e){let{variant:r,size:t,className:n,text:l}=e;return(0,s.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",n),children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)("div",{className:(0,a.cn)(i({variant:r,size:t}))}),l&&(0,s.jsx)("p",{className:"text-sm text-gray-600",children:l})]})})}},3448:function(e,r,t){"use strict";t.d(r,{cn:function(){return a}});var s=t(1994),n=t(3335);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,s.W)(r))}}},function(e){e.O(0,[851,648,605,971,117,744],function(){return e(e.s=1382)}),_N_E=e.O()}]);