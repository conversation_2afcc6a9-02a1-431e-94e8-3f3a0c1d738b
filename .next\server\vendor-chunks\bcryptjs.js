/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/dist/bcrypt.js":
/*!**********************************************!*\
  !*** ./node_modules/bcryptjs/dist/bcrypt.js ***!
  \**********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\nvar __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*\r\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\r\n Copyright (c) 2012 Shane Girish <<EMAIL>>\r\n Copyright (c) 2014 Daniel Wirtz <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\n/**\r\n * @license bcrypt.js (c) 2013 Daniel Wirtz <<EMAIL>>\r\n * Released under the Apache License, Version 2.0\r\n * see: https://github.com/dcodeIO/bcrypt.js for details\r\n */\r\n(function(global, factory) {\r\n\r\n    /* AMD */ if (true)\r\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    /* CommonJS */ else {}\r\n\r\n}(this, function() {\r\n    \"use strict\";\r\n\r\n    /**\r\n     * bcrypt namespace.\r\n     * @type {Object.<string,*>}\r\n     */\r\n    var bcrypt = {};\r\n\r\n    /**\r\n     * The random implementation to use as a fallback.\r\n     * @type {?function(number):!Array.<number>}\r\n     * @inner\r\n     */\r\n    var randomFallback = null;\r\n\r\n    /**\r\n     * Generates cryptographically secure random bytes.\r\n     * @function\r\n     * @param {number} len Bytes length\r\n     * @returns {!Array.<number>} Random bytes\r\n     * @throws {Error} If no random implementation is available\r\n     * @inner\r\n     */\r\n    function random(len) {\r\n        /* node */ if ( true && module && module['exports'])\r\n            try {\r\n                return (__webpack_require__(/*! crypto */ \"crypto\").randomBytes)(len);\r\n            } catch (e) {}\r\n        /* WCA */ try {\r\n            var a; (self['crypto']||self['msCrypto'])['getRandomValues'](a = new Uint32Array(len));\r\n            return Array.prototype.slice.call(a);\r\n        } catch (e) {}\r\n        /* fallback */ if (!randomFallback)\r\n            throw Error(\"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\");\r\n        return randomFallback(len);\r\n    }\r\n\r\n    // Test if any secure randomness source is available\r\n    var randomAvailable = false;\r\n    try {\r\n        random(1);\r\n        randomAvailable = true;\r\n    } catch (e) {}\r\n\r\n    // Default fallback, if any\r\n    randomFallback = null;\r\n    /**\r\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\r\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\r\n     *  is seeded properly!\r\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\r\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\r\n     * @see http://nodejs.org/api/crypto.html\r\n     * @see http://www.w3.org/TR/WebCryptoAPI/\r\n     */\r\n    bcrypt.setRandomFallback = function(random) {\r\n        randomFallback = random;\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a salt.\r\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {number=} seed_length Not supported.\r\n     * @returns {string} Resulting salt\r\n     * @throws {Error} If a random fallback is required but not set\r\n     * @expose\r\n     */\r\n    bcrypt.genSaltSync = function(rounds, seed_length) {\r\n        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof rounds !== 'number')\r\n            throw Error(\"Illegal arguments: \"+(typeof rounds)+\", \"+(typeof seed_length));\r\n        if (rounds < 4)\r\n            rounds = 4;\r\n        else if (rounds > 31)\r\n            rounds = 31;\r\n        var salt = [];\r\n        salt.push(\"$2a$\");\r\n        if (rounds < 10)\r\n            salt.push(\"0\");\r\n        salt.push(rounds.toString());\r\n        salt.push('$');\r\n        salt.push(base64_encode(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\r\n        return salt.join('');\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a salt.\r\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.genSalt = function(rounds, seed_length, callback) {\r\n        if (typeof seed_length === 'function')\r\n            callback = seed_length,\r\n            seed_length = undefined; // Not supported.\r\n        if (typeof rounds === 'function')\r\n            callback = rounds,\r\n            rounds = undefined;\r\n        if (typeof rounds === 'undefined')\r\n            rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        else if (typeof rounds !== 'number')\r\n            throw Error(\"illegal arguments: \"+(typeof rounds));\r\n\r\n        function _async(callback) {\r\n            nextTick(function() { // Pretty thin, but salting is fast enough\r\n                try {\r\n                    callback(null, bcrypt.genSaltSync(rounds));\r\n                } catch (err) {\r\n                    callback(err);\r\n                }\r\n            });\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\r\n     * @returns {string} Resulting hash\r\n     * @expose\r\n     */\r\n    bcrypt.hashSync = function(s, salt) {\r\n        if (typeof salt === 'undefined')\r\n            salt = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof salt === 'number')\r\n            salt = bcrypt.genSaltSync(salt);\r\n        if (typeof s !== 'string' || typeof salt !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt));\r\n        return _hash(s, salt);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {number|string} salt Salt length to generate or salt to use\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.hash = function(s, salt, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s === 'string' && typeof salt === 'number')\r\n                bcrypt.genSalt(salt, function(err, salt) {\r\n                    _hash(s, salt, callback, progressCallback);\r\n                });\r\n            else if (typeof s === 'string' && typeof salt === 'string')\r\n                _hash(s, salt, callback, progressCallback);\r\n            else\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt))));\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Compares two strings of the same length in constant time.\r\n     * @param {string} known Must be of the correct length\r\n     * @param {string} unknown Must be the same length as `known`\r\n     * @returns {boolean}\r\n     * @inner\r\n     */\r\n    function safeStringCompare(known, unknown) {\r\n        var right = 0,\r\n            wrong = 0;\r\n        for (var i=0, k=known.length; i<k; ++i) {\r\n            if (known.charCodeAt(i) === unknown.charCodeAt(i))\r\n                ++right;\r\n            else\r\n                ++wrong;\r\n        }\r\n        // Prevent removal of unused variables (never true, actually)\r\n        if (right < 0)\r\n            return false;\r\n        return wrong === 0;\r\n    }\r\n\r\n    /**\r\n     * Synchronously tests a string against a hash.\r\n     * @param {string} s String to compare\r\n     * @param {string} hash Hash to test against\r\n     * @returns {boolean} true if matching, otherwise false\r\n     * @throws {Error} If an argument is illegal\r\n     * @expose\r\n     */\r\n    bcrypt.compareSync = function(s, hash) {\r\n        if (typeof s !== \"string\" || typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash));\r\n        if (hash.length !== 60)\r\n            return false;\r\n        return safeStringCompare(bcrypt.hashSync(s, hash.substr(0, hash.length-31)), hash);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously compares the given data against the given hash.\r\n     * @param {string} s Data to compare\r\n     * @param {string} hash Data to be compared to\r\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.compare = function(s, hash, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s !== \"string\" || typeof hash !== \"string\") {\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash))));\r\n                return;\r\n            }\r\n            if (hash.length !== 60) {\r\n                nextTick(callback.bind(this, null, false));\r\n                return;\r\n            }\r\n            bcrypt.hash(s, hash.substr(0, 29), function(err, comp) {\r\n                if (err)\r\n                    callback(err);\r\n                else\r\n                    callback(null, safeStringCompare(comp, hash));\r\n            }, progressCallback);\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Gets the number of rounds used to encrypt the specified hash.\r\n     * @param {string} hash Hash to extract the used number of rounds from\r\n     * @returns {number} Number of rounds used\r\n     * @throws {Error} If `hash` is not a string\r\n     * @expose\r\n     */\r\n    bcrypt.getRounds = function(hash) {\r\n        if (typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        return parseInt(hash.split(\"$\")[2], 10);\r\n    };\r\n\r\n    /**\r\n     * Gets the salt portion from a hash. Does not validate the hash.\r\n     * @param {string} hash Hash to extract the salt from\r\n     * @returns {string} Extracted salt part\r\n     * @throws {Error} If `hash` is not a string or otherwise invalid\r\n     * @expose\r\n     */\r\n    bcrypt.getSalt = function(hash) {\r\n        if (typeof hash !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        if (hash.length !== 60)\r\n            throw Error(\"Illegal hash length: \"+hash.length+\" != 60\");\r\n        return hash.substring(0, 29);\r\n    };\r\n\r\n    /**\r\n     * Continues with the callback on the next tick.\r\n     * @function\r\n     * @param {function(...[*])} callback Callback to execute\r\n     * @inner\r\n     */\r\n    var nextTick = typeof process !== 'undefined' && process && typeof process.nextTick === 'function'\r\n        ? (typeof setImmediate === 'function' ? setImmediate : process.nextTick)\r\n        : setTimeout;\r\n\r\n    /**\r\n     * Converts a JavaScript string to UTF8 bytes.\r\n     * @param {string} str String\r\n     * @returns {!Array.<number>} UTF8 bytes\r\n     * @inner\r\n     */\r\n    function stringToBytes(str) {\r\n        var out = [],\r\n            i = 0;\r\n        utfx.encodeUTF16toUTF8(function() {\r\n            if (i >= str.length) return null;\r\n            return str.charCodeAt(i++);\r\n        }, function(b) {\r\n            out.push(b);\r\n        });\r\n        return out;\r\n    }\r\n\r\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\r\n\r\n    /**\r\n     * bcrypt's own non-standard base64 dictionary.\r\n     * @type {!Array.<string>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_CODE = \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split('');\r\n\r\n    /**\r\n     * @type {!Array.<number>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_INDEX = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0,\r\n        1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, -1, -1, -1, -1, -1, -1,\r\n        -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\r\n        20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28, 29, 30,\r\n        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\r\n        48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1];\r\n\r\n    /**\r\n     * @type {!function(...number):string}\r\n     * @inner\r\n     */\r\n    var stringFromCharCode = String.fromCharCode;\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input.\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @inner\r\n     */\r\n    function base64_encode(b, len) {\r\n        var off = 0,\r\n            rs = [],\r\n            c1, c2;\r\n        if (len <= 0 || len > b.length)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < len) {\r\n            c1 = b[off++] & 0xff;\r\n            rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\r\n            c1 = (c1 & 0x03) << 4;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 4) & 0x0f;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            c1 = (c2 & 0x0f) << 2;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 6) & 0x03;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            rs.push(BASE64_CODE[c2 & 0x3f]);\r\n        }\r\n        return rs.join('');\r\n    }\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output.\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @inner\r\n     */\r\n    function base64_decode(s, len) {\r\n        var off = 0,\r\n            slen = s.length,\r\n            olen = 0,\r\n            rs = [],\r\n            c1, c2, c3, c4, o, code;\r\n        if (len <= 0)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < slen - 1 && olen < len) {\r\n            code = s.charCodeAt(off++);\r\n            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            code = s.charCodeAt(off++);\r\n            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c1 == -1 || c2 == -1)\r\n                break;\r\n            o = (c1 << 2) >>> 0;\r\n            o |= (c2 & 0x30) >> 4;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c3 == -1)\r\n                break;\r\n            o = ((c2 & 0x0f) << 4) >>> 0;\r\n            o |= (c3 & 0x3c) >> 2;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            o = ((c3 & 0x03) << 6) >>> 0;\r\n            o |= c4;\r\n            rs.push(stringFromCharCode(o));\r\n            ++olen;\r\n        }\r\n        var res = [];\r\n        for (off = 0; off<olen; off++)\r\n            res.push(rs[off].charCodeAt(0));\r\n        return res;\r\n    }\r\n\r\n    /**\r\n     * utfx-embeddable (c) 2014 Daniel Wirtz <<EMAIL>>\r\n     * Released under the Apache License, Version 2.0\r\n     * see: https://github.com/dcodeIO/utfx for details\r\n     */\r\n    var utfx = function() {\r\n        \"use strict\";\r\n\r\n        /**\r\n         * utfx namespace.\r\n         * @inner\r\n         * @type {!Object.<string,*>}\r\n         */\r\n        var utfx = {};\r\n\r\n        /**\r\n         * Maximum valid code point.\r\n         * @type {number}\r\n         * @const\r\n         */\r\n        utfx.MAX_CODEPOINT = 0x10FFFF;\r\n\r\n        /**\r\n         * Encodes UTF8 code points to UTF8 bytes.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte\r\n         */\r\n        utfx.encodeUTF8 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src,\r\n                src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp < 0x80)\r\n                    dst(cp&0x7F);\r\n                else if (cp < 0x800)\r\n                    dst(((cp>>6)&0x1F)|0xC0),\r\n                    dst((cp&0x3F)|0x80);\r\n                else if (cp < 0x10000)\r\n                    dst(((cp>>12)&0x0F)|0xE0),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                else\r\n                    dst(((cp>>18)&0x07)|0xF0),\r\n                    dst(((cp>>12)&0x3F)|0x80),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Decodes UTF8 bytes to UTF8 code points.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each decoded code point.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\r\n         *  remaining bytes.\r\n         */\r\n        utfx.decodeUTF8 = function(src, dst) {\r\n            var a, b, c, d, fail = function(b) {\r\n                b = b.slice(0, b.indexOf(null));\r\n                var err = Error(b.toString());\r\n                err.name = \"TruncatedError\";\r\n                err['bytes'] = b;\r\n                throw err;\r\n            };\r\n            while ((a = src()) !== null) {\r\n                if ((a&0x80) === 0)\r\n                    dst(a);\r\n                else if ((a&0xE0) === 0xC0)\r\n                    ((b = src()) === null) && fail([a, b]),\r\n                    dst(((a&0x1F)<<6) | (b&0x3F));\r\n                else if ((a&0xF0) === 0xE0)\r\n                    ((b=src()) === null || (c=src()) === null) && fail([a, b, c]),\r\n                    dst(((a&0x0F)<<12) | ((b&0x3F)<<6) | (c&0x3F));\r\n                else if ((a&0xF8) === 0xF0)\r\n                    ((b=src()) === null || (c=src()) === null || (d=src()) === null) && fail([a, b, c ,d]),\r\n                    dst(((a&0x07)<<18) | ((b&0x3F)<<12) | ((c&0x3F)<<6) | (d&0x3F));\r\n                else throw RangeError(\"Illegal starting byte: \"+a);\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts UTF16 characters to UTF8 code points.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each converted code\r\n         *  point.\r\n         */\r\n        utfx.UTF16toUTF8 = function(src, dst) {\r\n            var c1, c2 = null;\r\n            while (true) {\r\n                if ((c1 = c2 !== null ? c2 : src()) === null)\r\n                    break;\r\n                if (c1 >= 0xD800 && c1 <= 0xDFFF) {\r\n                    if ((c2 = src()) !== null) {\r\n                        if (c2 >= 0xDC00 && c2 <= 0xDFFF) {\r\n                            dst((c1-0xD800)*0x400+c2-0xDC00+0x10000);\r\n                            c2 = null; continue;\r\n                        }\r\n                    }\r\n                }\r\n                dst(c1);\r\n            }\r\n            if (c2 !== null) dst(c2);\r\n        };\r\n\r\n        /**\r\n         * Converts UTF8 code points to UTF16 characters.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a code point is out of range\r\n         */\r\n        utfx.UTF8toUTF16 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src, src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp <= 0xFFFF)\r\n                    dst(cp);\r\n                else\r\n                    cp -= 0x10000,\r\n                    dst((cp>>10)+0xD800),\r\n                    dst((cp%0x400)+0xDC00);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts and encodes UTF16 characters to UTF8 bytes.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively `null`\r\n         *  if there are no more characters left.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte.\r\n         */\r\n        utfx.encodeUTF16toUTF8 = function(src, dst) {\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                utfx.encodeUTF8(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Decodes and converts UTF8 bytes to UTF16 characters.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\r\n         */\r\n        utfx.decodeUTF8toUTF16 = function(src, dst) {\r\n            utfx.decodeUTF8(src, function(cp) {\r\n                utfx.UTF8toUTF16(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Calculates the byte length of an UTF8 code point.\r\n         * @param {number} cp UTF8 code point\r\n         * @returns {number} Byte length\r\n         */\r\n        utfx.calculateCodePoint = function(cp) {\r\n            return (cp < 0x80) ? 1 : (cp < 0x800) ? 2 : (cp < 0x10000) ? 3 : 4;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 bytes required to store UTF8 code points.\r\n         * @param {(!function():number|null)} src Code points source as a function returning the next code point respectively\r\n         *  `null` if there are no more code points left.\r\n         * @returns {number} The number of UTF8 bytes required\r\n         */\r\n        utfx.calculateUTF8 = function(src) {\r\n            var cp, l=0;\r\n            while ((cp = src()) !== null)\r\n                l += utfx.calculateCodePoint(cp);\r\n            return l;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\r\n         * @param {(!function():number|null)} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @returns {!Array.<number>} The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\r\n         */\r\n        utfx.calculateUTF16asUTF8 = function(src) {\r\n            var n=0, l=0;\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                ++n; l += utfx.calculateCodePoint(cp);\r\n            });\r\n            return [n,l];\r\n        };\r\n\r\n        return utfx;\r\n    }();\r\n\r\n    Date.now = Date.now || function() { return +new Date; };\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BCRYPT_SALT_LEN = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BLOWFISH_NUM_ROUNDS = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var MAX_EXECUTION_TIME = 100;\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var P_ORIG = [\r\n        0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822,\r\n        0x299f31d0, 0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377,\r\n        0xbe5466cf, 0x34e90c6c, 0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5,\r\n        0xb5470917, 0x9216d5d9, 0x8979fb1b\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var S_ORIG = [\r\n        0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed,\r\n        0x6a267e96, 0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7,\r\n        0x0801f2e2, 0x858efc16, 0x636920d8, 0x71574e69, 0xa458fea3,\r\n        0xf4933d7e, 0x0d95748f, 0x728eb658, 0x718bcd58, 0x82154aee,\r\n        0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013, 0xc5d1b023,\r\n        0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\r\n        0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda,\r\n        0x55605c60, 0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440,\r\n        0x55ca396a, 0x2aab10b6, 0xb4cc5c34, 0x1141e8ce, 0xa15486af,\r\n        0x7c72e993, 0xb3ee1411, 0x636fbc2a, 0x2ba9c55d, 0x741831f6,\r\n        0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c, 0x7a325381,\r\n        0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\r\n        0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d,\r\n        0xe98575b1, 0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5,\r\n        0x0f6d6ff3, 0x83f44239, 0x2e0b4482, 0xa4842004, 0x69c8f04a,\r\n        0x9e1f9b5e, 0x21c66842, 0xf6e96c9a, 0x670c9c61, 0xabd388f0,\r\n        0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3, 0x6eef0b6c,\r\n        0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\r\n        0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3,\r\n        0x3b8b5ebe, 0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6,\r\n        0x4ed3aa62, 0x363f7706, 0x1bfedf72, 0x429b023d, 0x37d0d724,\r\n        0xd00a1248, 0xdb0fead3, 0x49f1c09b, 0x075372c9, 0x80991b7b,\r\n        0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b, 0x976ce0bd,\r\n        0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\r\n        0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f,\r\n        0x9b30952c, 0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd,\r\n        0x660f2807, 0x192e4bb3, 0xc0cba857, 0x45c8740f, 0xd20b5f39,\r\n        0xb9d3fbdb, 0x5579c0bd, 0x1a60320a, 0xd6a100c6, 0x402c7279,\r\n        0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8, 0x3c7516df,\r\n        0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\r\n        0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e,\r\n        0xdf1769db, 0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573,\r\n        0x695b27b0, 0xbbca58c8, 0xe1ffa35d, 0xb8f011a0, 0x10fa3d98,\r\n        0xfd2183b8, 0x4afcb56c, 0x2dd1d35b, 0x9a53e479, 0xb6f84565,\r\n        0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33, 0x62fb1341,\r\n        0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\r\n        0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0,\r\n        0xafc725e0, 0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64,\r\n        0x8888b812, 0x900df01c, 0x4fad5ea0, 0x688fc31c, 0xd1cff191,\r\n        0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777, 0xea752dfe, 0x8b021fa1,\r\n        0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299, 0xb4a84fe0,\r\n        0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\r\n        0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5,\r\n        0xfb9d35cf, 0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49,\r\n        0x00250e2d, 0x2071b35e, 0x226800bb, 0x57b8e0af, 0x2464369b,\r\n        0xf009b91e, 0x5563911d, 0x59dfa6aa, 0x78c14389, 0xd95a537f,\r\n        0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9, 0x11c81968,\r\n        0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\r\n        0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5,\r\n        0x571be91f, 0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6,\r\n        0xff34052e, 0xc5855664, 0x53b02d5d, 0xa99f8fa1, 0x08ba4799,\r\n        0x6e85076a, 0x4b7a70e9, 0xb5b32944, 0xdb75092e, 0xc4192623,\r\n        0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266, 0xecaa8c71,\r\n        0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\r\n        0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6,\r\n        0x99f73fd6, 0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1,\r\n        0x4cdd2086, 0x8470eb26, 0x6382e9c6, 0x021ecc5e, 0x09686b3f,\r\n        0x3ebaefc9, 0x3c971814, 0x6b6a70a1, 0x687f3584, 0x52a0e286,\r\n        0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c, 0x8e7d44ec,\r\n        0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\r\n        0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9,\r\n        0x7ca92ff6, 0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc,\r\n        0xc8b57634, 0x9af3dda7, 0xa9446146, 0x0fd0030e, 0xecc8c73e,\r\n        0xa4751e41, 0xe238cd99, 0x3bea0e2f, 0x3280bba1, 0x183eb331,\r\n        0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf, 0x2cb81290,\r\n        0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\r\n        0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6,\r\n        0x9f84cd87, 0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c,\r\n        0xec7aec3a, 0xdb851dfa, 0x63094366, 0xc464c3d2, 0xef1c1847,\r\n        0x3215d908, 0xdd433b37, 0x24c2ba16, 0x12a14d43, 0x2a65c451,\r\n        0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55, 0x81ac77d6,\r\n        0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\r\n        0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570,\r\n        0xeae96fb1, 0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa,\r\n        0x2965dcb9, 0x99e71d0f, 0x803e89d6, 0x5266c825, 0x2e4cc978,\r\n        0x9c10b36a, 0xc6150eba, 0x94e2ea78, 0xa5fc3c53, 0x1e0a2df4,\r\n        0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960, 0x5223a708,\r\n        0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\r\n        0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185,\r\n        0x68ab9802, 0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84,\r\n        0x1521b628, 0x29076170, 0xecdd4775, 0x619f1510, 0x13cca830,\r\n        0xeb61bd96, 0x0334fe1e, 0xaa0363cf, 0xb5735c90, 0x4c70a239,\r\n        0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7, 0x9cab5cab,\r\n        0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\r\n        0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19,\r\n        0x875fa099, 0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77,\r\n        0x11ed935f, 0x16681281, 0x0e358829, 0xc7e61fd6, 0x96dedfa1,\r\n        0x7858ba99, 0x57f584a5, 0x1b227263, 0x9b83c3ff, 0x1ac24696,\r\n        0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128, 0x58ebf2ef,\r\n        0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\r\n        0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15,\r\n        0xfacb4fd0, 0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105,\r\n        0xd81e799e, 0x86854dc7, 0xe44b476a, 0x3d816250, 0xcf62a1f2,\r\n        0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3, 0x7f1524c3, 0x69cb7492,\r\n        0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d, 0x1462b174,\r\n        0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\r\n        0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759,\r\n        0xcbee7460, 0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e,\r\n        0xe8efd855, 0x61d99735, 0xa969a7aa, 0xc50c06c2, 0x5a04abfc,\r\n        0x800bcadc, 0x9e447a2e, 0xc3453484, 0xfdd56705, 0x0e1e9ec9,\r\n        0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340, 0xc5c43465,\r\n        0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\r\n        0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c,\r\n        0x94692934, 0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068,\r\n        0xd4082471, 0x3320f46a, 0x43b7d4b7, 0x500061af, 0x1e39f62e,\r\n        0x97244546, 0x14214f74, 0xbf8b8840, 0x4d95fc1d, 0x96b591af,\r\n        0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785, 0x7fac6dd0,\r\n        0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\r\n        0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462,\r\n        0xd7486900, 0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c,\r\n        0xb58ce006, 0x7af4d6b6, 0xaace1e7c, 0xd3375fec, 0xce78a399,\r\n        0x406b2a42, 0x20fe9e35, 0xd9f385b9, 0xee39d7ab, 0x3b124e8b,\r\n        0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2, 0x3a6efa74,\r\n        0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\r\n        0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7,\r\n        0xd096954b, 0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33,\r\n        0xa62a4a56, 0x3f3125f9, 0x5ef47e1c, 0x9029317c, 0xfdf8e802,\r\n        0x04272f70, 0x80bb155c, 0x05282ce3, 0x95c11548, 0xe4c66d22,\r\n        0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f, 0x404779a4,\r\n        0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\r\n        0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2,\r\n        0x02e1329e, 0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1,\r\n        0x3b240b62, 0xeebeb922, 0x85b2a20e, 0xe6ba0d99, 0xde720c8c,\r\n        0x2da2f728, 0xd0127845, 0x95b794fd, 0x647d0862, 0xe7ccf5f0,\r\n        0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e, 0x0a476341,\r\n        0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\r\n        0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b,\r\n        0xdcd0e804, 0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b,\r\n        0x667b9ffb, 0xcedb7d9c, 0xa091cf0b, 0xd9155ea3, 0xbb132f88,\r\n        0x515bad24, 0x7b9479bf, 0x763bd6eb, 0x37392eb3, 0xcc115979,\r\n        0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b, 0x12754ccc,\r\n        0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\r\n        0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659,\r\n        0x0a121386, 0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f,\r\n        0xbebfe988, 0x64e4c3fe, 0x9dbc8057, 0xf0f7c086, 0x60787bf8,\r\n        0x6003604d, 0xd1fd8346, 0xf6381fb0, 0x7745ae04, 0xd736fccc,\r\n        0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f, 0x77a057be,\r\n        0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\r\n        0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255,\r\n        0x46fcd9b9, 0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2,\r\n        0x466e598e, 0x20b45770, 0x8cd55591, 0xc902de4c, 0xb90bace1,\r\n        0xbb8205d0, 0x11a86248, 0x7574a99e, 0xb77f19b6, 0xe0a9dc09,\r\n        0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c, 0x4a99a025,\r\n        0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\r\n        0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01,\r\n        0xa70683fa, 0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641,\r\n        0xc3604c06, 0x61a806b5, 0xf0177a28, 0xc0f586e0, 0x006058aa,\r\n        0x30dc7d62, 0x11e69ed7, 0x2338ea63, 0x53c2dd94, 0xc2c21634,\r\n        0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76, 0x6f05e409,\r\n        0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\r\n        0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3,\r\n        0x4dad0fc4, 0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c,\r\n        0x6fd5c7e7, 0x56e14ec4, 0x362abfce, 0xddc6c837, 0xd79a3234,\r\n        0x92638212, 0x670efa8e, 0x406000e0, 0x3a39ce37, 0xd3faf5cf,\r\n        0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742, 0xd3822740,\r\n        0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\r\n        0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f,\r\n        0xbc946e79, 0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d,\r\n        0xd5730a1d, 0x4cd04dc6, 0x2939bbdb, 0xa9ba4650, 0xac9526e8,\r\n        0xbe5ee304, 0xa1fad5f0, 0x6a2d519a, 0x63ef8ce2, 0x9a86ee22,\r\n        0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4, 0x83c061ba,\r\n        0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\r\n        0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69,\r\n        0x77fa0a59, 0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593,\r\n        0xe990fd5a, 0x9e34d797, 0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a,\r\n        0x017da67d, 0xd1cf3ed6, 0x7c7d2d28, 0x1f9f25cf, 0xadf2b89b,\r\n        0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6, 0x47b0acfd,\r\n        0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\r\n        0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4,\r\n        0x88f46dba, 0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2,\r\n        0x97271aec, 0xa93a072a, 0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb,\r\n        0x26dcf319, 0x7533d928, 0xb155fdf5, 0x03563482, 0x8aba3cbb,\r\n        0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f, 0x4de81751,\r\n        0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\r\n        0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369,\r\n        0x6413e680, 0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166,\r\n        0xb39a460a, 0x6445c0dd, 0x586cdecf, 0x1c20c8ae, 0x5bbef7dd,\r\n        0x1b588d40, 0xccd2017f, 0x6bb4e3bb, 0xdda26a7e, 0x3a59ff45,\r\n        0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb, 0x8d6612ae,\r\n        0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\r\n        0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08,\r\n        0x4eb4e2cc, 0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d,\r\n        0x06b89fb4, 0xce6ea048, 0x6f3f3b82, 0x3520ab82, 0x011a1d4b,\r\n        0x277227f8, 0x611560b1, 0xe7933fdc, 0xbb3a792b, 0x344525bd,\r\n        0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9, 0xe01cc87e,\r\n        0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\r\n        0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c,\r\n        0xe0b12b4f, 0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c,\r\n        0xbf97222c, 0x15e6fc2a, 0x0f91fc71, 0x9b941525, 0xfae59361,\r\n        0xceb69ceb, 0xc2a86459, 0x12baa8d1, 0xb6c1075e, 0xe3056a0c,\r\n        0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b, 0x4c98a0be,\r\n        0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\r\n        0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d,\r\n        0x9b992f2e, 0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891,\r\n        0xce6279cf, 0xcd3e7e6f, 0x1618b166, 0xfd2c1d05, 0x848fd2c5,\r\n        0xf6fb2299, 0xf523f357, 0xa6327623, 0x93a83531, 0x56cccd02,\r\n        0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc, 0xde966292,\r\n        0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\r\n        0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2,\r\n        0x35bdd2f6, 0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b,\r\n        0x53113ec0, 0x1640e3d3, 0x38abbd60, 0x2547adf0, 0xba38209c,\r\n        0xf746ce76, 0x77afa1c5, 0x20756060, 0x85cbfe4e, 0x8ae88dd8,\r\n        0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c, 0x01c36ae4,\r\n        0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\r\n        0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var C_ORIG = [\r\n        0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944,\r\n        0x6f756274\r\n    ];\r\n\r\n    /**\r\n     * @param {Array.<number>} lr\r\n     * @param {number} off\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @returns {Array.<number>}\r\n     * @inner\r\n     */\r\n    function _encipher(lr, off, P, S) { // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\r\n        var n,\r\n            l = lr[off],\r\n            r = lr[off + 1];\r\n\r\n        l ^= P[0];\r\n\r\n        /*\r\n        for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\r\n            // Feistel substitution on left word\r\n            n  = S[l >>> 24],\r\n            n += S[0x100 | ((l >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((l >> 8) & 0xff)],\r\n            n += S[0x300 | (l & 0xff)],\r\n            r ^= n ^ P[++i],\r\n            // Feistel substitution on right word\r\n            n  = S[r >>> 24],\r\n            n += S[0x100 | ((r >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((r >> 8) & 0xff)],\r\n            n += S[0x300 | (r & 0xff)],\r\n            l ^= n ^ P[++i];\r\n        */\r\n\r\n        //The following is an unrolled version of the above loop.\r\n        //Iteration 0\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[1];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[2];\r\n        //Iteration 1\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[3];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[4];\r\n        //Iteration 2\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[5];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[6];\r\n        //Iteration 3\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[7];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[8];\r\n        //Iteration 4\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[9];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[10];\r\n        //Iteration 5\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[11];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[12];\r\n        //Iteration 6\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[13];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[14];\r\n        //Iteration 7\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[15];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[16];\r\n\r\n        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\r\n        lr[off + 1] = l;\r\n        return lr;\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} data\r\n     * @param {number} offp\r\n     * @returns {{key: number, offp: number}}\r\n     * @inner\r\n     */\r\n    function _streamtoword(data, offp) {\r\n        for (var i = 0, word = 0; i < 4; ++i)\r\n            word = (word << 8) | (data[offp] & 0xff),\r\n            offp = (offp + 1) % data.length;\r\n        return { key: word, offp: offp };\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _key(key, P, S) {\r\n        var offset = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offset),\r\n            offset = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        for (i = 0; i < plen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Expensive key schedule Blowfish.\r\n     * @param {Array.<number>} data\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _ekskey(data, key, P, S) {\r\n        var offp = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offp),\r\n            offp = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        offp = 0;\r\n        for (i = 0; i < plen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Internaly crypts a string.\r\n     * @param {Array.<number>} b Bytes to crypt\r\n     * @param {Array.<number>} salt Salt bytes to use\r\n     * @param {number} rounds Number of rounds\r\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\r\n     *  omitted, the operation will be performed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _crypt(b, salt, rounds, callback, progressCallback) {\r\n        var cdata = C_ORIG.slice(),\r\n            clen = cdata.length,\r\n            err;\r\n\r\n        // Validate\r\n        if (rounds < 4 || rounds > 31) {\r\n            err = Error(\"Illegal number of rounds (4-31): \"+rounds);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        if (salt.length !== BCRYPT_SALT_LEN) {\r\n            err =Error(\"Illegal salt length: \"+salt.length+\" != \"+BCRYPT_SALT_LEN);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        rounds = (1 << rounds) >>> 0;\r\n\r\n        var P, S, i = 0, j;\r\n\r\n        //Use typed arrays when available - huge speedup!\r\n        if (Int32Array) {\r\n            P = new Int32Array(P_ORIG);\r\n            S = new Int32Array(S_ORIG);\r\n        } else {\r\n            P = P_ORIG.slice();\r\n            S = S_ORIG.slice();\r\n        }\r\n\r\n        _ekskey(salt, b, P, S);\r\n\r\n        /**\r\n         * Calcualtes the next round.\r\n         * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\r\n         * @inner\r\n         */\r\n        function next() {\r\n            if (progressCallback)\r\n                progressCallback(i / rounds);\r\n            if (i < rounds) {\r\n                var start = Date.now();\r\n                for (; i < rounds;) {\r\n                    i = i + 1;\r\n                    _key(b, P, S);\r\n                    _key(salt, P, S);\r\n                    if (Date.now() - start > MAX_EXECUTION_TIME)\r\n                        break;\r\n                }\r\n            } else {\r\n                for (i = 0; i < 64; i++)\r\n                    for (j = 0; j < (clen >> 1); j++)\r\n                        _encipher(cdata, j << 1, P, S);\r\n                var ret = [];\r\n                for (i = 0; i < clen; i++)\r\n                    ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\r\n                    ret.push((cdata[i] & 0xff) >>> 0);\r\n                if (callback) {\r\n                    callback(null, ret);\r\n                    return;\r\n                } else\r\n                    return ret;\r\n            }\r\n            if (callback)\r\n                nextTick(next);\r\n        }\r\n\r\n        // Async\r\n        if (typeof callback !== 'undefined') {\r\n            next();\r\n\r\n            // Sync\r\n        } else {\r\n            var res;\r\n            while (true)\r\n                if (typeof(res = next()) !== 'undefined')\r\n                    return res || [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Internally hashes a string.\r\n     * @param {string} s String to hash\r\n     * @param {?string} salt Salt to use, actually never null\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\r\n     *  hashing is perormed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _hash(s, salt, callback, progressCallback) {\r\n        var err;\r\n        if (typeof s !== 'string' || typeof salt !== 'string') {\r\n            err = Error(\"Invalid string / salt: Not a string\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n\r\n        // Validate the salt\r\n        var minor, offset;\r\n        if (salt.charAt(0) !== '$' || salt.charAt(1) !== '2') {\r\n            err = Error(\"Invalid salt version: \"+salt.substring(0,2));\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n        if (salt.charAt(2) === '$')\r\n            minor = String.fromCharCode(0),\r\n            offset = 3;\r\n        else {\r\n            minor = salt.charAt(2);\r\n            if ((minor !== 'a' && minor !== 'b' && minor !== 'y') || salt.charAt(3) !== '$') {\r\n                err = Error(\"Invalid salt revision: \"+salt.substring(2,4));\r\n                if (callback) {\r\n                    nextTick(callback.bind(this, err));\r\n                    return;\r\n                } else\r\n                    throw err;\r\n            }\r\n            offset = 4;\r\n        }\r\n\r\n        // Extract number of rounds\r\n        if (salt.charAt(offset + 2) > '$') {\r\n            err = Error(\"Missing salt rounds\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\r\n            r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\r\n            rounds = r1 + r2,\r\n            real_salt = salt.substring(offset + 3, offset + 25);\r\n        s += minor >= 'a' ? \"\\x00\" : \"\";\r\n\r\n        var passwordb = stringToBytes(s),\r\n            saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\r\n\r\n        /**\r\n         * Finishes hashing.\r\n         * @param {Array.<number>} bytes Byte array\r\n         * @returns {string}\r\n         * @inner\r\n         */\r\n        function finish(bytes) {\r\n            var res = [];\r\n            res.push(\"$2\");\r\n            if (minor >= 'a')\r\n                res.push(minor);\r\n            res.push(\"$\");\r\n            if (rounds < 10)\r\n                res.push(\"0\");\r\n            res.push(rounds.toString());\r\n            res.push(\"$\");\r\n            res.push(base64_encode(saltb, saltb.length));\r\n            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\r\n            return res.join('');\r\n        }\r\n\r\n        // Sync\r\n        if (typeof callback == 'undefined')\r\n            return finish(_crypt(passwordb, saltb, rounds));\r\n\r\n        // Async\r\n        else {\r\n            _crypt(passwordb, saltb, rounds, function(err, bytes) {\r\n                if (err)\r\n                    callback(err, null);\r\n                else\r\n                    callback(null, finish(bytes));\r\n            }, progressCallback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @expose\r\n     */\r\n    bcrypt.encodeBase64 = base64_encode;\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @expose\r\n     */\r\n    bcrypt.decodeBase64 = base64_decode;\r\n\r\n    return bcrypt;\r\n}));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/dist/bcrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/bcryptjs/index.js":
/*!****************************************!*\
  !*** ./node_modules/bcryptjs/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*\r\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\r\n Copyright (c) 2012 Shane Girish <<EMAIL>>\r\n Copyright (c) 2013 Daniel Wirtz <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\nmodule.exports = __webpack_require__(/*! ./dist/bcrypt.js */ \"(rsc)/./node_modules/bcryptjs/dist/bcrypt.js\");\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/index.js\n");

/***/ })

};
;