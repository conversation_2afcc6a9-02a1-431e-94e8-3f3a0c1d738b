import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb'
import Course, { CourseStatus } from '@/models/Course'
import Enrollment, { EnrollmentStatus } from '@/models/Enrollment'
import User, { UserRole } from '@/models/User'
import mongoose from 'mongoose'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

// GET /api/dashboard/instructor - L<PERSON>y dữ liệu dashboard cho giảng viên
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Verify user is instructor or admin
    const user = await (User as any).findById(session.user.id)
    if (!user || (user.role !== UserRole.INSTRUCTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({
        success: false,
        error: 'Bạn không có quyền truy cập trang này'
      }, { status: 403 })
    }
    
    // Get instructor's courses
    const courses = await (Course as any).find({
      instructor: session.user.id
    })
    .select('title slug status thumbnail stats pricing createdAt publishedAt')
    .sort({ createdAt: -1 })
    .lean()
    
    // Calculate instructor statistics
    const stats = {
      totalCourses: courses.length,
      publishedCourses: courses.filter(c => c.status === CourseStatus.PUBLISHED).length,
      totalStudents: courses.reduce((total, course) => total + (course.stats?.totalStudents || 0), 0),
      totalRevenue: 0, // Will be calculated from enrollments
      averageRating: 0,
      totalRatings: 0
    }
    
    // Calculate average rating and total ratings
    const coursesWithRatings = courses.filter(c => c.stats?.totalRatings > 0)
    if (coursesWithRatings.length > 0) {
      const totalWeightedRating = coursesWithRatings.reduce(
        (total, course) => total + (course.stats.averageRating * course.stats.totalRatings), 0
      )
      stats.totalRatings = coursesWithRatings.reduce(
        (total, course) => total + course.stats.totalRatings, 0
      )
      stats.averageRating = stats.totalRatings > 0 ? totalWeightedRating / stats.totalRatings : 0
    }
    
    // Calculate total revenue from enrollments
    const courseIds = courses.map(c => c._id)
    const enrollments = await (Enrollment as any).find({
      courseId: { $in: courseIds },
      status: { $in: [EnrollmentStatus.ACTIVE, EnrollmentStatus.COMPLETED] }
    }).select('payment.amount').lean()
    
    stats.totalRevenue = enrollments.reduce((total, enrollment) => 
      total + (enrollment.payment?.amount || 0), 0
    )
    
    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const recentEnrollments = await (Enrollment as any).find({
      courseId: { $in: courseIds },
      createdAt: { $gte: thirtyDaysAgo }
    })
    .populate('userId', 'profile.firstName profile.lastName')
    .populate('courseId', 'title')
    .sort({ createdAt: -1 })
    .limit(20)
    .lean()
    
    const recentCompletions = await (Enrollment as any).find({
      courseId: { $in: courseIds },
      'progress.status': 'completed',
      updatedAt: { $gte: thirtyDaysAgo }
    })
    .populate('userId', 'profile.firstName profile.lastName')
    .populate('courseId', 'title')
    .sort({ updatedAt: -1 })
    .limit(20)
    .lean()
    
    // Format recent activity
    const recentActivity = [
      ...recentEnrollments.map(enrollment => ({
        type: 'enrollment' as const,
        studentName: `${enrollment.userId.profile.firstName} ${enrollment.userId.profile.lastName}`,
        courseName: enrollment.courseId.title,
        timestamp: enrollment.createdAt,
        details: {
          enrollmentId: enrollment._id,
          amount: enrollment.payment?.amount || 0
        }
      })),
      ...recentCompletions.map(enrollment => ({
        type: 'completion' as const,
        studentName: `${enrollment.userId.profile.firstName} ${enrollment.userId.profile.lastName}`,
        courseName: enrollment.courseId.title,
        timestamp: enrollment.updatedAt,
        details: {
          enrollmentId: enrollment._id,
          completionPercentage: enrollment.progress.completionPercentage
        }
      }))
    ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    
    // Get monthly revenue data (last 12 months)
    const monthlyRevenue = await getMonthlyRevenue(courseIds)
    
    // Get top performing courses
    const topCourses = courses
      .filter(c => c.status === CourseStatus.PUBLISHED)
      .sort((a, b) => (b.stats?.totalStudents || 0) - (a.stats?.totalStudents || 0))
      .slice(0, 5)
    
    // Get course performance metrics
    const courseMetrics = await getCourseMetrics(courseIds)
    
    return NextResponse.json({
      success: true,
      data: {
        courses,
        stats,
        recentActivity: recentActivity.slice(0, 20),
        monthlyRevenue,
        topCourses,
        courseMetrics
      }
    })
    
  } catch (error) {
    console.error('Error fetching instructor dashboard:', error)
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi tải dashboard'
    }, { status: 500 })
  }
}

// Helper function to get monthly revenue data
async function getMonthlyRevenue(courseIds: mongoose.Types.ObjectId[]) {
  try {
    const twelveMonthsAgo = new Date()
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)
    
    const monthlyData = await (Enrollment as any).aggregate([
      {
        $match: {
          courseId: { $in: courseIds },
          'payment.paidAt': { $gte: twelveMonthsAgo },
          status: { $in: [EnrollmentStatus.ACTIVE, EnrollmentStatus.COMPLETED] }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$payment.paidAt' },
            month: { $month: '$payment.paidAt' }
          },
          revenue: { $sum: '$payment.amount' },
          enrollments: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ])
    
    // Fill in missing months with zero values
    const result = []
    const now = new Date()
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      
      const data = monthlyData.find(d => d._id.year === year && d._id.month === month)
      
      result.push({
        month: date.toLocaleDateString('vi-VN', { month: 'short', year: 'numeric' }),
        revenue: data?.revenue || 0,
        enrollments: data?.enrollments || 0
      })
    }
    
    return result
  } catch (error) {
    console.error('Error getting monthly revenue:', error)
    return []
  }
}

// Helper function to get course performance metrics
async function getCourseMetrics(courseIds: mongoose.Types.ObjectId[]) {
  try {
    const metrics = await (Enrollment as any).aggregate([
      {
        $match: {
          courseId: { $in: courseIds }
        }
      },
      {
        $group: {
          _id: '$courseId',
          totalEnrollments: { $sum: 1 },
          activeEnrollments: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          completedEnrollments: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          averageProgress: { $avg: '$progress.completionPercentage' },
          totalRevenue: { $sum: '$payment.amount' },
          averageWatchTime: { $avg: '$progress.totalWatchTime' }
        }
      },
      {
        $lookup: {
          from: 'courses',
          localField: '_id',
          foreignField: '_id',
          as: 'course'
        }
      },
      {
        $unwind: '$course'
      },
      {
        $project: {
          courseTitle: '$course.title',
          courseSlug: '$course.slug',
          totalEnrollments: 1,
          activeEnrollments: 1,
          completedEnrollments: 1,
          completionRate: {
            $cond: [
              { $gt: ['$totalEnrollments', 0] },
              { $multiply: [{ $divide: ['$completedEnrollments', '$totalEnrollments'] }, 100] },
              0
            ]
          },
          averageProgress: { $round: ['$averageProgress', 1] },
          totalRevenue: 1,
          averageWatchTime: { $round: [{ $divide: ['$averageWatchTime', 60] }, 1] } // Convert to minutes
        }
      },
      {
        $sort: { totalEnrollments: -1 }
      }
    ])
    
    return metrics
  } catch (error) {
    console.error('Error getting course metrics:', error)
    return []
  }
}
