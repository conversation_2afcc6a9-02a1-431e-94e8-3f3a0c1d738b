{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/home", "destination": "/", "statusCode": 308, "regex": "^(?!/_next)/home(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/courses/[id]", "regex": "^/api/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/courses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/courses/[id]/enroll", "regex": "^/api/courses/([^/]+?)/enroll(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/courses/(?<nxtPid>[^/]+?)/enroll(?:/)?$"}, {"page": "/courses/[slug]", "regex": "^/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/courses/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/error", "regex": "^/auth/error(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/error(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/courses", "regex": "^/courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/courses(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/instructor", "regex": "^/dashboard/instructor(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/instructor(?:/)?$"}, {"page": "/dashboard/student", "regex": "^/dashboard/student(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student(?:/)?$"}, {"page": "/test-components", "regex": "^/test\\-components(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-components(?:/)?$"}, {"page": "/test-dashboard", "regex": "^/test\\-dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-dashboard(?:/)?$"}, {"page": "/test-layout", "regex": "^/test\\-layout(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-layout(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}