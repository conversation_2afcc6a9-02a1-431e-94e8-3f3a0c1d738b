'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Loading } from '@/components/ui/Loading'

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (status === 'authenticated' && session?.user) {
      // Redirect based on user role
      const userRole = session.user.role || 'student'
      
      if (userRole === 'instructor' || userRole === 'admin') {
        router.push('/dashboard/instructor')
      } else {
        router.push('/dashboard/student')
      }
    }
  }, [status, session, router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loading size="lg" />
        <p className="mt-4 text-gray-600"><PERSON><PERSON> chuyển hướng đến dashboard...</p>
      </div>
    </div>
  )
}
