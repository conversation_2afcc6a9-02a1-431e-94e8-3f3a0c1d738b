# Development Implementation Plan - <PERSON><PERSON> thống LMS

## Tổng quan Kế hoạch Triển khai

### M<PERSON><PERSON> tiêu
Xây dựng hệ thống LMS Next.js fullstack hoàn chỉnh với 5 modules chính, 12 database collections, và 200+ API endpoints theo các tài liệu specifications đã định nghĩa.

### Timeline Tổng thể
- **Total Duration**: 16-20 weeks (4-5 months)
- **Team Size**: 3-4 developers (1 senior, 2-3 mid-level)
- **Phases**: 7 phases (0-6) với deliverables rõ ràng

### Technology Stack
- **Frontend**: Next.js 14+, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, NextAuth.js
- **Database**: MongoDB, Mongoose ODM
- **AI Integration**: OpenAI API, Google Cloud AI
- **File Storage**: AWS S3 hoặc Cloudinary
- **Payment**: Stripe, PayPal
- **Real-time**: Socket.IO
- **Testing**: Jest, React Testing Library, Supertest

---

## PHASE 0: PROJECT SETUP (Week 1-2)

### Objectives
- Khởi tạo Next.js project với cấu trúc folder chuẩn
- Setup development environment và tools
- Cấu hình database connection và authentication
- Tạo foundation code cho các phases tiếp theo

### Technical Tasks

#### 1. Project Initialization
```bash
# Create Next.js project
npx create-next-app@latest lms-system --typescript --tailwind --eslint --app

# Install core dependencies
npm install mongoose next-auth @next-auth/mongodb-adapter
npm install @aws-sdk/client-s3 stripe socket.io
npm install zod react-hook-form @hookform/resolvers
npm install zustand @tanstack/react-query

# Install dev dependencies
npm install -D jest @testing-library/react @testing-library/jest-dom
npm install -D supertest @types/supertest
npm install -D prettier eslint-config-prettier
```

#### 2. Folder Structure Setup
```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/
│   │   ├── courses/
│   │   ├── assessments/
│   │   └── profile/
│   ├── api/
│   │   ├── auth/
│   │   ├── users/
│   │   ├── courses/
│   │   └── webhooks/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/
│   ├── forms/
│   ├── layout/
│   └── providers/
├── lib/
│   ├── auth/
│   ├── database/
│   │   ├── models/
│   │   └── services/
│   ├── utils/
│   ├── validations/
│   └── constants/
├── types/
├── hooks/
├── stores/
└── middleware.ts
```

#### 3. Configuration Files
```typescript
// lib/database/connection.ts
import mongoose from 'mongoose'

const MONGODB_URI = process.env.MONGODB_URI!

if (!MONGODB_URI) {
  throw new Error('Please define MONGODB_URI environment variable')
}

let cached = global.mongoose

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null }
}

export async function connectDB() {
  if (cached.conn) {
    return cached.conn
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    }

    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose
    })
  }

  try {
    cached.conn = await cached.promise
  } catch (e) {
    cached.promise = null
    throw e
  }

  return cached.conn
}
```

#### 4. Environment Variables
```bash
# .env.local
MONGODB_URI=mongodb://localhost:27017/lms_database
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# AI Services
OPENAI_API_KEY=your-openai-key
GOOGLE_CLOUD_API_KEY=your-google-key

# File Storage
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_S3_BUCKET=your-bucket-name
AWS_REGION=ap-southeast-1

# Payment
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_WEBHOOK_SECRET=your-webhook-secret
```

### Deliverables
- [ ] Next.js project với TypeScript setup
- [ ] Database connection working
- [ ] Basic authentication flow
- [ ] Folder structure theo standards
- [ ] Development tools configured
- [ ] Environment variables setup

### Acceptance Criteria
- Project runs successfully on `npm run dev`
- Database connection established
- Basic login/register pages accessible
- ESLint và Prettier working
- Git repository initialized với proper .gitignore

### Timeline: 2 weeks (10 person-days)

---

## PHASE 1: CORE AUTHENTICATION & USER MANAGEMENT (Week 3-4)

### Objectives
- Implement complete authentication system
- User registration, login, profile management
- Role-based access control (RBAC)
- Email verification và password reset
- Admin user management interface

### Database Models to Implement
```typescript
// lib/database/models/User.ts
interface IUser {
  email: string
  password: string
  profile: UserProfile
  role: 'student' | 'teacher' | 'admin' | 'super_admin'
  subscription: UserSubscription
  preferences: UserPreferences
  status: 'active' | 'inactive' | 'suspended'
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
}
```

### API Endpoints Priority
**High Priority:**
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Reset password

**Medium Priority:**
- `POST /api/users/upload-avatar` - Avatar upload
- `GET /api/admin/users` - Admin user list
- `PUT /api/admin/users/:id` - Admin update user
- `POST /api/auth/verify-email` - Email verification

### Frontend Components
```typescript
// components/auth/
├── LoginForm.tsx
├── RegisterForm.tsx
├── ForgotPasswordForm.tsx
├── ResetPasswordForm.tsx
└── EmailVerification.tsx

// components/profile/
├── ProfileForm.tsx
├── AvatarUpload.tsx
├── PasswordChange.tsx
└── PreferencesForm.tsx

// components/admin/
├── UserList.tsx
├── UserDetail.tsx
└── UserActions.tsx
```

### Implementation Steps

#### Step 1: NextAuth.js Configuration
```typescript
// lib/auth/authOptions.ts
import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { MongoDBAdapter } from '@next-auth/mongodb-adapter'
import { connectDB } from '@/lib/database'
import { User } from '@/lib/database/models/User'
import bcrypt from 'bcryptjs'

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        await connectDB()
        const user = await User.findOne({ email: credentials.email })

        if (!user || !await bcrypt.compare(credentials.password, user.password)) {
          return null
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: `${user.profile.firstName} ${user.profile.lastName}`,
          role: user.role
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    }
  }
}
```

#### Step 2: User Model Implementation
```typescript
// lib/database/models/User.ts
import mongoose, { Schema, Document } from 'mongoose'
import bcrypt from 'bcryptjs'

interface IUser extends Document {
  email: string
  password: string
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
    dateOfBirth?: Date
  }
  role: 'student' | 'teacher' | 'admin' | 'super_admin'
  status: 'active' | 'inactive' | 'suspended'
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
}

const userSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true, lowercase: true },
  password: { type: String, required: true },
  profile: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    avatar: String,
    phone: String,
    dateOfBirth: Date
  },
  role: { 
    type: String, 
    enum: ['student', 'teacher', 'admin', 'super_admin'], 
    default: 'student' 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'suspended'], 
    default: 'active' 
  },
  emailVerified: { type: Boolean, default: false }
}, {
  timestamps: true
})

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next()
  
  this.password = await bcrypt.hash(this.password, 12)
  next()
})

// Indexes
userSchema.index({ email: 1 })
userSchema.index({ role: 1, status: 1 })

export const User = mongoose.models.User || mongoose.model<IUser>('User', userSchema)
```

#### Step 3: Authentication API Routes
```typescript
// app/api/auth/register/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { connectDB } from '@/lib/database'
import { User } from '@/lib/database/models/User'
import { createApiResponse, createErrorResponse } from '@/lib/utils/api'

const registerSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
  password: z.string().min(8, 'Mật khẩu phải có ít nhất 8 ký tự'),
  confirmPassword: z.string(),
  profile: z.object({
    firstName: z.string().min(1, 'Họ không được để trống'),
    lastName: z.string().min(1, 'Tên không được để trống')
  })
}).refine(data => data.password === data.confirmPassword, {
  message: 'Mật khẩu xác nhận không khớp',
  path: ['confirmPassword']
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = registerSchema.parse(body)
    
    await connectDB()
    
    // Check if user exists
    const existingUser = await User.findOne({ email: validatedData.email })
    if (existingUser) {
      return createErrorResponse('Email đã được sử dụng', 409)
    }
    
    // Create user
    const user = new User({
      email: validatedData.email,
      password: validatedData.password,
      profile: validatedData.profile
    })
    
    await user.save()
    
    // Remove password from response
    const userResponse = user.toObject()
    delete userResponse.password
    
    return NextResponse.json(
      createApiResponse(userResponse, 'Đăng ký thành công'),
      { status: 201 }
    )
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Dữ liệu không hợp lệ', 400, error.errors)
    }
    
    console.error('Registration error:', error)
    return createErrorResponse('Đã xảy ra lỗi, vui lòng thử lại', 500)
  }
}
```

### Testing Requirements
```typescript
// __tests__/auth/register.test.ts
import { createMocks } from 'node-mocks-http'
import { POST } from '@/app/api/auth/register/route'
import { connectDB, disconnectDB } from '@/lib/database'
import { User } from '@/lib/database/models/User'

describe('/api/auth/register', () => {
  beforeAll(async () => {
    await connectDB()
  })
  
  afterAll(async () => {
    await disconnectDB()
  })
  
  beforeEach(async () => {
    await User.deleteMany({})
  })
  
  it('should register new user successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      profile: {
        firstName: 'John',
        lastName: 'Doe'
      }
    }
    
    const { req } = createMocks({
      method: 'POST',
      body: userData
    })
    
    const response = await POST(req)
    const data = await response.json()
    
    expect(response.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.data.email).toBe(userData.email)
    expect(data.data.password).toBeUndefined()
  })
  
  it('should return error for duplicate email', async () => {
    // Create existing user
    await User.create({
      email: '<EMAIL>',
      password: 'password123',
      profile: { firstName: 'John', lastName: 'Doe' }
    })
    
    const userData = {
      email: '<EMAIL>',
      password: 'newpassword123',
      confirmPassword: 'newpassword123',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith'
      }
    }
    
    const { req } = createMocks({
      method: 'POST',
      body: userData
    })
    
    const response = await POST(req)
    const data = await response.json()
    
    expect(response.status).toBe(409)
    expect(data.success).toBe(false)
    expect(data.message).toBe('Email đã được sử dụng')
  })
})
```

### Acceptance Criteria
- [ ] User có thể đăng ký tài khoản mới
- [ ] User có thể đăng nhập/đăng xuất
- [ ] Profile management working (view/edit)
- [ ] Avatar upload functionality
- [ ] Password reset flow complete
- [ ] Email verification working
- [ ] Admin có thể quản lý users
- [ ] Role-based access control implemented
- [ ] All API endpoints tested
- [ ] Frontend forms với validation

### Timeline: 2 weeks (14 person-days)

### Dependencies
- Phase 0 completed
- Database connection established
- NextAuth.js configured

---

## PHASE 2: COURSE MANAGEMENT SYSTEM (Week 5-7)

### Objectives
- Implement complete course management functionality
- Course CRUD operations với rich content support
- Course enrollment system
- Lesson content management (video, audio, documents)
- Course categories và filtering
- Instructor dashboard

### Database Models to Implement
```typescript
// lib/database/models/Course.ts
interface ICourse {
  title: string
  slug: string
  description: string
  instructor: ObjectId
  category: 'english' | 'japanese' | 'korean' | 'chinese'
  level: 'beginner' | 'intermediate' | 'advanced'
  pricing: {
    type: 'free' | 'paid'
    price: number
    currency: string
  }
  content: {
    modules: Array<{
      title: string
      lessons: Array<{
        title: string
        type: 'video' | 'audio' | 'text' | 'quiz'
        content: any
        duration: number
      }>
    }>
  }
  settings: {
    isPublished: boolean
    maxStudents: number
  }
  stats: {
    totalEnrollments: number
    averageRating: number
  }
}

// lib/database/models/Enrollment.ts
interface IEnrollment {
  userId: ObjectId
  courseId: ObjectId
  progress: {
    status: 'enrolled' | 'in_progress' | 'completed'
    completionPercentage: number
    lessonsCompleted: Array<{
      lessonId: ObjectId
      completedAt: Date
    }>
  }
  enrolledAt: Date
}
```

### API Endpoints Priority
**High Priority:**
- `GET /api/courses` - Public course listing
- `GET /api/courses/:id` - Course details
- `POST /api/courses/:id/enroll` - Course enrollment
- `GET /api/courses/:id/lessons/:lessonId` - Lesson content
- `POST /api/courses/:id/lessons/:lessonId/complete` - Mark lesson complete
- `POST /api/courses` - Create course (instructor)
- `PUT /api/courses/:id` - Update course
- `GET /api/instructor/courses` - Instructor's courses

**Medium Priority:**
- `POST /api/courses/:id/upload-thumbnail` - Course thumbnail
- `POST /api/courses/:id/modules` - Add module
- `POST /api/courses/:id/modules/:moduleId/lessons` - Add lesson
- `POST /api/upload/course-video` - Video upload
- `GET /api/courses/:id/analytics` - Course analytics

### Frontend Components
```typescript
// components/course/
├── CourseCard.tsx
├── CourseDetail.tsx
├── CourseList.tsx
├── CourseFilters.tsx
├── EnrollmentButton.tsx
└── CourseProgress.tsx

// components/lesson/
├── VideoPlayer.tsx
├── AudioPlayer.tsx
├── LessonContent.tsx
├── LessonNavigation.tsx
└── LessonProgress.tsx

// components/instructor/
├── CourseBuilder.tsx
├── ModuleEditor.tsx
├── LessonEditor.tsx
├── ContentUpload.tsx
└── CourseAnalytics.tsx
```

### Implementation Steps

#### Step 1: Course Model và Service
```typescript
// lib/database/services/courseService.ts
export class CourseService {
  static async createCourse(data: CreateCourseData, instructorId: string) {
    await connectDB()

    const slug = generateSlug(data.title)

    const course = new Course({
      ...data,
      slug,
      instructor: instructorId,
      settings: {
        isPublished: false,
        ...data.settings
      },
      stats: {
        totalEnrollments: 0,
        averageRating: 0
      }
    })

    await course.save()
    return course
  }

  static async getCourses(filters: CourseFilters = {}) {
    const {
      page = 1,
      limit = 12,
      category,
      level,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = filters

    const query: any = { 'settings.isPublished': true }

    if (category) query.category = category
    if (level) query.level = level
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }

    const skip = (page - 1) * limit
    const sortOptions: any = {}
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1

    const [courses, total] = await Promise.all([
      Course.find(query)
        .populate('instructor', 'profile.firstName profile.lastName profile.avatar')
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .lean(),
      Course.countDocuments(query)
    ])

    return {
      courses,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  static async enrollUserInCourse(userId: string, courseId: string) {
    const session = await mongoose.startSession()

    try {
      session.startTransaction()

      // Check existing enrollment
      const existingEnrollment = await Enrollment.findOne({
        userId,
        courseId
      }).session(session)

      if (existingEnrollment) {
        throw new Error('Bạn đã đăng ký khóa học này rồi')
      }

      // Create enrollment
      const enrollment = new Enrollment({
        userId,
        courseId,
        progress: {
          status: 'enrolled',
          completionPercentage: 0,
          lessonsCompleted: []
        },
        enrolledAt: new Date()
      })

      await enrollment.save({ session })

      // Update course stats
      await Course.findByIdAndUpdate(
        courseId,
        { $inc: { 'stats.totalEnrollments': 1 } },
        { session }
      )

      await session.commitTransaction()
      return enrollment

    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      session.endSession()
    }
  }
}
```

#### Step 2: Course API Routes
```typescript
// app/api/courses/route.ts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filters = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '12'),
      category: searchParams.get('category') || undefined,
      level: searchParams.get('level') || undefined,
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }

    const result = await CourseService.getCourses(filters)

    return NextResponse.json(createApiResponse(result))
  } catch (error) {
    console.error('Get courses error:', error)
    return createErrorResponse('Không thể lấy danh sách khóa học', 500)
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || !['teacher', 'admin'].includes(session.user.role)) {
      return createErrorResponse('Không có quyền tạo khóa học', 403)
    }

    const body = await request.json()
    const validatedData = createCourseSchema.parse(body)

    const course = await CourseService.createCourse(validatedData, session.user.id)

    return NextResponse.json(
      createApiResponse(course, 'Tạo khóa học thành công'),
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Dữ liệu không hợp lệ', 400, error.errors)
    }

    console.error('Create course error:', error)
    return createErrorResponse('Không thể tạo khóa học', 500)
  }
}
```

#### Step 3: Course Enrollment API
```typescript
// app/api/courses/[courseId]/enroll/route.ts
export async function POST(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return createErrorResponse('Chưa đăng nhập', 401)
    }

    const body = await request.json()
    const { paymentMethod, activationCode } = body

    // Validate course exists và available
    const course = await Course.findById(params.courseId)
    if (!course || !course.settings.isPublished) {
      return createErrorResponse('Khóa học không tồn tại hoặc chưa được xuất bản', 404)
    }

    // Handle different enrollment methods
    let enrollmentResult

    if (paymentMethod === 'activation_code') {
      // Validate activation code
      const codeValidation = await ActivationCodeService.validateCode(activationCode)
      if (!codeValidation.valid) {
        return createErrorResponse('Mã kích hoạt không hợp lệ', 400)
      }

      // Enroll with activation code
      enrollmentResult = await CourseService.enrollWithActivationCode(
        session.user.id,
        params.courseId,
        activationCode
      )
    } else if (course.pricing.type === 'free') {
      // Free enrollment
      enrollmentResult = await CourseService.enrollUserInCourse(
        session.user.id,
        params.courseId
      )
    } else {
      // Paid enrollment - create payment intent
      const paymentIntent = await StripeService.createPaymentIntent({
        amount: course.pricing.price,
        currency: course.pricing.currency,
        courseId: params.courseId,
        userId: session.user.id
      })

      return NextResponse.json(createApiResponse({
        requiresPayment: true,
        paymentIntent
      }))
    }

    return NextResponse.json(
      createApiResponse(enrollmentResult, 'Đăng ký khóa học thành công'),
      { status: 201 }
    )

  } catch (error) {
    console.error('Course enrollment error:', error)
    return createErrorResponse(error.message || 'Không thể đăng ký khóa học', 500)
  }
}
```

### Testing Requirements
```typescript
// __tests__/courses/courseService.test.ts
describe('CourseService', () => {
  describe('createCourse', () => {
    it('should create course successfully', async () => {
      const courseData = {
        title: 'Test Course',
        description: 'Test Description',
        category: 'english',
        level: 'beginner',
        pricing: { type: 'free', price: 0, currency: 'VND' }
      }

      const result = await CourseService.createCourse(courseData, 'instructor_id')

      expect(result.title).toBe(courseData.title)
      expect(result.slug).toBe('test-course')
      expect(result.settings.isPublished).toBe(false)
    })
  })

  describe('enrollUserInCourse', () => {
    it('should enroll user successfully', async () => {
      const course = await Course.create({
        title: 'Test Course',
        instructor: 'instructor_id',
        settings: { isPublished: true }
      })

      const enrollment = await CourseService.enrollUserInCourse('user_id', course._id)

      expect(enrollment.userId.toString()).toBe('user_id')
      expect(enrollment.courseId.toString()).toBe(course._id.toString())
      expect(enrollment.progress.status).toBe('enrolled')
    })

    it('should prevent duplicate enrollment', async () => {
      const course = await Course.create({
        title: 'Test Course',
        instructor: 'instructor_id'
      })

      // First enrollment
      await CourseService.enrollUserInCourse('user_id', course._id)

      // Second enrollment should fail
      await expect(
        CourseService.enrollUserInCourse('user_id', course._id)
      ).rejects.toThrow('Bạn đã đăng ký khóa học này rồi')
    })
  })
})
```

### Acceptance Criteria
- [ ] Public course listing với filters working
- [ ] Course detail page với full information
- [ ] Course enrollment flow complete
- [ ] Lesson content viewing (video/audio/text)
- [ ] Progress tracking working
- [ ] Instructor có thể tạo/edit courses
- [ ] Course builder interface functional
- [ ] File upload cho course content
- [ ] Course analytics basic metrics
- [ ] All API endpoints tested

### Timeline: 3 weeks (21 person-days)

### Dependencies
- Phase 1 completed (User authentication)
- File upload service implemented
- Payment integration basic setup

---

## PHASE 3: ASSESSMENT SYSTEM (WITHOUT AI) (Week 8-10)

### Objectives
- Implement assessment creation và management
- Multiple question types (multiple choice, fill blanks, essay)
- Assessment taking flow với timer
- Basic scoring system (non-AI)
- Result tracking và analytics
- Question bank management

### Database Models to Implement
```typescript
// lib/database/models/Assessment.ts
interface IAssessment {
  title: string
  type: 'listening' | 'speaking' | 'reading' | 'writing' | 'mixed'
  courseId?: ObjectId
  configuration: {
    duration: number
    maxAttempts: number
    passingScore: number
    randomizeQuestions: boolean
  }
  questions: Array<{
    type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'essay'
    content: any
    points: number
    correctAnswer?: any
  }>
  settings: {
    isActive: boolean
    availableFrom?: Date
    availableUntil?: Date
  }
}

// lib/database/models/AssessmentResult.ts
interface IAssessmentResult {
  userId: ObjectId
  assessmentId: ObjectId
  attempt: {
    attemptNumber: number
    startedAt: Date
    submittedAt: Date
    timeSpent: number
  }
  responses: Array<{
    questionId: ObjectId
    response: any
    scoring: {
      points: number
      maxPoints: number
      isCorrect: boolean
    }
  }>
  results: {
    totalScore: number
    maxScore: number
    percentage: number
    passed: boolean
  }
  status: 'in_progress' | 'submitted' | 'graded'
}
```

### API Endpoints Priority
**High Priority:**
- `GET /api/assessments` - List assessments
- `GET /api/assessments/:id` - Assessment details
- `POST /api/assessments/:id/start` - Start assessment
- `POST /api/assessments/:id/submit-answer` - Submit answer
- `POST /api/assessments/:id/submit` - Submit assessment
- `GET /api/assessments/:id/results` - Get results
- `POST /api/assessments` - Create assessment (teacher)

**Medium Priority:**
- `GET /api/assessments/results/:resultId` - Detailed result
- `GET /api/assessments/results/:resultId/review` - Review answers
- `PUT /api/assessments/:id` - Update assessment
- `GET /api/teacher/assessments` - Teacher's assessments

### Implementation Steps

#### Step 1: Assessment Models
```typescript
// lib/database/models/Assessment.ts
const questionSchema = new Schema({
  type: {
    type: String,
    enum: ['multiple_choice', 'true_false', 'fill_blank', 'essay'],
    required: true
  },
  content: {
    question: { type: String, required: true },
    // Multiple choice specific
    options: [{
      id: String,
      text: String,
      isCorrect: Boolean
    }],
    // Fill blank specific
    blanks: [{
      position: Number,
      correctAnswers: [String],
      caseSensitive: { type: Boolean, default: false }
    }],
    // Essay specific
    rubric: {
      maxWords: Number,
      criteria: [String]
    }
  },
  points: { type: Number, required: true, default: 1 },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  }
})

const assessmentSchema = new Schema({
  title: { type: String, required: true },
  type: {
    type: String,
    enum: ['listening', 'speaking', 'reading', 'writing', 'mixed'],
    required: true
  },
  courseId: { type: Schema.Types.ObjectId, ref: 'Course' },
  configuration: {
    duration: { type: Number, required: true }, // minutes
    maxAttempts: { type: Number, default: 3 },
    passingScore: { type: Number, default: 70 }, // percentage
    randomizeQuestions: { type: Boolean, default: true },
    showResults: {
      type: String,
      enum: ['immediate', 'after_submission', 'manual'],
      default: 'immediate'
    }
  },
  questions: [questionSchema],
  settings: {
    isActive: { type: Boolean, default: true },
    availableFrom: Date,
    availableUntil: Date
  },
  createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
}, { timestamps: true })

export const Assessment = mongoose.models.Assessment ||
  mongoose.model('Assessment', assessmentSchema)
```

#### Step 2: Assessment Service
```typescript
// lib/database/services/assessmentService.ts
export class AssessmentService {
  static async createAssessment(data: CreateAssessmentData, createdBy: string) {
    await connectDB()

    const assessment = new Assessment({
      ...data,
      createdBy
    })

    await assessment.save()
    return assessment
  }

  static async startAssessment(assessmentId: string, userId: string) {
    await connectDB()

    const assessment = await Assessment.findById(assessmentId)
    if (!assessment || !assessment.settings.isActive) {
      throw new Error('Bài kiểm tra không khả dụng')
    }

    // Check attempt limit
    const previousAttempts = await AssessmentResult.countDocuments({
      userId,
      assessmentId
    })

    if (previousAttempts >= assessment.configuration.maxAttempts) {
      throw new Error('Bạn đã hết số lần làm bài')
    }

    // Create new session
    const sessionId = uuidv4()
    const startTime = new Date()
    const endTime = new Date(startTime.getTime() + assessment.configuration.duration * 60000)

    // Randomize questions if enabled
    let questions = [...assessment.questions]
    if (assessment.configuration.randomizeQuestions) {
      questions = shuffleArray(questions)
    }

    // Remove correct answers from questions
    const questionsForUser = questions.map(q => ({
      _id: q._id,
      type: q.type,
      content: {
        question: q.content.question,
        options: q.content.options?.map(opt => ({
          id: opt.id,
          text: opt.text
          // Remove isCorrect
        })),
        blanks: q.content.blanks?.map(blank => ({
          position: blank.position
          // Remove correctAnswers
        }))
      },
      points: q.points
    }))

    return {
      sessionId,
      attemptNumber: previousAttempts + 1,
      startTime,
      endTime,
      questions: questionsForUser,
      timeRemaining: assessment.configuration.duration * 60
    }
  }

  static async submitAnswer(
    assessmentId: string,
    userId: string,
    sessionId: string,
    questionId: string,
    response: any
  ) {
    // Store answer in temporary collection or cache
    await TemporaryAnswer.findOneAndUpdate(
      { sessionId, questionId },
      {
        sessionId,
        questionId,
        userId,
        assessmentId,
        response,
        submittedAt: new Date()
      },
      { upsert: true }
    )

    return { saved: true }
  }

  static async submitAssessment(
    assessmentId: string,
    userId: string,
    sessionId: string
  ) {
    const session = await mongoose.startSession()

    try {
      session.startTransaction()

      // Get assessment
      const assessment = await Assessment.findById(assessmentId).session(session)
      if (!assessment) {
        throw new Error('Bài kiểm tra không tồn tại')
      }

      // Get all answers for this session
      const answers = await TemporaryAnswer.find({ sessionId }).session(session)

      // Score the assessment
      const scoringResult = await this.scoreAssessment(assessment, answers)

      // Create assessment result
      const result = new AssessmentResult({
        userId,
        assessmentId,
        attempt: {
          attemptNumber: await this.getNextAttemptNumber(userId, assessmentId),
          startedAt: answers[0]?.createdAt || new Date(),
          submittedAt: new Date(),
          timeSpent: 0 // Calculate from start/end time
        },
        responses: scoringResult.responses,
        results: scoringResult.summary,
        status: 'graded'
      })

      await result.save({ session })

      // Clean up temporary answers
      await TemporaryAnswer.deleteMany({ sessionId }).session(session)

      await session.commitTransaction()

      return {
        resultId: result._id,
        status: 'submitted',
        results: scoringResult.summary
      }

    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      session.endSession()
    }
  }

  private static async scoreAssessment(assessment: any, answers: any[]) {
    const responses = []
    let totalScore = 0
    let maxScore = 0

    for (const question of assessment.questions) {
      const answer = answers.find(a => a.questionId.toString() === question._id.toString())
      maxScore += question.points

      if (!answer) {
        responses.push({
          questionId: question._id,
          response: null,
          scoring: {
            points: 0,
            maxPoints: question.points,
            isCorrect: false
          }
        })
        continue
      }

      const scoring = this.scoreQuestion(question, answer.response)
      totalScore += scoring.points

      responses.push({
        questionId: question._id,
        response: answer.response,
        scoring
      })
    }

    const percentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0
    const passed = percentage >= assessment.configuration.passingScore

    return {
      responses,
      summary: {
        totalScore,
        maxScore,
        percentage,
        passed
      }
    }
  }

  private static scoreQuestion(question: any, response: any) {
    switch (question.type) {
      case 'multiple_choice':
        const correctOption = question.content.options.find(opt => opt.isCorrect)
        const isCorrect = response.selectedOption === correctOption?.id
        return {
          points: isCorrect ? question.points : 0,
          maxPoints: question.points,
          isCorrect
        }

      case 'true_false':
        const correct = response.answer === question.content.correctAnswer
        return {
          points: correct ? question.points : 0,
          maxPoints: question.points,
          isCorrect: correct
        }

      case 'fill_blank':
        let correctBlanks = 0
        const totalBlanks = question.content.blanks.length

        for (let i = 0; i < totalBlanks; i++) {
          const userAnswer = response.answers[i]?.toLowerCase().trim()
          const correctAnswers = question.content.blanks[i].correctAnswers.map(a => a.toLowerCase())

          if (correctAnswers.includes(userAnswer)) {
            correctBlanks++
          }
        }

        const points = Math.round((correctBlanks / totalBlanks) * question.points)
        return {
          points,
          maxPoints: question.points,
          isCorrect: correctBlanks === totalBlanks
        }

      case 'essay':
        // For now, essays require manual grading
        return {
          points: 0,
          maxPoints: question.points,
          isCorrect: false,
          requiresManualGrading: true
        }

      default:
        return {
          points: 0,
          maxPoints: question.points,
          isCorrect: false
        }
    }
  }
}
```

### Testing Requirements
```typescript
// __tests__/assessments/assessmentService.test.ts
describe('AssessmentService', () => {
  describe('startAssessment', () => {
    it('should start assessment successfully', async () => {
      const assessment = await Assessment.create({
        title: 'Test Assessment',
        type: 'reading',
        configuration: {
          duration: 30,
          maxAttempts: 3,
          passingScore: 70
        },
        questions: [
          {
            type: 'multiple_choice',
            content: {
              question: 'What is 2+2?',
              options: [
                { id: 'A', text: '3', isCorrect: false },
                { id: 'B', text: '4', isCorrect: true }
              ]
            },
            points: 2
          }
        ],
        createdBy: 'teacher_id'
      })

      const result = await AssessmentService.startAssessment(assessment._id, 'user_id')

      expect(result.sessionId).toBeDefined()
      expect(result.attemptNumber).toBe(1)
      expect(result.questions).toHaveLength(1)
      expect(result.questions[0].content.options[0].isCorrect).toBeUndefined()
    })
  })

  describe('scoreQuestion', () => {
    it('should score multiple choice correctly', () => {
      const question = {
        type: 'multiple_choice',
        content: {
          options: [
            { id: 'A', text: '3', isCorrect: false },
            { id: 'B', text: '4', isCorrect: true }
          ]
        },
        points: 2
      }

      const correctResponse = { selectedOption: 'B' }
      const incorrectResponse = { selectedOption: 'A' }

      const correctResult = AssessmentService.scoreQuestion(question, correctResponse)
      const incorrectResult = AssessmentService.scoreQuestion(question, incorrectResponse)

      expect(correctResult.points).toBe(2)
      expect(correctResult.isCorrect).toBe(true)
      expect(incorrectResult.points).toBe(0)
      expect(incorrectResult.isCorrect).toBe(false)
    })
  })
})
```

### Acceptance Criteria
- [ ] Teachers có thể tạo assessments với multiple question types
- [ ] Students có thể làm bài với timer working
- [ ] Auto-scoring cho multiple choice, true/false, fill blanks
- [ ] Essay questions marked for manual grading
- [ ] Assessment results displayed correctly
- [ ] Attempt limits enforced
- [ ] Question randomization working
- [ ] Progress saving during assessment
- [ ] All API endpoints tested

### Timeline: 3 weeks (21 person-days)

### Dependencies
- Phase 2 completed (Course system)
- WebSocket service for real-time timer
- Temporary storage for in-progress answers

---

## PHASE 4: AI INTEGRATION (Week 11-13)

### Objectives
- Integrate OpenAI API cho speaking và writing assessment
- Implement AI scoring algorithms
- Audio processing và transcription
- Automated feedback generation
- AI confidence scoring và fallback mechanisms
- Performance optimization cho AI calls

### AI Services to Implement
```typescript
// lib/ai/openaiService.ts
interface AIAssessmentRequest {
  type: 'speaking' | 'writing'
  content: string | AudioFile
  questionContext: string
  level: 'beginner' | 'intermediate' | 'advanced'
  criteria: string[]
}

interface AIAssessmentResult {
  overallScore: number
  criteriaScores: Record<string, number>
  feedback: string
  transcription?: string
  confidence: number
  processingTime: number
}
```

### API Endpoints Priority
**High Priority:**
- `POST /api/ai/score-speaking` - AI speaking assessment
- `POST /api/ai/score-writing` - AI writing assessment
- `POST /api/upload/speaking-response` - Upload speaking audio
- `GET /api/assessments/results/:id/ai-feedback` - Get AI feedback

**Medium Priority:**
- `POST /api/ai/transcribe-audio` - Audio transcription only
- `POST /api/ai/analyze-pronunciation` - Pronunciation analysis
- `GET /api/ai/processing-status/:jobId` - Check AI job status

### Implementation Steps

#### Step 1: OpenAI Service Setup
```typescript
// lib/ai/openaiService.ts
import OpenAI from 'openai'
import { Logger } from '@/lib/utils/logger'

export class OpenAIService {
  private client: OpenAI
  private readonly maxRetries = 3
  private readonly timeout = 30000 // 30 seconds

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      timeout: this.timeout
    })
  }

  async scoreSpeakingAssessment(request: SpeakingAssessmentRequest): Promise<SpeakingAssessmentResult> {
    const startTime = Date.now()

    try {
      // 1. Transcribe audio using Whisper
      const transcription = await this.transcribeAudio(request.audioUrl)

      // 2. Analyze pronunciation and fluency
      const pronunciationAnalysis = await this.analyzePronunciation(
        request.audioUrl,
        transcription
      )

      // 3. Score content and grammar
      const contentScore = await this.scoreSpokenContent(
        transcription,
        request.questionText,
        request.level
      )

      // 4. Combine scores
      const result = this.combineSpeakingScores(
        pronunciationAnalysis,
        contentScore,
        request.criteria
      )

      const processingTime = (Date.now() - startTime) / 1000

      Logger.info('AI speaking assessment completed', {
        action: 'ai_speaking_assessment',
        metadata: {
          level: request.level,
          processingTime,
          confidence: result.confidence,
          overallScore: result.overallScore
        }
      })

      return {
        ...result,
        transcription,
        processingTime
      }

    } catch (error) {
      Logger.error('AI speaking assessment failed', error, {
        action: 'ai_speaking_assessment_failed',
        metadata: request
      })

      // Return fallback score for manual review
      return this.createFallbackSpeakingResult(error.message)
    }
  }

  async scoreWritingAssessment(request: WritingAssessmentRequest): Promise<WritingAssessmentResult> {
    const startTime = Date.now()

    try {
      const prompt = this.buildWritingScoringPrompt(request)

      const completion = await this.client.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an expert English language assessor specializing in writing evaluation.
                     Provide detailed, constructive feedback and accurate scoring based on the given criteria.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for consistent scoring
        max_tokens: 1500,
        response_format: { type: 'json_object' }
      })

      const result = this.parseWritingAssessmentResult(completion.choices[0].message.content)
      const processingTime = (Date.now() - startTime) / 1000

      Logger.info('AI writing assessment completed', {
        action: 'ai_writing_assessment',
        metadata: {
          wordCount: request.text.split(' ').length,
          processingTime,
          confidence: result.confidence,
          overallScore: result.overallScore
        }
      })

      return {
        ...result,
        processingTime
      }

    } catch (error) {
      Logger.error('AI writing assessment failed', error, {
        action: 'ai_writing_assessment_failed',
        metadata: { textLength: request.text.length }
      })

      return this.createFallbackWritingResult(error.message)
    }
  }

  private async transcribeAudio(audioUrl: string): Promise<string> {
    try {
      // Download audio file
      const response = await fetch(audioUrl)
      if (!response.ok) {
        throw new Error(`Failed to download audio: ${response.statusText}`)
      }

      const audioBuffer = await response.arrayBuffer()
      const audioFile = new File([audioBuffer], 'audio.mp3', { type: 'audio/mp3' })

      const transcription = await this.client.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
        language: 'en',
        response_format: 'text'
      })

      return transcription
    } catch (error) {
      Logger.error('Audio transcription failed', error)
      throw new Error('Không thể chuyển đổi audio thành text')
    }
  }

  private buildWritingScoringPrompt(request: WritingAssessmentRequest): string {
    return `
Please evaluate the following English writing sample:

**Task**: ${request.prompt}
**Student Level**: ${request.level}
**Text**: ${request.text}
**Word Count**: ${request.text.split(' ').length}

**Evaluation Criteria**: ${request.criteria.join(', ')}

Please provide a detailed assessment in the following JSON format:
{
  "overallScore": number (0-10),
  "criteriaScores": {
    "grammar": number (0-10),
    "vocabulary": number (0-10),
    "coherence": number (0-10),
    "task_response": number (0-10)
  },
  "feedback": "Detailed feedback in Vietnamese",
  "strengths": ["List of strengths"],
  "improvements": ["List of areas for improvement"],
  "grammarErrors": [
    {
      "text": "incorrect text",
      "correction": "corrected text",
      "explanation": "explanation in Vietnamese"
    }
  ],
  "confidence": number (0-1)
}

Focus on providing constructive, encouraging feedback that helps the student improve.
    `.trim()
  }

  private parseWritingAssessmentResult(content: string): WritingAssessmentResult {
    try {
      const parsed = JSON.parse(content)

      // Validate required fields
      if (!parsed.overallScore || !parsed.criteriaScores || !parsed.feedback) {
        throw new Error('Invalid AI response format')
      }

      // Ensure scores are within valid range
      parsed.overallScore = Math.max(0, Math.min(10, parsed.overallScore))
      Object.keys(parsed.criteriaScores).forEach(key => {
        parsed.criteriaScores[key] = Math.max(0, Math.min(10, parsed.criteriaScores[key]))
      })

      return parsed
    } catch (error) {
      throw new Error('Không thể phân tích kết quả từ AI')
    }
  }

  private createFallbackSpeakingResult(errorMessage: string): SpeakingAssessmentResult {
    return {
      overallScore: 0,
      criteriaScores: {
        pronunciation: 0,
        fluency: 0,
        grammar: 0,
        vocabulary: 0
      },
      feedback: 'Bài nói cần được chấm điểm thủ công do lỗi hệ thống AI.',
      transcription: '',
      confidence: 0,
      processingTime: 0,
      requiresManualReview: true,
      errorMessage
    }
  }

  private createFallbackWritingResult(errorMessage: string): WritingAssessmentResult {
    return {
      overallScore: 0,
      criteriaScores: {
        grammar: 0,
        vocabulary: 0,
        coherence: 0,
        task_response: 0
      },
      feedback: 'Bài viết cần được chấm điểm thủ công do lỗi hệ thống AI.',
      confidence: 0,
      processingTime: 0,
      requiresManualReview: true,
      errorMessage
    }
  }
}
```

#### Step 2: AI Assessment Integration
```typescript
// lib/assessment/aiScoringService.ts
export class AIScoringService {
  private openaiService: OpenAIService
  private processingQueue: Map<string, AIJob> = new Map()

  constructor() {
    this.openaiService = new OpenAIService()
  }

  async processAssessmentWithAI(
    resultId: string,
    assessmentType: 'speaking' | 'writing',
    content: any
  ): Promise<string> {
    const jobId = uuidv4()

    // Add to processing queue
    this.processingQueue.set(jobId, {
      id: jobId,
      resultId,
      type: assessmentType,
      status: 'processing',
      startedAt: new Date()
    })

    // Process asynchronously
    this.processAIJob(jobId, assessmentType, content).catch(error => {
      Logger.error('AI job processing failed', error, {
        jobId,
        resultId,
        type: assessmentType
      })
    })

    return jobId
  }

  private async processAIJob(
    jobId: string,
    assessmentType: 'speaking' | 'writing',
    content: any
  ) {
    try {
      const job = this.processingQueue.get(jobId)
      if (!job) return

      let aiResult: any

      if (assessmentType === 'speaking') {
        aiResult = await this.openaiService.scoreSpeakingAssessment(content)
      } else {
        aiResult = await this.openaiService.scoreWritingAssessment(content)
      }

      // Update assessment result with AI scores
      await AssessmentResult.findByIdAndUpdate(job.resultId, {
        $set: {
          'aiScoring': aiResult,
          'status': aiResult.requiresManualReview ? 'requires_review' : 'completed',
          'processedAt': new Date()
        }
      })

      // Update job status
      job.status = 'completed'
      job.completedAt = new Date()

      // Send notification to user
      const wsService = getWebSocketService()
      const result = await AssessmentResult.findById(job.resultId).populate('userId')

      if (result) {
        wsService.sendNotificationToUser(result.userId._id.toString(), {
          type: 'assessment_scored',
          title: 'Kết quả bài kiểm tra',
          message: 'Bài kiểm tra của bạn đã được chấm điểm',
          data: {
            resultId: job.resultId,
            score: aiResult.overallScore
          }
        })
      }

    } catch (error) {
      const job = this.processingQueue.get(jobId)
      if (job) {
        job.status = 'failed'
        job.error = error.message
      }

      // Mark for manual review
      await AssessmentResult.findByIdAndUpdate(job.resultId, {
        $set: {
          'status': 'requires_review',
          'aiError': error.message,
          'processedAt': new Date()
        }
      })
    }
  }

  getJobStatus(jobId: string): AIJob | null {
    return this.processingQueue.get(jobId) || null
  }
}
```

### Testing Requirements
```typescript
// __tests__/ai/openaiService.test.ts
describe('OpenAIService', () => {
  let openaiService: OpenAIService

  beforeEach(() => {
    openaiService = new OpenAIService()
  })

  describe('scoreWritingAssessment', () => {
    it('should score writing assessment successfully', async () => {
      const request = {
        text: 'My daily routine starts at 6 AM when I wake up...',
        prompt: 'Describe your daily routine',
        level: 'intermediate',
        criteria: ['grammar', 'vocabulary', 'coherence', 'task_response']
      }

      const result = await openaiService.scoreWritingAssessment(request)

      expect(result.overallScore).toBeGreaterThanOrEqual(0)
      expect(result.overallScore).toBeLessThanOrEqual(10)
      expect(result.criteriaScores).toHaveProperty('grammar')
      expect(result.feedback).toBeTruthy()
      expect(result.confidence).toBeGreaterThan(0)
    }, 30000) // 30 second timeout for AI calls

    it('should handle AI service errors gracefully', async () => {
      // Mock OpenAI to throw error
      jest.spyOn(openaiService['client'].chat.completions, 'create')
        .mockRejectedValueOnce(new Error('API Error'))

      const request = {
        text: 'Test text',
        prompt: 'Test prompt',
        level: 'beginner',
        criteria: ['grammar']
      }

      const result = await openaiService.scoreWritingAssessment(request)

      expect(result.requiresManualReview).toBe(true)
      expect(result.overallScore).toBe(0)
      expect(result.errorMessage).toBeTruthy()
    })
  })
})
```

### Acceptance Criteria
- [ ] AI scoring working cho speaking assessments
- [ ] AI scoring working cho writing assessments
- [ ] Audio transcription accurate
- [ ] Detailed feedback generation
- [ ] Error handling và fallback mechanisms
- [ ] Performance optimization (< 30s processing)
- [ ] Confidence scoring implemented
- [ ] Manual review queue cho low confidence scores
- [ ] Real-time notifications cho completed scoring
- [ ] All AI endpoints tested

### Timeline: 3 weeks (21 person-days)

### Dependencies
- Phase 3 completed (Assessment system)
- OpenAI API access configured
- File upload service for audio files
- WebSocket service for notifications

---

## PHASE 5: ACTIVATION CODES & PAYMENT SYSTEM (Week 14-16)

### Objectives
- Implement activation code generation và management
- Payment integration với Stripe/PayPal
- Webhook handling cho payment processing
- Distributor management system
- Revenue tracking và reporting
- Automated enrollment after payment

### Database Models to Implement
```typescript
// lib/database/models/ActivationCode.ts
interface IActivationCode {
  code: string
  type: 'course' | 'subscription' | 'bundle'
  grants: {
    courseIds: ObjectId[]
    subscriptionPlan?: string
    subscriptionDuration?: number
    accessDuration?: number
  }
  configuration: {
    batchId: string
    maxUses: number
    currentUses: number
    validFrom: Date
    validUntil: Date
  }
  usage: Array<{
    userId: ObjectId
    usedAt: Date
  }>
  sales: {
    distributorId?: ObjectId
    originalPrice: number
    salePrice: number
    commission: number
  }
  status: 'active' | 'used' | 'expired' | 'disabled'
}

// lib/database/models/Payment.ts
interface IPayment {
  userId: ObjectId
  transactionId: string
  paymentMethod: 'stripe' | 'paypal'
  amount: {
    subtotal: number
    tax: number
    total: number
  }
  items: Array<{
    type: 'course' | 'subscription'
    itemId: ObjectId
    name: string
    price: number
  }>
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  gatewayData: any
}
```

### API Endpoints Priority
**High Priority:**
- `POST /api/admin/activation-codes/generate` - Generate codes
- `GET /api/admin/activation-codes` - List codes
- `POST /api/activation-codes/validate` - Validate code
- `POST /api/activation-codes/redeem` - Redeem code
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/webhooks/stripe` - Stripe webhook
- `GET /api/payments/history` - Payment history

**Medium Priority:**
- `GET /api/admin/activation-codes/analytics` - Code analytics
- `POST /api/payments/refund` - Process refund
- `GET /api/admin/revenue/dashboard` - Revenue dashboard

### Implementation Steps

#### Step 1: Activation Code Service
```typescript
// lib/database/services/activationCodeService.ts
export class ActivationCodeService {
  static async generateCodes(request: GenerateCodesRequest): Promise<GenerationResult> {
    await connectDB()

    const codes = []
    const batchId = request.batchId || `BATCH_${Date.now()}`

    for (let i = 0; i < request.quantity; i++) {
      const code = this.generateUniqueCode(request.codePrefix, request.codeLength)

      const activationCode = new ActivationCode({
        code,
        type: request.type,
        grants: request.grants,
        configuration: {
          batchId,
          maxUses: request.maxUses || 1,
          currentUses: 0,
          validFrom: request.validFrom,
          validUntil: request.validUntil,
          generatedBy: request.generatedBy
        },
        sales: request.sales,
        status: 'active'
      })

      await activationCode.save()
      codes.push({
        _id: activationCode._id,
        code: activationCode.code,
        status: activationCode.status
      })
    }

    Logger.info('Activation codes generated', {
      action: 'generate_activation_codes',
      metadata: {
        batchId,
        quantity: request.quantity,
        type: request.type,
        generatedBy: request.generatedBy
      }
    })

    return {
      batchId,
      totalGenerated: codes.length,
      codes,
      downloadUrl: `/api/admin/activation-codes/download/${batchId}`
    }
  }

  static async validateCode(code: string, userId?: string): Promise<CodeValidationResult> {
    await connectDB()

    const activationCode = await ActivationCode.findOne({ code })

    if (!activationCode) {
      return { valid: false, reason: 'CODE_NOT_FOUND' }
    }

    if (activationCode.status !== 'active') {
      return { valid: false, reason: 'CODE_INACTIVE' }
    }

    if (activationCode.currentUses >= activationCode.configuration.maxUses) {
      return { valid: false, reason: 'CODE_EXHAUSTED' }
    }

    const now = new Date()
    if (now < activationCode.configuration.validFrom || now > activationCode.configuration.validUntil) {
      return { valid: false, reason: 'CODE_EXPIRED' }
    }

    // Check if user already used this code
    if (userId && activationCode.usage.some(u => u.userId.toString() === userId)) {
      return { valid: false, reason: 'CODE_ALREADY_USED' }
    }

    // Get course details for display
    let grantDetails = {}
    if (activationCode.type === 'course') {
      const courses = await Course.find({
        _id: { $in: activationCode.grants.courseIds }
      }).select('title thumbnail pricing.price')

      grantDetails = {
        courses: courses.map(c => ({
          _id: c._id,
          title: c.title,
          thumbnail: c.thumbnail,
          originalPrice: c.pricing.price
        })),
        totalValue: courses.reduce((sum, c) => sum + c.pricing.price, 0)
      }
    }

    return {
      valid: true,
      code: activationCode.code,
      type: activationCode.type,
      grants: grantDetails,
      restrictions: {
        validUntil: activationCode.configuration.validUntil,
        remainingUses: activationCode.configuration.maxUses - activationCode.currentUses
      }
    }
  }

  static async redeemCode(code: string, userId: string): Promise<RedemptionResult> {
    const session = await mongoose.startSession()

    try {
      session.startTransaction()

      // Validate code again within transaction
      const validation = await this.validateCode(code, userId)
      if (!validation.valid) {
        throw new Error(`Mã kích hoạt không hợp lệ: ${validation.reason}`)
      }

      const activationCode = await ActivationCode.findOne({ code }).session(session)
      if (!activationCode) {
        throw new Error('Mã kích hoạt không tồn tại')
      }

      // Process redemption based on type
      let grantedItems = {}

      if (activationCode.type === 'course') {
        grantedItems = await this.grantCourseAccess(
          userId,
          activationCode.grants.courseIds,
          activationCode.grants.accessDuration,
          session
        )
      } else if (activationCode.type === 'subscription') {
        grantedItems = await this.grantSubscriptionAccess(
          userId,
          activationCode.grants.subscriptionPlan,
          activationCode.grants.subscriptionDuration,
          session
        )
      }

      // Update activation code usage
      await ActivationCode.findByIdAndUpdate(
        activationCode._id,
        {
          $inc: { currentUses: 1 },
          $push: {
            usage: {
              userId,
              usedAt: new Date()
            }
          },
          $set: {
            status: activationCode.currentUses + 1 >= activationCode.configuration.maxUses ? 'used' : 'active'
          }
        },
        { session }
      )

      await session.commitTransaction()

      Logger.info('Activation code redeemed', {
        userId,
        action: 'redeem_activation_code',
        metadata: {
          code: activationCode.code,
          type: activationCode.type,
          batchId: activationCode.configuration.batchId
        }
      })

      return {
        redemption: {
          _id: uuidv4(),
          code: activationCode.code,
          redeemedAt: new Date()
        },
        granted: grantedItems
      }

    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      session.endSession()
    }
  }

  private static generateUniqueCode(prefix: string = '', length: number = 12): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = prefix

    for (let i = result.length; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }

    return result
  }

  private static async grantCourseAccess(
    userId: string,
    courseIds: ObjectId[],
    accessDuration: number,
    session: any
  ) {
    const enrollments = []
    const expiresAt = accessDuration ?
      new Date(Date.now() + accessDuration * 24 * 60 * 60 * 1000) :
      null

    for (const courseId of courseIds) {
      const enrollment = new Enrollment({
        userId,
        courseId,
        enrollmentInfo: {
          enrolledAt: new Date(),
          source: 'activation_code',
          expiresAt
        },
        progress: {
          status: 'enrolled',
          completionPercentage: 0
        }
      })

      await enrollment.save({ session })
      enrollments.push(enrollment)

      // Update course stats
      await Course.findByIdAndUpdate(
        courseId,
        { $inc: { 'stats.totalEnrollments': 1 } },
        { session }
      )
    }

    return { courses: enrollments }
  }
}
```

#### Step 2: Payment Service
```typescript
// lib/payment/stripeService.ts
export class StripeService {
  private stripe: Stripe

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16'
    })
  }

  async createPaymentIntent(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // Calculate total amount
      const subtotal = request.items.reduce((sum, item) => sum + item.price, 0)
      const tax = Math.round(subtotal * 0.1) // 10% VAT
      const total = subtotal + tax

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: total,
        currency: request.currency || 'vnd',
        metadata: {
          userId: request.userId,
          items: JSON.stringify(request.items)
        },
        automatic_payment_methods: {
          enabled: true
        }
      })

      // Create payment record
      const payment = new Payment({
        userId: request.userId,
        transactionId: paymentIntent.id,
        paymentMethod: 'stripe',
        amount: {
          subtotal,
          tax,
          total
        },
        items: request.items,
        status: 'pending',
        gatewayData: {
          paymentIntentId: paymentIntent.id,
          clientSecret: paymentIntent.client_secret
        }
      })

      await payment.save()

      Logger.info('Payment intent created', {
        userId: request.userId,
        action: 'payment_intent_created',
        metadata: {
          paymentIntentId: paymentIntent.id,
          amount: total,
          currency: request.currency
        }
      })

      return {
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret!,
        status: paymentIntent.status,
        amount: total
      }
    } catch (error) {
      Logger.error('Payment intent creation failed', error)
      throw new Error('Không thể tạo payment intent')
    }
  }

  async handleWebhook(body: string, signature: string): Promise<void> {
    try {
      const event = this.stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSuccess(event.data.object as Stripe.PaymentIntent)
          break

        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
          break

        default:
          Logger.warn(`Unhandled webhook event: ${event.type}`)
      }
    } catch (error) {
      Logger.error('Webhook handling failed', error)
      throw error
    }
  }

  private async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    const session = await mongoose.startSession()

    try {
      session.startTransaction()

      // Update payment status
      const payment = await Payment.findOneAndUpdate(
        { transactionId: paymentIntent.id },
        {
          status: 'completed',
          'gatewayData.completedAt': new Date()
        },
        { session, new: true }
      )

      if (!payment) {
        throw new Error(`Payment not found: ${paymentIntent.id}`)
      }

      // Process enrollments
      for (const item of payment.items) {
        if (item.type === 'course') {
          await CourseService.enrollUserInCourse(
            payment.userId.toString(),
            item.itemId.toString()
          )
        }
      }

      await session.commitTransaction()

      // Send notifications
      const wsService = getWebSocketService()
      wsService.sendNotificationToUser(payment.userId.toString(), {
        type: 'payment_success',
        title: 'Thanh toán thành công',
        message: 'Bạn đã được cấp quyền truy cập khóa học',
        data: {
          paymentId: payment._id,
          items: payment.items
        }
      })

      Logger.info('Payment processed successfully', {
        userId: payment.userId.toString(),
        action: 'payment_success',
        metadata: {
          paymentIntentId: paymentIntent.id,
          amount: payment.amount.total
        }
      })

    } catch (error) {
      await session.abortTransaction()
      Logger.error('Payment processing failed', error)
    } finally {
      session.endSession()
    }
  }
}
```

### Testing Requirements
```typescript
// __tests__/activationCodes/activationCodeService.test.ts
describe('ActivationCodeService', () => {
  describe('generateCodes', () => {
    it('should generate activation codes successfully', async () => {
      const request = {
        type: 'course',
        quantity: 10,
        grants: {
          courseIds: ['course_id_1'],
          accessDuration: 365
        },
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        generatedBy: 'admin_id'
      }

      const result = await ActivationCodeService.generateCodes(request)

      expect(result.totalGenerated).toBe(10)
      expect(result.codes).toHaveLength(10)
      expect(result.batchId).toBeTruthy()
    })
  })

  describe('validateCode', () => {
    it('should validate active code successfully', async () => {
      const code = await ActivationCode.create({
        code: 'TEST123',
        type: 'course',
        grants: { courseIds: ['course_id'] },
        configuration: {
          maxUses: 1,
          currentUses: 0,
          validFrom: new Date(Date.now() - 1000),
          validUntil: new Date(Date.now() + 1000000)
        },
        status: 'active'
      })

      const result = await ActivationCodeService.validateCode('TEST123')

      expect(result.valid).toBe(true)
      expect(result.code).toBe('TEST123')
    })

    it('should reject expired code', async () => {
      await ActivationCode.create({
        code: 'EXPIRED123',
        type: 'course',
        configuration: {
          validFrom: new Date(Date.now() - 2000),
          validUntil: new Date(Date.now() - 1000)
        },
        status: 'active'
      })

      const result = await ActivationCodeService.validateCode('EXPIRED123')

      expect(result.valid).toBe(false)
      expect(result.reason).toBe('CODE_EXPIRED')
    })
  })
})
```

### Acceptance Criteria
- [ ] Admin có thể generate activation codes theo batch
- [ ] Code validation working với proper error messages
- [ ] Code redemption grants course access correctly
- [ ] Payment flow working với Stripe integration
- [ ] Webhook processing enrolls users automatically
- [ ] Revenue tracking và reporting functional
- [ ] Distributor commission calculation
- [ ] All payment endpoints tested
- [ ] Error handling cho failed payments

### Timeline: 3 weeks (21 person-days)

### Dependencies
- Phase 2 completed (Course enrollment system)
- Stripe account setup và webhook configuration
- Admin interface for code management

---

## PHASE 6: ANALYTICS & ADVANCED FEATURES (Week 17-20)

### Objectives
- Implement comprehensive analytics system
- Real-time dashboard với charts và metrics
- Gamification system (points, badges, leaderboards)
- Advanced reporting capabilities
- Performance optimization và caching
- Production deployment preparation

### Database Models to Implement
```typescript
// lib/database/models/Analytics.ts
interface IAnalytics {
  event: {
    name: string
    category: 'user' | 'course' | 'assessment' | 'payment'
    action: string
    label?: string
    value?: number
  }
  user: {
    userId?: ObjectId
    sessionId: string
    role?: string
  }
  context: {
    page: string
    referrer?: string
    userAgent: string
    ipAddress: string
  }
  timestamp: Date
}

// lib/database/models/Gamification.ts
interface IGamification {
  userId: ObjectId
  points: {
    total: number
    available: number
    lifetime: number
  }
  badges: Array<{
    badgeId: string
    name: string
    earnedAt: Date
  }>
  streaks: {
    current: { days: number, startDate: Date }
    longest: { days: number, startDate: Date, endDate: Date }
  }
  leaderboards: {
    global: { rank: number, score: number }
    course: Array<{ courseId: ObjectId, rank: number, score: number }>
  }
}
```

### API Endpoints Priority
**High Priority:**
- `POST /api/analytics/events` - Track events
- `GET /api/analytics/dashboard` - Dashboard data
- `GET /api/analytics/courses/:id` - Course analytics
- `GET /api/gamification/profile` - User gamification data
- `GET /api/gamification/leaderboard` - Leaderboards
- `POST /api/gamification/award-points` - Award points

**Medium Priority:**
- `GET /api/analytics/reports/revenue` - Revenue reports
- `GET /api/analytics/reports/engagement` - Engagement reports
- `GET /api/admin/system/health` - System health metrics

### Implementation Steps

#### Step 1: Analytics Service
```typescript
// lib/analytics/analyticsService.ts
export class AnalyticsService {
  static async trackEvent(eventData: AnalyticsEvent): Promise<void> {
    try {
      await connectDB()

      const analytics = new Analytics({
        event: eventData.event,
        user: eventData.user,
        context: eventData.context,
        timestamp: new Date()
      })

      await analytics.save()

      // Also send to external analytics if configured
      if (process.env.GOOGLE_ANALYTICS_ID) {
        await this.sendToGoogleAnalytics(eventData)
      }

    } catch (error) {
      Logger.error('Analytics tracking failed', error, {
        action: 'analytics_tracking_failed',
        metadata: eventData
      })
      // Don't throw error to avoid breaking user experience
    }
  }

  static async getDashboardData(
    userId: string,
    role: string,
    period: string = '30d'
  ): Promise<DashboardData> {
    await connectDB()

    const endDate = new Date()
    const startDate = new Date()

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    if (role === 'admin' || role === 'super_admin') {
      return this.getAdminDashboard(startDate, endDate)
    } else if (role === 'teacher') {
      return this.getTeacherDashboard(userId, startDate, endDate)
    } else {
      return this.getStudentDashboard(userId, startDate, endDate)
    }
  }

  private static async getAdminDashboard(startDate: Date, endDate: Date): Promise<AdminDashboardData> {
    const [
      userStats,
      courseStats,
      revenueStats,
      engagementStats
    ] = await Promise.all([
      this.getUserStats(startDate, endDate),
      this.getCourseStats(startDate, endDate),
      this.getRevenueStats(startDate, endDate),
      this.getEngagementStats(startDate, endDate)
    ])

    return {
      overview: {
        totalUsers: userStats.total,
        activeUsers: userStats.active,
        totalCourses: courseStats.total,
        totalRevenue: revenueStats.total,
        conversionRate: this.calculateConversionRate(userStats, revenueStats)
      },
      trends: {
        users: userStats.trends,
        revenue: revenueStats.trends,
        engagement: engagementStats.trends
      },
      topCourses: courseStats.topCourses,
      userSegments: userStats.segments
    }
  }

  private static async getUserStats(startDate: Date, endDate: Date) {
    const totalUsers = await User.countDocuments()
    const newUsers = await User.countDocuments({
      createdAt: { $gte: startDate, $lte: endDate }
    })

    const activeUsers = await Analytics.distinct('user.userId', {
      timestamp: { $gte: startDate, $lte: endDate },
      'user.userId': { $exists: true }
    }).then(userIds => userIds.length)

    // Daily trends
    const trends = await Analytics.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
          'event.category': 'user',
          'event.action': 'login'
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' }
          },
          activeUsers: { $addToSet: '$user.userId' }
        }
      },
      {
        $project: {
          date: '$_id',
          activeUsers: { $size: '$activeUsers' }
        }
      },
      { $sort: { date: 1 } }
    ])

    return {
      total: totalUsers,
      new: newUsers,
      active: activeUsers,
      trends,
      segments: await this.getUserSegments()
    }
  }
}
```

#### Step 2: Gamification Service
```typescript
// lib/gamification/gamificationService.ts
export class GamificationService {
  static async awardPoints(
    userId: string,
    points: number,
    reason: string,
    sourceType: string,
    sourceId?: string
  ): Promise<void> {
    await connectDB()

    const session = await mongoose.startSession()

    try {
      session.startTransaction()

      // Update user gamification data
      const gamification = await Gamification.findOneAndUpdate(
        { userId },
        {
          $inc: {
            'points.total': points,
            'points.available': points,
            'points.lifetime': points
          },
          $push: {
            'points.history': {
              points,
              reason,
              sourceType,
              sourceId,
              earnedAt: new Date()
            }
          }
        },
        { upsert: true, new: true, session }
      )

      // Check for new badges
      const newBadges = await this.checkForNewBadges(userId, gamification, session)

      // Update streaks
      await this.updateStreaks(userId, session)

      // Update leaderboards
      await this.updateLeaderboards(userId, points, session)

      await session.commitTransaction()

      // Send notifications for new badges
      if (newBadges.length > 0) {
        const wsService = getWebSocketService()
        wsService.sendNotificationToUser(userId, {
          type: 'badge_earned',
          title: 'Huy hiệu mới!',
          message: `Bạn đã nhận được ${newBadges.length} huy hiệu mới`,
          data: { badges: newBadges }
        })
      }

      Logger.info('Points awarded', {
        userId,
        action: 'points_awarded',
        metadata: { points, reason, sourceType, sourceId }
      })

    } catch (error) {
      await session.abortTransaction()
      Logger.error('Points award failed', error)
      throw error
    } finally {
      session.endSession()
    }
  }

  static async getLeaderboard(
    type: 'global' | 'course',
    courseId?: string,
    limit: number = 100
  ): Promise<LeaderboardEntry[]> {
    await connectDB()

    let matchStage: any = {}
    let sortField = 'points.total'

    if (type === 'course' && courseId) {
      // Get users enrolled in specific course
      const enrollments = await Enrollment.find({ courseId }).distinct('userId')
      matchStage = { userId: { $in: enrollments } }
      sortField = `leaderboards.course.${courseId}.score`
    }

    const leaderboard = await Gamification.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          userId: 1,
          userName: {
            $concat: ['$user.profile.firstName', ' ', '$user.profile.lastName']
          },
          avatar: '$user.profile.avatar',
          totalPoints: '$points.total',
          currentStreak: '$streaks.current.days',
          badges: { $size: '$badges' }
        }
      },
      { $sort: { totalPoints: -1 } },
      { $limit: limit },
      {
        $addFields: {
          rank: { $add: [{ $indexOfArray: [[], null] }, 1] }
        }
      }
    ])

    // Add rank manually since $indexOfArray doesn't work as expected
    return leaderboard.map((entry, index) => ({
      ...entry,
      rank: index + 1
    }))
  }

  private static async checkForNewBadges(
    userId: string,
    gamification: any,
    session: any
  ): Promise<Badge[]> {
    const newBadges = []
    const currentBadgeIds = gamification.badges.map(b => b.badgeId)

    // Define badge criteria
    const badgeCriteria = [
      {
        id: 'first_course_complete',
        name: 'Người hoàn thành đầu tiên',
        check: async () => {
          const completedCourses = await Enrollment.countDocuments({
            userId,
            'progress.status': 'completed'
          }).session(session)
          return completedCourses >= 1
        }
      },
      {
        id: 'points_collector_100',
        name: 'Thợ sưu tập điểm',
        check: async () => gamification.points.total >= 100
      },
      {
        id: 'streak_master_7',
        name: 'Bậc thầy chuỗi ngày',
        check: async () => gamification.streaks.current.days >= 7
      },
      {
        id: 'assessment_ace',
        name: 'Cao thủ kiểm tra',
        check: async () => {
          const highScoreAssessments = await AssessmentResult.countDocuments({
            userId,
            'results.percentage': { $gte: 90 }
          }).session(session)
          return highScoreAssessments >= 5
        }
      }
    ]

    for (const criteria of badgeCriteria) {
      if (!currentBadgeIds.includes(criteria.id) && await criteria.check()) {
        const badge = {
          badgeId: criteria.id,
          name: criteria.name,
          earnedAt: new Date()
        }

        await Gamification.findOneAndUpdate(
          { userId },
          { $push: { badges: badge } },
          { session }
        )

        newBadges.push(badge)
      }
    }

    return newBadges
  }
}
```

### Testing Requirements
```typescript
// __tests__/analytics/analyticsService.test.ts
describe('AnalyticsService', () => {
  describe('trackEvent', () => {
    it('should track event successfully', async () => {
      const eventData = {
        event: {
          name: 'course_view',
          category: 'course',
          action: 'view',
          value: 1
        },
        user: {
          userId: 'user_id',
          sessionId: 'session_id'
        },
        context: {
          page: '/courses/course-id',
          userAgent: 'test-agent',
          ipAddress: '127.0.0.1'
        }
      }

      await AnalyticsService.trackEvent(eventData)

      const savedEvent = await Analytics.findOne({
        'event.name': 'course_view'
      })

      expect(savedEvent).toBeTruthy()
      expect(savedEvent.event.category).toBe('course')
    })
  })

  describe('getDashboardData', () => {
    it('should return admin dashboard data', async () => {
      // Seed test data
      await User.create([
        { email: '<EMAIL>', role: 'student' },
        { email: '<EMAIL>', role: 'student' }
      ])

      const result = await AnalyticsService.getDashboardData(
        'admin_id',
        'admin',
        '30d'
      )

      expect(result.overview).toHaveProperty('totalUsers')
      expect(result.overview).toHaveProperty('totalRevenue')
      expect(result.trends).toHaveProperty('users')
    })
  })
})
```

### Acceptance Criteria
- [ ] Analytics tracking working cho all major events
- [ ] Admin dashboard với comprehensive metrics
- [ ] Real-time charts và visualizations
- [ ] Gamification system fully functional
- [ ] Leaderboards updating correctly
- [ ] Badge system working
- [ ] Performance optimized (< 2s page loads)
- [ ] Caching implemented cho expensive queries
- [ ] All analytics endpoints tested
- [ ] Production deployment ready

### Timeline: 4 weeks (28 person-days)

### Dependencies
- All previous phases completed
- Chart.js hoặc similar visualization library
- Redis for caching (optional)
- Production environment setup

---

## RISK MANAGEMENT & MITIGATION

### Potential Blockers

#### 1. AI Integration Challenges
**Risk**: OpenAI API rate limits, accuracy issues
**Mitigation**:
- Implement fallback manual scoring
- Use multiple AI providers
- Optimize prompts for consistency
- Add confidence thresholds

#### 2. Performance Issues
**Risk**: Slow database queries, large file uploads
**Mitigation**:
- Database indexing strategy
- CDN for file storage
- Pagination for large datasets
- Background job processing

#### 3. Payment Integration Complexity
**Risk**: Webhook failures, payment disputes
**Mitigation**:
- Robust error handling
- Payment reconciliation system
- Manual payment verification
- Comprehensive logging

#### 4. Scalability Concerns
**Risk**: High user load, concurrent assessments
**Mitigation**:
- Horizontal scaling preparation
- Database connection pooling
- Caching strategy
- Load testing

### Alternative Approaches

#### AI Scoring Alternatives
- **Primary**: OpenAI GPT-4
- **Backup**: Google Cloud AI
- **Fallback**: Rule-based scoring + manual review

#### File Storage Alternatives
- **Primary**: AWS S3
- **Backup**: Cloudinary
- **Fallback**: Local storage với CDN

#### Payment Processing Alternatives
- **Primary**: Stripe
- **Backup**: PayPal
- **Fallback**: Bank transfer với manual verification

---

## QUALITY ASSURANCE CHECKPOINTS

### Code Review Milestones
- [ ] **Phase 1**: Authentication system review
- [ ] **Phase 2**: Course management review
- [ ] **Phase 3**: Assessment system review
- [ ] **Phase 4**: AI integration review
- [ ] **Phase 5**: Payment system review
- [ ] **Phase 6**: Analytics system review

### Testing Milestones
- [ ] **Unit Tests**: 80%+ coverage for all services
- [ ] **Integration Tests**: All API endpoints tested
- [ ] **E2E Tests**: Critical user flows tested
- [ ] **Performance Tests**: Load testing completed
- [ ] **Security Tests**: Vulnerability assessment

### Performance Benchmarks
- [ ] **Page Load**: < 2 seconds
- [ ] **API Response**: < 500ms average
- [ ] **File Upload**: < 30 seconds for 100MB
- [ ] **AI Scoring**: < 30 seconds per assessment
- [ ] **Database Queries**: < 100ms for simple queries

---

## DEPLOYMENT STRATEGY

### Environment Setup
1. **Development**: Local development với Docker
2. **Staging**: Production-like environment for testing
3. **Production**: Scalable cloud deployment

### Deployment Pipeline
1. **CI/CD**: GitHub Actions hoặc similar
2. **Testing**: Automated test suite
3. **Build**: Docker containerization
4. **Deploy**: Zero-downtime deployment
5. **Monitor**: Health checks và alerting

### Go-Live Checklist
- [ ] All phases completed và tested
- [ ] Performance benchmarks met
- [ ] Security audit completed
- [ ] Backup strategy implemented
- [ ] Monitoring và alerting setup
- [ ] Documentation completed
- [ ] Team training completed

---

## KẾT LUẬN

### Summary
Kế hoạch triển khai này cung cấp roadmap chi tiết để xây dựng hệ thống LMS hoàn chỉnh trong 16-20 weeks với 6 phases rõ ràng. Mỗi phase có objectives cụ thể, deliverables đo lường được, và acceptance criteria rõ ràng.

### Success Metrics
- **Functional**: Tất cả 200+ API endpoints working
- **Performance**: System handles 1000+ concurrent users
- **Quality**: 80%+ test coverage, < 1% error rate
- **User Experience**: Intuitive interface, < 2s load times
- **Business**: Revenue tracking, activation code system working

### Next Steps
1. **Team Assembly**: Recruit và onboard development team
2. **Environment Setup**: Provision development infrastructure
3. **Phase 0 Kickoff**: Begin project initialization
4. **Stakeholder Alignment**: Regular progress reviews
5. **Continuous Improvement**: Iterate based on feedback

**Timeline**: 16-20 weeks to production-ready system
**Team**: 3-4 developers + 1 project manager
**Budget**: Estimate based on team costs + infrastructure + third-party services
