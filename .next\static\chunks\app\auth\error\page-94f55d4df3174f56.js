(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[590],{7819:function(e,t,r){Promise.resolve().then(r.bind(r,1811))},9376:function(e,t,r){"use strict";var n=r(5475);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},1811:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var n=r(7437),i=r(9376),a=r(7648),s=r(6334),o=r(8629);function l(){let e=(0,i.useSearchParams)().get("error"),t=(e=>{switch(e){case"Configuration":return{title:"Lỗi cấu h\xecnh",message:"C\xf3 lỗi trong cấu h\xecnh hệ thống. Vui l\xf2ng li\xean hệ quản trị vi\xean.",variant:"error"};case"AccessDenied":return{title:"Truy cập bị từ chối",message:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o t\xe0i nguy\xean n\xe0y.",variant:"warning"};case"Verification":return{title:"Lỗi x\xe1c thực",message:"Li\xean kết x\xe1c thực kh\xf4ng hợp lệ hoặc đ\xe3 hết hạn. Vui l\xf2ng thử lại.",variant:"error"};case"Default":return{title:"Lỗi đăng nhập",message:"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập. Vui l\xf2ng thử lại.",variant:"error"};case"EmailNotVerified":return{title:"Email chưa được x\xe1c thực",message:"Vui l\xf2ng kiểm tra email v\xe0 nhấp v\xe0o li\xean kết x\xe1c thực trước khi đăng nhập.",variant:"warning"};case"AccountLocked":return{title:"T\xe0i khoản bị kh\xf3a",message:"T\xe0i khoản của bạn đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần. Vui l\xf2ng thử lại sau.",variant:"error"};case"OAuthSignin":return{title:"Lỗi đăng nhập OAuth",message:"Kh\xf4ng thể đăng nhập bằng t\xe0i khoản mạng x\xe3 hội. Vui l\xf2ng thử lại.",variant:"error"};case"OAuthCallback":return{title:"Lỗi OAuth Callback",message:"C\xf3 lỗi xảy ra khi xử l\xfd th\xf4ng tin từ nh\xe0 cung cấp OAuth.",variant:"error"};case"OAuthCreateAccount":return{title:"Kh\xf4ng thể tạo t\xe0i khoản",message:"Kh\xf4ng thể tạo t\xe0i khoản từ th\xf4ng tin OAuth. Email c\xf3 thể đ\xe3 được sử dụng.",variant:"error"};case"EmailCreateAccount":return{title:"Kh\xf4ng thể tạo t\xe0i khoản",message:"Kh\xf4ng thể tạo t\xe0i khoản với email n\xe0y.",variant:"error"};case"Callback":return{title:"Lỗi Callback",message:"C\xf3 lỗi xảy ra trong qu\xe1 tr\xecnh xử l\xfd callback.",variant:"error"};case"OAuthAccountNotLinked":return{title:"T\xe0i khoản chưa được li\xean kết",message:"Email n\xe0y đ\xe3 được sử dụng với phương thức đăng nhập kh\xe1c. Vui l\xf2ng đăng nhập bằng phương thức ban đầu.",variant:"warning"};case"SessionRequired":return{title:"Y\xeau cầu đăng nhập",message:"Bạn cần đăng nhập để truy cập trang n\xe0y.",variant:"info"};default:return{title:"Lỗi kh\xf4ng x\xe1c định",message:"Đ\xe3 xảy ra lỗi kh\xf4ng mong muốn. Vui l\xf2ng thử lại sau.",variant:"error"}}})(e);return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100",children:(0,n.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,n.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:t.title})]}),(0,n.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg",children:[(0,n.jsx)(o.g7,{variant:t.variant,message:t.message}),(0,n.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,n.jsx)(a.default,{href:"/auth/signin",children:(0,n.jsx)(s.z,{className:"w-full",children:"Thử đăng nhập lại"})}),(0,n.jsx)(a.default,{href:"/",children:(0,n.jsx)(s.z,{variant:"outline",className:"w-full",children:"Về trang chủ"})}),"EmailNotVerified"===e&&(0,n.jsx)(a.default,{href:"/auth/resend-verification",children:(0,n.jsx)(s.z,{variant:"secondary",className:"w-full",children:"Gửi lại email x\xe1c thực"})})]}),(0,n.jsx)("div",{className:"mt-6 text-center",children:(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Nếu vấn đề vẫn tiếp tục, vui l\xf2ng"," ",(0,n.jsx)(a.default,{href:"/contact",className:"font-medium text-primary hover:text-primary/80",children:"li\xean hệ hỗ trợ"})]})})]}),!1]})})}},8629:function(e,t,r){"use strict";r.d(t,{g7:function(){return d}});var n=r(7437),i=r(2265),a=r(535),s=r(3448);let o=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),l=i.forwardRef((e,t)=>{let{className:r,variant:i,...a}=e;return(0,n.jsx)("div",{ref:t,role:"alert",className:(0,s.cn)(o({variant:i}),r),...a})});l.displayName="Alert";let c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("h5",{ref:t,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",r),...i})});c.displayName="AlertTitle";let u=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",r),...i})});u.displayName="AlertDescription";let h={success:(0,n.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:(0,n.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:(0,n.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:(0,n.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function d(e){let{title:t,message:r,variant:i="info",onClose:a}=e;return(0,n.jsxs)(l,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[i],className:"relative",children:[h[i],(0,n.jsxs)("div",{className:"flex-1",children:[t&&(0,n.jsx)(c,{children:t}),(0,n.jsx)(u,{children:r})]}),a&&(0,n.jsx)("button",{onClick:a,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:(0,n.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},6334:function(e,t,r){"use strict";r.d(t,{z:function(){return l}});var n=r(7437),i=r(2265),a=r(535),s=r(3448);let o=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef((e,t)=>{let{className:r,variant:i,size:a,asChild:l=!1,...c}=e;return(0,n.jsx)("button",{className:(0,s.cn)(o({variant:i,size:a,className:r})),ref:t,...c})});l.displayName="Button"},3448:function(e,t,r){"use strict";r.d(t,{cn:function(){return a}});var n=r(1994),i=r(3335);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,n.W)(t))}}},function(e){e.O(0,[851,648,971,117,744],function(){return e(e.s=7819)}),_N_E=e.O()}]);