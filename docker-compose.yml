version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: webta-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: webta-lms
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - webta-network

  # Redis for caching and sessions
  redis:
    image: redis:7.2-alpine
    container_name: webta-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redispassword
    volumes:
      - redis_data:/data
    networks:
      - webta-network

  # Next.js Application (for production)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: webta-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=**************************************/webta-lms?authSource=admin
      - REDIS_URL=redis://:redispassword@redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-production-secret-key
    depends_on:
      - mongodb
      - redis
    networks:
      - webta-network
    # Uncomment for development with volume mounting
    # volumes:
    #   - .:/app
    #   - /app/node_modules

  # MongoDB Express (Database Admin UI)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: webta-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    depends_on:
      - mongodb
    networks:
      - webta-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  webta-network:
    driver: bridge
