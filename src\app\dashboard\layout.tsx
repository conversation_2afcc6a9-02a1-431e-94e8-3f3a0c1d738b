'use client'

import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'
import Link from 'next/link'
import { Loading } from '@/components/ui/Loading'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  if (!session) {
    return null
  }

  // Determine which dashboard to show based on user role
  const userRole = session.user?.role || 'student'
  
  // Redirect to appropriate dashboard if on base /dashboard route
  useEffect(() => {
    if (pathname === '/dashboard') {
      if (userRole === 'instructor' || userRole === 'admin') {
        router.push('/dashboard/instructor')
      } else {
        router.push('/dashboard/student')
      }
    }
  }, [pathname, userRole, router])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Dashboard Navigation */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-8">
              <Link href="/" className="text-xl font-bold text-primary">
                WebTA LMS
              </Link>
              
              <nav className="flex space-x-4">
                <Link
                  href="/dashboard/student"
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname === '/dashboard/student'
                      ? 'bg-primary text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Dashboard Học viên
                </Link>
                
                {(userRole === 'instructor' || userRole === 'admin') && (
                  <Link
                    href="/dashboard/instructor"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      pathname === '/dashboard/instructor'
                        ? 'bg-primary text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    Dashboard Giảng viên
                  </Link>
                )}
                
                <Link
                  href="/courses"
                  className="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900"
                >
                  Khóa học
                </Link>
              </nav>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Xin chào, {session.user?.name || session.user?.email}
              </span>
              <Link
                href="/api/auth/signout"
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Đăng xuất
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      {children}
    </div>
  )
}
