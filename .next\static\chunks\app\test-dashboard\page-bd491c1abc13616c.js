(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[506],{9939:function(e,r,s){Promise.resolve().then(s.bind(s,4108))},4108:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return o}});var t=s(7437),a=s(2265),n=s(605),d=s(6334),l=s(757),i=s(1215);function o(){let{data:e,status:r}=(0,n.useSession)(),[s,o]=(0,a.useState)(null),[c,u]=(0,a.useState)(null),[m,f]=(0,a.useState)(!1),[x,b]=(0,a.useState)(null),h=async()=>{f(!0),b(null);try{let e=await fetch("/api/dashboard/student"),r=await e.json();r.success?o(r.data):b("Student API Error: ".concat(r.error))}catch(e){b("Student API Error: ".concat(e))}finally{f(!1)}},g=async()=>{f(!0),b(null);try{let e=await fetch("/api/dashboard/instructor"),r=await e.json();r.success?u(r.data):b("Instructor API Error: ".concat(r.error))}catch(e){b("Instructor API Error: ".concat(e))}finally{f(!1)}};return"loading"===r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(i.gb,{size:"lg"})}):e?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)(l.Zb,{className:"p-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Dashboard API Test"}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Session Information"}),(0,t.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg",children:(0,t.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e,null,2)})})]}),(0,t.jsxs)("div",{className:"flex gap-4 mb-8",children:[(0,t.jsx)(d.z,{onClick:h,disabled:m,children:m?(0,t.jsx)(i.gb,{size:"sm"}):"Test Student API"}),(0,t.jsx)(d.z,{onClick:g,disabled:m,variant:"outline",children:m?(0,t.jsx)(i.gb,{size:"sm"}):"Test Instructor API"})]}),x&&(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-red-600 mb-2",children:"Error:"}),(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 p-4 rounded-lg",children:(0,t.jsx)("p",{className:"text-red-800",children:x})})]}),s&&(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Student Dashboard Data:"}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 p-4 rounded-lg",children:(0,t.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(s,null,2)})})]}),c&&(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Instructor Dashboard Data:"}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 p-4 rounded-lg",children:(0,t.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(c,null,2)})})]}),(0,t.jsxs)("div",{className:"mt-8 pt-8 border-t",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Navigation Test:"}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("a",{href:"/dashboard/student",className:"text-blue-600 hover:underline",children:"Go to Student Dashboard"}),(0,t.jsx)("a",{href:"/dashboard/instructor",className:"text-blue-600 hover:underline",children:"Go to Instructor Dashboard"}),(0,t.jsx)("a",{href:"/dashboard",className:"text-blue-600 hover:underline",children:"Go to Dashboard (Auto-redirect)"})]})]})]})})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)(l.Zb,{className:"p-8",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Dashboard Test"}),(0,t.jsx)("p",{className:"text-red-600",children:"Bạn cần đăng nhập để test dashboard"})]})})}},6334:function(e,r,s){"use strict";s.d(r,{z:function(){return i}});var t=s(7437),a=s(2265),n=s(535),d=s(3448);let l=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,r)=>{let{className:s,variant:a,size:n,asChild:i=!1,...o}=e;return(0,t.jsx)("button",{className:(0,d.cn)(l({variant:a,size:n,className:s})),ref:r,...o})});i.displayName="Button"},757:function(e,r,s){"use strict";s.d(r,{Zb:function(){return i}});var t=s(7437),a=s(2265),n=s(535),d=s(3448);let l=(0,n.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),i=a.forwardRef((e,r)=>{let{className:s,variant:a,size:n,...i}=e;return(0,t.jsx)("div",{ref:r,className:(0,d.cn)(l({variant:a,size:n}),s),...i})});i.displayName="Card",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",s),...a})}).displayName="CardHeader",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})}).displayName="CardTitle",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,d.cn)("text-sm text-muted-foreground",s),...a})}).displayName="CardDescription",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,d.cn)("p-6 pt-0",s),...a})}).displayName="CardContent",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,d.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},1215:function(e,r,s){"use strict";s.d(r,{gb:function(){return l}});var t=s(7437),a=s(535),n=s(3448);let d=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function l(e){let{variant:r,size:s,className:a,text:l}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex items-center justify-center",a),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)("div",{className:(0,n.cn)(d({variant:r,size:s}))}),l&&(0,t.jsx)("p",{className:"text-sm text-gray-600",children:l})]})})}},3448:function(e,r,s){"use strict";s.d(r,{cn:function(){return n}});var t=s(1994),a=s(3335);function n(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.m6)((0,t.W)(r))}}},function(e){e.O(0,[851,605,971,117,744],function(){return e(e.s=9939)}),_N_E=e.O()}]);