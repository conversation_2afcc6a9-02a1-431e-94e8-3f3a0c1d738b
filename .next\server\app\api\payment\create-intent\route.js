"use strict";(()=>{var e={};e.id=223,e.ids=[223],e.modules={38013:e=>{e.exports=require("mongodb")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},99933:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>S,requestAsyncStorage:()=>k,routeModule:()=>E,serverHooks:()=>N,staticGenerationAsyncStorage:()=>b});var i={};r.r(i),r.d(i,{POST:()=>x});var s=r(49303),n=r(88716),a=r(60670),o=r(87070),u=r(75571),l=r(95456),c=r(46029),p=r(14184),d=r(89332),m=r(93330),h=r(60321),g=r(9133),f=r(11185),y=r.n(f);let w=g.z.object({courseId:g.z.string().min(1,"Course ID l\xe0 bắt buộc"),currency:g.z.string().optional().default("VND")});async function x(e){try{await (0,p.ZP)();let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id)return o.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});let i=await e.json(),{courseId:s,currency:n}=w.parse(i);if(!y().Types.ObjectId.isValid(s))return o.NextResponse.json({success:!1,error:"Course ID kh\xf4ng hợp lệ"},{status:400});let a=await d.ZP.findById(s);if(!a)return o.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy kh\xf3a học"},{status:404});if("published"!==a.status)return o.NextResponse.json({success:!1,error:"Kh\xf3a học chưa được xuất bản"},{status:400});if(a.pricing.basePrice<=0)return o.NextResponse.json({success:!1,error:"Kh\xf3a học n\xe0y miễn ph\xed, kh\xf4ng cần thanh to\xe1n"},{status:400});let{default:g}=await r.e(544).then(r.bind(r,66820)),f=await g.findOne({userId:t.user.id,courseId:s});if(f&&"active"===f.status)return o.NextResponse.json({success:!1,error:"Bạn đ\xe3 đăng k\xfd kh\xf3a học n\xe0y rồi"},{status:400});let x=await m.ZP.findById(t.user.id);if(!x)return o.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy th\xf4ng tin người d\xf9ng"},{status:404});let E=(0,c.BQ)(a.pricing.basePrice,n),k=new h.ZP({userId:t.user.id,courseId:s,amount:a.pricing.basePrice,originalAmount:a.pricing.basePrice,currency:n.toUpperCase(),status:h.bG.PENDING,method:h.XL.STRIPE,type:h.uG.COURSE_PURCHASE,metadata:{userAgent:e.headers.get("user-agent")||"",ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",courseName:a.title,userEmail:x.email}});await k.save();let b=await (0,c.Gl)({amount:E,currency:n.toLowerCase(),courseId:s,userId:t.user.id,metadata:{paymentId:k._id.toString(),courseName:a.title,userEmail:x.email,userName:`${x.profile.firstName} ${x.profile.lastName}`}});return k.externalId=b.id,k.details.stripePaymentIntentId=b.id,await k.save(),o.NextResponse.json({success:!0,data:{clientSecret:b.client_secret,paymentIntentId:b.id,paymentId:k._id,amount:a.pricing.basePrice,currency:n.toUpperCase(),course:{id:a._id,title:a.title,thumbnail:a.thumbnail}},message:"Payment intent đ\xe3 được tạo th\xe0nh c\xf4ng"})}catch(e){if(console.error("Create payment intent error:",e),e instanceof g.z.ZodError)return o.NextResponse.json({success:!1,error:"Dữ liệu kh\xf4ng hợp lệ",details:e.errors},{status:400});if(e.type&&e.type.startsWith("Stripe")){let t=(0,c.ks)(e);return o.NextResponse.json({success:!1,error:t.message,type:t.type},{status:400})}return o.NextResponse.json({success:!1,error:"Lỗi server khi tạo payment intent"},{status:500})}}let E=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payment/create-intent/route",pathname:"/api/payment/create-intent",filename:"route",bundlePath:"app/api/payment/create-intent/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\payment\\create-intent\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:k,staticGenerationAsyncStorage:b,serverHooks:N}=E,v="/api/payment/create-intent/route";function S(){return(0,a.patchFetch)({serverHooks:N,staticGenerationAsyncStorage:b})}},95456:(e,t,r)=>{r.d(t,{L:()=>c});var i=r(53797),s=r(77234),n=r(41017),a=r(38013),o=r(14184),u=r(93330);let l=new a.MongoClient(process.env.MONGODB_URI).connect(),c={adapter:(0,n.dJ)(l),secret:process.env.NEXTAUTH_SECRET,providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email v\xe0 mật khẩu l\xe0 bắt buộc");try{await (0,o.ZP)();let t=await u.ZP.findOne({email:e.email.toLowerCase()}).select("+password");if(!t)throw Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");if(t.isLocked())throw Error("T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần");if(t.status!==u.J0.ACTIVE)throw Error("T\xe0i khoản chưa được k\xedch hoạt");if(!await t.comparePassword(e.password))throw await t.incrementLoginAttempts(),Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");return t.loginAttempts>0&&await t.updateOne({$unset:{loginAttempts:1,lockUntil:1}}),await t.updateOne({lastLogin:new Date}),{id:t._id.toString(),email:t.email,name:t.profile.fullName,role:t.role,status:t.status,emailVerified:t.emailVerified,image:t.profile.avatar}}catch(e){throw Error(e.message||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập")}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:e,user:t,account:r})=>(r&&t&&(e.role=t.role,e.status=t.status,e.emailVerified=t.emailVerified),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.status=t.status,e.user.emailVerified=t.emailVerified),e),signIn:async({user:e,account:t,profile:r})=>t?.provider!=="credentials"||!0===e.emailVerified,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:i}){console.log(`User ${e.email} signed in with ${t?.provider}`)},async signOut({session:e,token:t}){console.log("User signed out")},async createUser({user:e}){console.log(`New user created: ${e.email}`)}},debug:!1}},93330:(e,t,r)=>{r.d(t,{J0:()=>s,ZP:()=>p,i4:()=>i});var i,s,n,a=r(11185),o=r.n(a),u=r(42023),l=r.n(u);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(i||(i={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(s||(s={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(n||(n={}));let c=new a.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(i),default:"student"},status:{type:String,enum:Object.values(s),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(n)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});c.index({"profile.firstName":1,"profile.lastName":1}),c.index({role:1,status:1}),c.index({createdAt:-1}),c.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),c.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),c.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await l().genSalt(12);this.password=await l().hash(this.password,t),e()}catch(t){e(t)}}),c.methods.comparePassword=async function(e){return l().compare(e,this.password)},c.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},c.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},c.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},c.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let p=o().models.User||o().model("User",c)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[276,242,70,799,133,472,791],()=>r(99933));module.exports=i})();