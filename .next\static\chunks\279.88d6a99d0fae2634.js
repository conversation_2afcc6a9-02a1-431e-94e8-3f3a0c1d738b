(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[279],{257:function(e,t,n){"use strict";var r,i;e.exports=(null==(r=n.g.process)?void 0:r.env)&&"object"==typeof(null==(i=n.g.process)?void 0:i.env)?n.g.process:n(4227)},4601:function(){},4227:function(e){!function(){var t={229:function(e){var t,n,r,i=e.exports={};function s(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}function c(e){if(t===setTimeout)return setTimeout(e,0);if((t===s||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:s}catch(e){t=s}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(e){n=o}}();var a=[],u=!1,l=-1;function d(){u&&r&&(u=!1,r.length?a=r.concat(a):l=-1,a.length&&h())}function h(){if(!u){var e=c(d);u=!0;for(var t=a.length;t;){for(r=a,a=[];++l<t;)r&&r[l].run();l=-1,t=a.length}r=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];a.push(new f(e,t)),1!==a.length||u||c(h)},f.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},n={};function r(e){var i=n[e];if(void 0!==i)return i.exports;var s=n[e]={exports:{}},o=!0;try{t[e](s,s.exports,r),o=!1}finally{o&&delete n[e]}return s.exports}r.ab="//";var i=r(229);e.exports=i}()},8975:function(e,t,n){"use strict";var r=n(257);n(4601);var i=n(2265),s=i&&"object"==typeof i&&"default"in i?i:{default:i},o=void 0!==r&&r.env&&!0,c=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,i=t.optimizeForSpeed,s=void 0===i?o:i;u(c(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",u("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(u(c(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];u(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,n){t&&u(c(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return n?i.insertBefore(r,n):i.appendChild(r),r},function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var l=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},d={};function h(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return d[r]||(d[r]="jsx-"+l(e+"-"+n)),d[r]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return d[n]||(d[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[n]}var m=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=r||new a({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),r&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,i=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=s,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var i=h(r,n);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=i.createContext(null);p.displayName="StyleSheetContext";var v=s.default.useInsertionEffect||s.default.useLayoutEffect,y="undefined"!=typeof window?new m:void 0;function g(e){var t=y||i.useContext(p);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}g.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=g},29:function(e,t,n){"use strict";e.exports=n(8975).style},9279:function(e,t,n){"use strict";n.r(t),n.d(t,{RichTextEditor:function(){return u}});var r=n(7437),i=n(29),s=n.n(i),o=n(2265),c=n(3448),a=n(6334);function u(e){var t,n;let{value:i="",onChange:u,placeholder:l="Nhập nội dung...",className:d,disabled:h=!1,minHeight:f=200,maxHeight:m=500}=e,[p,v]=(0,o.useState)(i),[y,g]=(0,o.useState)(!1),_=(0,o.useRef)(null);(0,o.useEffect)(()=>{i!==p&&(v(i),_.current&&(_.current.innerHTML=i))},[i]);let x=()=>{if(_.current){let e=_.current.innerHTML;v(e),null==u||u(e)}},S=(e,t)=>{var n;document.execCommand(e,!1,t),null===(n=_.current)||void 0===n||n.focus(),x()},b=e=>document.queryCommandState(e);return(0,r.jsxs)("div",{className:"jsx-8c916196c427eda3 "+((0,c.cn)("border rounded-lg overflow-hidden",d)||""),children:[(0,r.jsxs)("div",{className:"jsx-8c916196c427eda3 border-b bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,r.jsx)("div",{className:"jsx-8c916196c427eda3 flex gap-1 border-r pr-2 mr-2",children:[{command:"bold",icon:"\uD835\uDC01",title:"Đậm (Ctrl+B)",shortcut:"Ctrl+B"},{command:"italic",icon:"\uD835\uDC3C",title:"Nghi\xeang (Ctrl+I)",shortcut:"Ctrl+I"},{command:"underline",icon:"\uD835\uDC14",title:"Gạch ch\xe2n (Ctrl+U)",shortcut:"Ctrl+U"},{command:"strikeThrough",icon:"\uD835\uDC12",title:"Gạch ngang",shortcut:""}].map(e=>(0,r.jsx)(a.z,{variant:b(e.command)?"default":"ghost",size:"sm",onClick:()=>S(e.command),disabled:h,title:e.title,className:"w-8 h-8 p-0 text-sm font-bold",children:e.icon},e.command))}),(0,r.jsx)("div",{className:"jsx-8c916196c427eda3 flex gap-1 border-r pr-2 mr-2",children:[{command:"justifyLeft",icon:"⬅",title:"Căn tr\xe1i"},{command:"justifyCenter",icon:"⬌",title:"Căn giữa"},{command:"justifyRight",icon:"➡",title:"Căn phải"},{command:"justifyFull",icon:"⬌",title:"Căn đều"}].map(e=>(0,r.jsx)(a.z,{variant:b(e.command)?"default":"ghost",size:"sm",onClick:()=>S(e.command),disabled:h,title:e.title,className:"w-8 h-8 p-0 text-sm",children:e.icon},e.command))}),(0,r.jsx)("div",{className:"jsx-8c916196c427eda3 flex gap-1 border-r pr-2 mr-2",children:[{command:"insertUnorderedList",icon:"•",title:"Danh s\xe1ch kh\xf4ng thứ tự"},{command:"insertOrderedList",icon:"1.",title:"Danh s\xe1ch c\xf3 thứ tự"}].map(e=>(0,r.jsx)(a.z,{variant:b(e.command)?"default":"ghost",size:"sm",onClick:()=>S(e.command),disabled:h,title:e.title,className:"w-8 h-8 p-0 text-sm",children:e.icon},e.command))}),(0,r.jsxs)("div",{className:"jsx-8c916196c427eda3 flex gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)(a.z,{variant:"ghost",size:"sm",onClick:()=>{let e=prompt("Nhập URL:");e&&S("createLink",e)},disabled:h,title:"Ch\xe8n li\xean kết",className:"w-8 h-8 p-0 text-sm",children:"\uD83D\uDD17"}),(0,r.jsx)(a.z,{variant:"ghost",size:"sm",onClick:()=>{let e=prompt("Nhập URL h\xecnh ảnh:");e&&S("insertImage",e)},disabled:h,title:"Ch\xe8n h\xecnh ảnh",className:"w-8 h-8 p-0 text-sm",children:"\uD83D\uDDBC️"})]}),(0,r.jsx)(a.z,{variant:"ghost",size:"sm",onClick:()=>{S("removeFormat")},disabled:h,title:"X\xf3a định dạng",className:"w-8 h-8 p-0 text-sm",children:"\uD83E\uDDF9"})]}),(0,r.jsx)("div",{ref:_,contentEditable:!h,onInput:x,onFocus:()=>{g(!0)},onBlur:()=>{g(!1)},style:{minHeight:"".concat(f,"px"),maxHeight:"".concat(m,"px")},dangerouslySetInnerHTML:{__html:p},"data-placeholder":l,className:"jsx-8c916196c427eda3 "+((0,c.cn)("p-4 outline-none overflow-y-auto","prose prose-sm max-w-none","focus:ring-2 focus:ring-primary focus:ring-inset",h&&"bg-gray-50 cursor-not-allowed",y&&"ring-2 ring-primary ring-inset")||"")}),(0,r.jsxs)("div",{className:"jsx-8c916196c427eda3 border-t bg-gray-50 px-4 py-2 text-xs text-gray-500 flex justify-between",children:[(0,r.jsxs)("span",{className:"jsx-8c916196c427eda3",children:[(null===(n=_.current)||void 0===n?void 0:null===(t=n.textContent)||void 0===t?void 0:t.length)||0," k\xfd tự"]}),(0,r.jsx)("span",{className:"jsx-8c916196c427eda3 text-gray-400",children:"Hỗ trợ HTML formatting"})]}),(0,r.jsx)(s(),{id:"8c916196c427eda3",children:'[contenteditable].jsx-8c916196c427eda3:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}[contenteditable].jsx-8c916196c427eda3 img.jsx-8c916196c427eda3{max-width:100%;height:auto;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}[contenteditable].jsx-8c916196c427eda3 a.jsx-8c916196c427eda3{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-8c916196c427eda3 ul.jsx-8c916196c427eda3,[contenteditable].jsx-8c916196c427eda3 ol.jsx-8c916196c427eda3{padding-left:1.5rem}[contenteditable].jsx-8c916196c427eda3 blockquote.jsx-8c916196c427eda3{border-left:4px solid#e5e7eb;padding-left:1rem;margin:1rem 0;font-style:italic;color:#6b7280}[contenteditable].jsx-8c916196c427eda3 code.jsx-8c916196c427eda3{background-color:#f3f4f6;padding:.125rem .25rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:"Courier New",monospace;font-size:.875em}[contenteditable].jsx-8c916196c427eda3 pre.jsx-8c916196c427eda3{background-color:#f3f4f6;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;overflow-x:auto;font-family:"Courier New",monospace}'})]})}t.default=u}}]);