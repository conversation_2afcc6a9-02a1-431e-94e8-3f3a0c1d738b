(()=>{var e={};e.id=65,e.ids=[65],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},45567:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d}),t(17993),t(39285),t(35866);var a=t(23191),r=t(88716),l=t(37922),n=t.n(l),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,17993)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\courses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\courses\\page.tsx"],x="/courses/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/courses/page",pathname:"/courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16104:(e,s,t)=>{Promise.resolve().then(t.bind(t,16347))},16347:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(10326),r=t(17577),l=t(35047),n=t(90434),i=t(46226),c=t(99837),d=t(89175),o=t(47375),x=t(36792),h=t(8555),m=t(17334);function u(){(0,l.useRouter)();let e=(0,l.useSearchParams)(),[s,t]=(0,r.useState)([]),[u,g]=(0,r.useState)(!0),[p,v]=(0,r.useState)(null),[j,b]=(0,r.useState)(null),[f,N]=(0,r.useState)(e.get("search")||""),[y,D]=(0,r.useState)(e.get("category")||""),[k,P]=(0,r.useState)(e.get("level")||""),[w,C]=(0,r.useState)(e.get("language")||""),[T,S]=(0,r.useState)(e.get("sortBy")||"createdAt"),[A,_]=(0,r.useState)(e.get("sortOrder")||"desc"),[M,I]=(0,r.useState)(parseInt(e.get("page")||"1")),q=async()=>{try{g(!0),v(null);let e=new URLSearchParams({page:M.toString(),limit:"12",sortBy:T,sortOrder:A});f&&e.append("search",f),y&&e.append("category",y),k&&e.append("level",k),w&&e.append("language",w);let s=await fetch(`/api/courses?${e}`),a=await s.json();a.success?(t(a.data.courses),b(a.data.pagination)):v(a.error||"Lỗi khi tải danh s\xe1ch kh\xf3a học")}catch(e){v("Lỗi kết nối server")}finally{g(!1)}},z=()=>{I(1),q()},R=(e,s)=>0===e?"Miễn ph\xed":new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"===s?"VND":"USD"}).format(e),G=(e,s)=>0===s?"Chưa c\xf3 đ\xe1nh gi\xe1":`${e.toFixed(1)} ⭐ (${s} đ\xe1nh gi\xe1)`;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Kh\xf3a học"}),a.jsx("p",{className:"mt-2 text-gray-600",children:"Kh\xe1m ph\xe1 c\xe1c kh\xf3a học ngoại ngữ chất lượng cao"})]})}),a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[a.jsx("div",{className:"lg:w-64 flex-shrink-0",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 sticky top-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Bộ lọc"}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),I(1),q()},className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xecm kiếm"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(d.I,{type:"text",placeholder:"T\xecm kh\xf3a học...",value:f,onChange:e=>N(e.target.value)}),a.jsx(c.z,{type:"submit",size:"sm",children:"T\xecm"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Danh mục"}),(0,a.jsxs)("select",{value:y,onChange:e=>{D(e.target.value),z()},className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[a.jsx("option",{value:"",children:"Tất cả danh mục"}),a.jsx("option",{value:"english",children:"Tiếng Anh"}),a.jsx("option",{value:"chinese",children:"Tiếng Trung"}),a.jsx("option",{value:"japanese",children:"Tiếng Nhật"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tr\xecnh độ"}),(0,a.jsxs)("select",{value:k,onChange:e=>{P(e.target.value),z()},className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[a.jsx("option",{value:"",children:"Tất cả tr\xecnh độ"}),a.jsx("option",{value:"beginner",children:"Cơ bản"}),a.jsx("option",{value:"intermediate",children:"Trung cấp"}),a.jsx("option",{value:"advanced",children:"N\xe2ng cao"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xf4n ngữ"}),(0,a.jsxs)("select",{value:w,onChange:e=>{C(e.target.value),z()},className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[a.jsx("option",{value:"",children:"Tất cả ng\xf4n ngữ"}),a.jsx("option",{value:"english",children:"Tiếng Anh"}),a.jsx("option",{value:"chinese",children:"Tiếng Trung"}),a.jsx("option",{value:"japanese",children:"Tiếng Nhật"})]})]})]})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("div",{className:"text-gray-600",children:j&&(0,a.jsxs)("span",{children:["Hiển thị ",(j.currentPage-1)*j.limit+1,"-",Math.min(j.currentPage*j.limit,j.totalCount),"trong tổng số ",j.totalCount," kh\xf3a học"]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("label",{className:"text-sm text-gray-700",children:"Sắp xếp:"}),(0,a.jsxs)("select",{value:`${T}-${A}`,onChange:e=>{let[s,t]=e.target.value.split("-");S(s),_(t)},className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[a.jsx("option",{value:"createdAt-desc",children:"Mới nhất"}),a.jsx("option",{value:"createdAt-asc",children:"Cũ nhất"}),a.jsx("option",{value:"stats.totalStudents-desc",children:"Nhiều học vi\xean nhất"}),a.jsx("option",{value:"stats.averageRating-desc",children:"Đ\xe1nh gi\xe1 cao nhất"}),a.jsx("option",{value:"pricing.basePrice-asc",children:"Gi\xe1 thấp nhất"}),a.jsx("option",{value:"pricing.basePrice-desc",children:"Gi\xe1 cao nhất"})]})]})]}),p&&a.jsx(h.g7,{variant:"error",message:p,className:"mb-6"}),u&&a.jsx(m.h0,{}),!u&&s.length>0&&a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8",children:s.map(e=>a.jsx(o.Zb,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:(0,a.jsxs)(n.default,{href:`/courses/${e.slug}`,children:[a.jsx("div",{className:"aspect-video bg-gray-200 relative",children:e.thumbnail?a.jsx(i.default,{src:e.thumbnail,alt:e.title,fill:!0,className:"object-cover"}):a.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"\uD83D\uDCDA"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[a.jsx(x.C,{variant:"secondary",size:"sm",children:e.category.name}),a.jsx(x.C,{variant:"outline",size:"sm",children:e.level})]}),a.jsx("h3",{className:"font-semibold text-lg mb-2 line-clamp-2",children:e.title}),a.jsx("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3 text-sm text-gray-500",children:[a.jsx("span",{children:"\uD83D\uDC68‍\uD83C\uDFEB"}),(0,a.jsxs)("span",{children:[e.instructor.profile.firstName," ",e.instructor.profile.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-semibold text-primary",children:R(e.pricing.basePrice,e.pricing.currency)}),a.jsx("div",{className:"text-xs text-gray-500",children:G(e.stats.averageRating,e.stats.totalRatings)})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.stats.totalStudents," học vi\xean"]})]})]})]})},e._id))}),!u&&0===s.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDA"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Kh\xf4ng t\xecm thấy kh\xf3a học n\xe0o"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Thử thay đổi bộ lọc hoặc từ kh\xf3a t\xecm kiếm"}),a.jsx(c.z,{onClick:()=>{N(""),D(""),P(""),C(""),I(1),q()},children:"X\xf3a bộ lọc"})]}),j&&j.totalPages>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center gap-2",children:[a.jsx(c.z,{variant:"outline",disabled:!j.hasPrevPage,onClick:()=>I(M-1),children:"Trước"}),(0,a.jsxs)("span",{className:"px-4 py-2 text-sm text-gray-600",children:["Trang ",j.currentPage," / ",j.totalPages]}),a.jsx(c.z,{variant:"outline",disabled:!j.hasNextPage,onClick:()=>I(M+1),children:"Sau"})]})]})]})})]})}},89175:(e,s,t)=>{"use strict";t.d(s,{I:()=>n});var a=t(10326),r=t(17577),l=t(51223);let n=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));n.displayName="Input"},17993:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\courses\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,105,226,826,631],()=>t(45567));module.exports=a})();