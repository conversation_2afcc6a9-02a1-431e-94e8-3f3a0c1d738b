'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { AlertMessage } from '@/components/ui/Alert'

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  const getErrorInfo = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return {
          title: 'Lỗi cấu hình',
          message: 'Có lỗi trong cấu hình hệ thống. Vui lòng liên hệ quản trị viên.',
          variant: 'error' as const,
        }
      case 'AccessDenied':
        return {
          title: 'T<PERSON>y cập bị từ chối',
          message: 'Bạn không có quyền truy cập vào tài nguyên này.',
          variant: 'warning' as const,
        }
      case 'Verification':
        return {
          title: 'Lỗi xác thực',
          message: '<PERSON><PERSON><PERSON> kết xác thực không hợp lệ hoặc đã hết hạn. <PERSON><PERSON> lòng thử lại.',
          variant: 'error' as const,
        }
      case 'Default':
        return {
          title: 'Lỗi đăng nhập',
          message: 'Đã xảy ra lỗi trong quá trình đăng nhập. Vui lòng thử lại.',
          variant: 'error' as const,
        }
      case 'EmailNotVerified':
        return {
          title: 'Email chưa được xác thực',
          message: 'Vui lòng kiểm tra email và nhấp vào liên kết xác thực trước khi đăng nhập.',
          variant: 'warning' as const,
        }
      case 'AccountLocked':
        return {
          title: 'Tài khoản bị khóa',
          message: 'Tài khoản của bạn đã bị khóa do đăng nhập sai quá nhiều lần. Vui lòng thử lại sau.',
          variant: 'error' as const,
        }
      case 'OAuthSignin':
        return {
          title: 'Lỗi đăng nhập OAuth',
          message: 'Không thể đăng nhập bằng tài khoản mạng xã hội. Vui lòng thử lại.',
          variant: 'error' as const,
        }
      case 'OAuthCallback':
        return {
          title: 'Lỗi OAuth Callback',
          message: 'Có lỗi xảy ra khi xử lý thông tin từ nhà cung cấp OAuth.',
          variant: 'error' as const,
        }
      case 'OAuthCreateAccount':
        return {
          title: 'Không thể tạo tài khoản',
          message: 'Không thể tạo tài khoản từ thông tin OAuth. Email có thể đã được sử dụng.',
          variant: 'error' as const,
        }
      case 'EmailCreateAccount':
        return {
          title: 'Không thể tạo tài khoản',
          message: 'Không thể tạo tài khoản với email này.',
          variant: 'error' as const,
        }
      case 'Callback':
        return {
          title: 'Lỗi Callback',
          message: 'Có lỗi xảy ra trong quá trình xử lý callback.',
          variant: 'error' as const,
        }
      case 'OAuthAccountNotLinked':
        return {
          title: 'Tài khoản chưa được liên kết',
          message: 'Email này đã được sử dụng với phương thức đăng nhập khác. Vui lòng đăng nhập bằng phương thức ban đầu.',
          variant: 'warning' as const,
        }
      case 'SessionRequired':
        return {
          title: 'Yêu cầu đăng nhập',
          message: 'Bạn cần đăng nhập để truy cập trang này.',
          variant: 'info' as const,
        }
      default:
        return {
          title: 'Lỗi không xác định',
          message: 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại sau.',
          variant: 'error' as const,
        }
    }
  }

  const errorInfo = getErrorInfo(error)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {errorInfo.title}
          </h2>
        </div>

        <div className="bg-white py-8 px-6 shadow rounded-lg">
          <AlertMessage
            variant={errorInfo.variant}
            message={errorInfo.message}
          />

          <div className="mt-6 space-y-4">
            <Link href="/auth/signin">
              <Button className="w-full">
                Thử đăng nhập lại
              </Button>
            </Link>

            <Link href="/">
              <Button variant="outline" className="w-full">
                Về trang chủ
              </Button>
            </Link>

            {error === 'EmailNotVerified' && (
              <Link href="/auth/resend-verification">
                <Button variant="secondary" className="w-full">
                  Gửi lại email xác thực
                </Button>
              </Link>
            )}
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Nếu vấn đề vẫn tiếp tục, vui lòng{' '}
              <Link href="/contact" className="font-medium text-primary hover:text-primary/80">
                liên hệ hỗ trợ
              </Link>
            </p>
          </div>
        </div>

        {/* Debug info for development */}
        {process.env.NODE_ENV === 'development' && error && (
          <div className="bg-gray-100 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Debug Info:</h3>
            <p className="text-xs text-gray-600">Error Code: {error}</p>
          </div>
        )}
      </div>
    </div>
  )
}
