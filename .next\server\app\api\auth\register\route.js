"use strict";(()=>{var e={};e.id=2,e.ids=[2],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},3673:(e,t,i)=>{i.r(t),i.d(t,{originalPathname:()=>k,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>d,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var n={};i.r(n),i.d(n,{POST:()=>m});var r=i(49303),a=i(88716),s=i(60670),o=i(87070),l=i(14184),u=i(93330),c=i(9133);let p=c.z.object({email:c.z.string().email("Email kh\xf4ng hợp lệ"),password:c.z.string().min(8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"),firstName:c.z.string().min(1,"T\xean l\xe0 bắt buộc").max(50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"),lastName:c.z.string().min(1,"Họ l\xe0 bắt buộc").max(50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"),role:c.z.enum(["student","instructor"]).optional().default("student")});async function m(e){try{let t=await e.json(),i=p.parse(t);if(await (0,l.ZP)(),await u.ZP.findOne({email:i.email.toLowerCase()}))return o.NextResponse.json({error:"Email đ\xe3 được sử dụng"},{status:400});let n=new u.ZP({email:i.email.toLowerCase(),password:i.password,role:i.role,status:u.J0.PENDING_VERIFICATION,profile:{firstName:i.firstName,lastName:i.lastName,languagePreferences:{native:["vietnamese"],learning:["english"]}},preferences:{notifications:{email:!0,push:!0,sms:!1},privacy:{profileVisibility:"public",showProgress:!0,showAchievements:!0},learning:{dailyGoal:30,preferredDifficulty:"medium"}}}),r=n.generateEmailVerificationToken();return await n.save(),console.log(`Verification token for ${n.email}: ${r}`),o.NextResponse.json({message:"Đăng k\xfd th\xe0nh c\xf4ng! Vui l\xf2ng kiểm tra email để x\xe1c thực t\xe0i khoản.",userId:n._id},{status:201})}catch(e){if(console.error("Registration error:",e),e instanceof c.z.ZodError)return o.NextResponse.json({error:"Dữ liệu kh\xf4ng hợp lệ",details:e.errors},{status:400});if(11e3===e.code)return o.NextResponse.json({error:"Email đ\xe3 được sử dụng"},{status:400});return o.NextResponse.json({error:"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng k\xfd"},{status:500})}}let d=new r.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:f}=d,k="/api/auth/register/route";function v(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}},14184:(e,t,i)=>{i.d(t,{ZP:()=>o});var n=i(11185),r=i.n(n);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let o=async function(){if(s.conn)return s.conn;s.promise||(s.promise=r().connect(a,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},93330:(e,t,i)=>{i.d(t,{J0:()=>r,ZP:()=>p,i4:()=>n});var n,r,a,s=i(11185),o=i.n(s),l=i(42023),u=i.n(l);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(n||(n={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(r||(r={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(a||(a={}));let c=new s.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(n),default:"student"},status:{type:String,enum:Object.values(r),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(a)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});c.index({"profile.firstName":1,"profile.lastName":1}),c.index({role:1,status:1}),c.index({createdAt:-1}),c.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),c.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),c.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await u().genSalt(12);this.password=await u().hash(this.password,t),e()}catch(t){e(t)}}),c.methods.comparePassword=async function(e){return u().compare(e,this.password)},c.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},c.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},c.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},c.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let p=o().models.User||o().model("User",c)}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[276,242,70,133],()=>i(3673));module.exports=n})();