"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/student/route";
exports.ids = ["app/api/dashboard/student/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstudent%2Froute&page=%2Fapi%2Fdashboard%2Fstudent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstudent%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstudent%2Froute&page=%2Fapi%2Fdashboard%2Fstudent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstudent%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ADMIN_OneDrive_Desktop_WebTA_src_app_api_dashboard_student_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/student/route.ts */ \"(rsc)/./src/app/api/dashboard/student/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/student/route\",\n        pathname: \"/api/dashboard/student\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/student/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\api\\\\dashboard\\\\student\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ADMIN_OneDrive_Desktop_WebTA_src_app_api_dashboard_student_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/student/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstudent%2Froute&page=%2Fapi%2Fdashboard%2Fstudent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstudent%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/student/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/dashboard/student/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Enrollment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Enrollment */ \"(rsc)/./src/models/Enrollment.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n\n\n\n\n\n\n// GET /api/dashboard/student - Lấy dữ liệu dashboard cho học viên\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Vui l\\xf2ng đăng nhập\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify user is student\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findById(session.user.id);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Kh\\xf4ng t\\xecm thấy người d\\xf9ng\"\n            }, {\n                status: 404\n            });\n        }\n        // Get user enrollments with course details\n        const enrollments = await _models_Enrollment__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find({\n            userId: session.user.id,\n            status: {\n                $in: [\n                    _models_Enrollment__WEBPACK_IMPORTED_MODULE_4__.EnrollmentStatus.ACTIVE,\n                    _models_Enrollment__WEBPACK_IMPORTED_MODULE_4__.EnrollmentStatus.COMPLETED\n                ]\n            }\n        }).populate({\n            path: \"courseId\",\n            select: \"title slug thumbnail instructor stats\",\n            populate: {\n                path: \"instructor\",\n                select: \"profile.firstName profile.lastName profile.avatar\"\n            }\n        }).sort({\n            lastAccessedAt: -1\n        }).lean();\n        // Calculate dashboard statistics\n        const stats = {\n            totalCourses: enrollments.length,\n            completedCourses: enrollments.filter((e)=>e.progress.status === \"completed\").length,\n            totalWatchTime: enrollments.reduce((total, e)=>total + e.progress.totalWatchTime, 0),\n            averageProgress: enrollments.length > 0 ? enrollments.reduce((total, e)=>total + e.progress.completionPercentage, 0) / enrollments.length : 0\n        };\n        // Get recent activity (last 7 days)\n        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);\n        const recentActivity = enrollments.filter((e)=>new Date(e.progress.lastAccessedAt) > sevenDaysAgo).length;\n        // Get learning streak (consecutive days with activity)\n        const learningStreak = await calculateLearningStreak(session.user.id);\n        // Get upcoming deadlines (courses with access expiration)\n        const upcomingDeadlines = enrollments.filter((e)=>e.accessExpiresAt && new Date(e.accessExpiresAt) > new Date()).sort((a, b)=>new Date(a.accessExpiresAt).getTime() - new Date(b.accessExpiresAt).getTime()).slice(0, 5);\n        // Get recommended courses (based on categories of enrolled courses)\n        const enrolledCategories = enrollments.map((e)=>e.courseId?.category).filter(Boolean);\n        // Get achievements\n        const achievements = calculateAchievements(enrollments, stats);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                enrollments,\n                stats: {\n                    ...stats,\n                    recentActivity,\n                    learningStreak\n                },\n                upcomingDeadlines,\n                achievements\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching student dashboard:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Lỗi server khi tải dashboard\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to calculate learning streak\nasync function calculateLearningStreak(userId) {\n    try {\n        // Get enrollments with recent activity\n        const enrollments = await _models_Enrollment__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find({\n            userId,\n            status: _models_Enrollment__WEBPACK_IMPORTED_MODULE_4__.EnrollmentStatus.ACTIVE\n        }).select(\"progress.lastAccessedAt\").lean();\n        if (enrollments.length === 0) return 0;\n        // Get unique activity dates (sorted descending)\n        const activityDates = enrollments.map((e)=>{\n            const date = new Date(e.progress.lastAccessedAt);\n            return date.toDateString();\n        }).filter((date, index, array)=>array.indexOf(date) === index).sort((a, b)=>new Date(b).getTime() - new Date(a).getTime());\n        if (activityDates.length === 0) return 0;\n        // Calculate consecutive days\n        let streak = 0;\n        const today = new Date().toDateString();\n        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();\n        // Check if there's activity today or yesterday\n        if (activityDates[0] === today || activityDates[0] === yesterday) {\n            streak = 1;\n            // Count consecutive days\n            for(let i = 1; i < activityDates.length; i++){\n                const currentDate = new Date(activityDates[i - 1]);\n                const nextDate = new Date(activityDates[i]);\n                const diffDays = Math.floor((currentDate.getTime() - nextDate.getTime()) / (1000 * 60 * 60 * 24));\n                if (diffDays === 1) {\n                    streak++;\n                } else {\n                    break;\n                }\n            }\n        }\n        return streak;\n    } catch (error) {\n        console.error(\"Error calculating learning streak:\", error);\n        return 0;\n    }\n}\n// Helper function to calculate achievements\nfunction calculateAchievements(enrollments, stats) {\n    const achievements = [];\n    // First course achievement\n    if (stats.totalCourses >= 1) {\n        achievements.push({\n            id: \"first_course\",\n            title: \"Kh\\xf3a học đầu ti\\xean\",\n            description: \"Đ\\xe3 đăng k\\xfd kh\\xf3a học đầu ti\\xean\",\n            icon: \"\\uD83C\\uDFAF\",\n            unlockedAt: enrollments[0]?.createdAt\n        });\n    }\n    // Course completion achievements\n    if (stats.completedCourses >= 1) {\n        achievements.push({\n            id: \"first_completion\",\n            title: \"Ho\\xe0n th\\xe0nh đầu ti\\xean\",\n            description: \"Đ\\xe3 ho\\xe0n th\\xe0nh kh\\xf3a học đầu ti\\xean\",\n            icon: \"\\uD83C\\uDFC6\",\n            unlockedAt: enrollments.find((e)=>e.progress.status === \"completed\")?.updatedAt\n        });\n    }\n    if (stats.completedCourses >= 5) {\n        achievements.push({\n            id: \"five_completions\",\n            title: \"Học vi\\xean t\\xedch cực\",\n            description: \"Đ\\xe3 ho\\xe0n th\\xe0nh 5 kh\\xf3a học\",\n            icon: \"\\uD83C\\uDF1F\",\n            unlockedAt: new Date()\n        });\n    }\n    // Watch time achievements\n    const totalHours = Math.floor(stats.totalWatchTime / 3600);\n    if (totalHours >= 10) {\n        achievements.push({\n            id: \"ten_hours\",\n            title: \"Người học ki\\xean tr\\xec\",\n            description: \"Đ\\xe3 học hơn 10 giờ\",\n            icon: \"⏰\",\n            unlockedAt: new Date()\n        });\n    }\n    if (totalHours >= 50) {\n        achievements.push({\n            id: \"fifty_hours\",\n            title: \"Chuy\\xean gia học tập\",\n            description: \"Đ\\xe3 học hơn 50 giờ\",\n            icon: \"\\uD83C\\uDF93\",\n            unlockedAt: new Date()\n        });\n    }\n    // Progress achievements\n    if (stats.averageProgress >= 80) {\n        achievements.push({\n            id: \"high_progress\",\n            title: \"Người ho\\xe0n th\\xe0nh xuất sắc\",\n            description: \"Tiến độ trung b\\xecnh tr\\xean 80%\",\n            icon: \"\\uD83D\\uDCC8\",\n            unlockedAt: new Date()\n        });\n    }\n    return achievements.sort((a, b)=>new Date(b.unlockedAt).getTime() - new Date(a.unlockedAt).getTime());\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/student/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _next_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @next-auth/mongodb-adapter */ \"(rsc)/./node_modules/@next-auth/mongodb-adapter/dist/index.js\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n\n\n\n\n\n\n// MongoDB client for NextAuth adapter\nconst client = new mongodb__WEBPACK_IMPORTED_MODULE_3__.MongoClient(process.env.MONGODB_URI);\nconst clientPromise = client.connect();\nconst authOptions = {\n    adapter: (0,_next_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_2__.MongoDBAdapter)(clientPromise),\n    secret: process.env.NEXTAUTH_SECRET,\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Email v\\xe0 mật khẩu l\\xe0 bắt buộc\");\n                }\n                try {\n                    await (0,_mongodb__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n                    // Find user with password field\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                        email: credentials.email.toLowerCase()\n                    }).select(\"+password\");\n                    if (!user) {\n                        throw new Error(\"Email hoặc mật khẩu kh\\xf4ng đ\\xfang\");\n                    }\n                    // Check if account is locked\n                    if (user.isLocked()) {\n                        throw new Error(\"T\\xe0i khoản đ\\xe3 bị kh\\xf3a do đăng nhập sai qu\\xe1 nhiều lần\");\n                    }\n                    // Check if account is active\n                    if (user.status !== _models_User__WEBPACK_IMPORTED_MODULE_5__.UserStatus.ACTIVE) {\n                        throw new Error(\"T\\xe0i khoản chưa được k\\xedch hoạt\");\n                    }\n                    // Verify password\n                    const isPasswordValid = await user.comparePassword(credentials.password);\n                    if (!isPasswordValid) {\n                        // Increment login attempts\n                        await user.incrementLoginAttempts();\n                        throw new Error(\"Email hoặc mật khẩu kh\\xf4ng đ\\xfang\");\n                    }\n                    // Reset login attempts on successful login\n                    if (user.loginAttempts > 0) {\n                        await user.updateOne({\n                            $unset: {\n                                loginAttempts: 1,\n                                lockUntil: 1\n                            }\n                        });\n                    }\n                    // Update last login\n                    await user.updateOne({\n                        lastLogin: new Date()\n                    });\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.profile.fullName,\n                        role: user.role,\n                        status: user.status,\n                        emailVerified: user.emailVerified,\n                        image: user.profile.avatar\n                    };\n                } catch (error) {\n                    throw new Error(error.message || \"Đ\\xe3 xảy ra lỗi trong qu\\xe1 tr\\xecnh đăng nhập\");\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\",\n        verifyRequest: \"/auth/verify-request\",\n        newUser: \"/auth/welcome\"\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // Initial sign in\n            if (account && user) {\n                token.role = user.role;\n                token.status = user.status;\n                token.emailVerified = user.emailVerified;\n            }\n            // Return previous token if the access token has not expired yet\n            return token;\n        },\n        async session ({ session, token }) {\n            // Send properties to the client\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.emailVerified = token.emailVerified;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow OAuth without email verification\n            if (account?.provider !== \"credentials\") {\n                return true;\n            }\n            // For credentials, check email verification\n            return user.emailVerified === true;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            console.log(`User ${user.email} signed in with ${account?.provider}`);\n        },\n        async signOut ({ session, token }) {\n            console.log(`User signed out`);\n        },\n        async createUser ({ user }) {\n            console.log(`New user created: ${user.email}`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONNECTION_STATES: () => (/* binding */ CONNECTION_STATES),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   disconnectDB: () => (/* binding */ disconnectDB),\n/* harmony export */   getConnectionStatus: () => (/* binding */ getConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env.local\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n/**\n * Utility function để disconnect từ database\n * Chủ yếu sử dụng trong testing\n */ async function disconnectDB() {\n    if (cached.conn) {\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().disconnect();\n        cached.conn = null;\n        cached.promise = null;\n    }\n}\n/**\n * Check database connection status\n */ function getConnectionStatus() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState;\n}\n/**\n * Connection states:\n * 0 = disconnected\n * 1 = connected\n * 2 = connecting\n * 3 = disconnecting\n */ const CONNECTION_STATES = {\n    DISCONNECTED: 0,\n    CONNECTED: 1,\n    CONNECTING: 2,\n    DISCONNECTING: 3\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Enrollment.ts":
/*!**********************************!*\
  !*** ./src/models/Enrollment.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnrollmentStatus: () => (/* binding */ EnrollmentStatus),\n/* harmony export */   EnrollmentType: () => (/* binding */ EnrollmentType),\n/* harmony export */   ProgressStatus: () => (/* binding */ ProgressStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nvar EnrollmentStatus;\n(function(EnrollmentStatus) {\n    EnrollmentStatus[\"ACTIVE\"] = \"active\";\n    EnrollmentStatus[\"COMPLETED\"] = \"completed\";\n    EnrollmentStatus[\"SUSPENDED\"] = \"suspended\";\n    EnrollmentStatus[\"EXPIRED\"] = \"expired\";\n    EnrollmentStatus[\"REFUNDED\"] = \"refunded\";\n})(EnrollmentStatus || (EnrollmentStatus = {}));\nvar EnrollmentType;\n(function(EnrollmentType) {\n    EnrollmentType[\"PAID\"] = \"paid\";\n    EnrollmentType[\"FREE\"] = \"free\";\n    EnrollmentType[\"ACTIVATION_CODE\"] = \"activation_code\";\n    EnrollmentType[\"ADMIN_GRANTED\"] = \"admin_granted\";\n})(EnrollmentType || (EnrollmentType = {}));\nvar ProgressStatus;\n(function(ProgressStatus) {\n    ProgressStatus[\"NOT_STARTED\"] = \"not_started\";\n    ProgressStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    ProgressStatus[\"COMPLETED\"] = \"completed\";\n})(ProgressStatus || (ProgressStatus = {}));\nconst LessonProgressSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    lessonId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Lesson\",\n        required: true\n    },\n    status: {\n        type: String,\n        enum: Object.values(ProgressStatus),\n        default: \"not_started\"\n    },\n    startedAt: Date,\n    completedAt: Date,\n    watchTime: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    lastPosition: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    attempts: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    score: {\n        type: Number,\n        min: 0,\n        max: 100\n    },\n    notes: String\n}, {\n    _id: false\n});\nconst EnrollmentSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"User ID l\\xe0 bắt buộc\"\n        ]\n    },\n    courseId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Course\",\n        required: [\n            true,\n            \"Course ID l\\xe0 bắt buộc\"\n        ]\n    },\n    status: {\n        type: String,\n        enum: Object.values(EnrollmentStatus),\n        default: \"active\"\n    },\n    type: {\n        type: String,\n        enum: Object.values(EnrollmentType),\n        required: [\n            true,\n            \"Loại đăng k\\xfd l\\xe0 bắt buộc\"\n        ]\n    },\n    progress: {\n        status: {\n            type: String,\n            enum: Object.values(ProgressStatus),\n            default: \"not_started\"\n        },\n        completedLessons: {\n            type: Number,\n            default: 0,\n            min: 0\n        },\n        totalLessons: {\n            type: Number,\n            default: 0,\n            min: 0\n        },\n        completionPercentage: {\n            type: Number,\n            default: 0,\n            min: 0,\n            max: 100\n        },\n        totalWatchTime: {\n            type: Number,\n            default: 0,\n            min: 0\n        },\n        lastAccessedAt: {\n            type: Date,\n            default: Date.now\n        },\n        estimatedCompletionDate: Date,\n        lessons: [\n            LessonProgressSchema\n        ]\n    },\n    payment: {\n        amount: {\n            type: Number,\n            required: true,\n            min: 0\n        },\n        currency: {\n            type: String,\n            default: \"VND\"\n        },\n        method: {\n            type: String,\n            enum: [\n                \"stripe\",\n                \"paypal\",\n                \"activation_code\",\n                \"free\"\n            ],\n            required: true\n        },\n        transactionId: String,\n        activationCode: String,\n        paidAt: Date,\n        refundedAt: Date,\n        refundAmount: {\n            type: Number,\n            min: 0\n        }\n    },\n    certificate: {\n        issued: {\n            type: Boolean,\n            default: false\n        },\n        issuedAt: Date,\n        certificateId: String,\n        downloadUrl: String,\n        validUntil: Date\n    },\n    accessGrantedAt: {\n        type: Date,\n        default: Date.now\n    },\n    accessExpiresAt: Date,\n    lastAccessedAt: {\n        type: Date,\n        default: Date.now\n    },\n    enrolledBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\"\n    },\n    notes: String\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\nEnrollmentSchema.index({\n    userId: 1,\n    courseId: 1\n}, {\n    unique: true\n});\nEnrollmentSchema.index({\n    userId: 1,\n    status: 1\n});\nEnrollmentSchema.index({\n    courseId: 1,\n    status: 1\n});\nEnrollmentSchema.index({\n    status: 1,\n    accessExpiresAt: 1\n});\nEnrollmentSchema.index({\n    \"payment.method\": 1,\n    \"payment.paidAt\": -1\n});\nEnrollmentSchema.index({\n    \"progress.status\": 1\n});\nEnrollmentSchema.index({\n    \"progress.completionPercentage\": -1\n});\nEnrollmentSchema.index({\n    lastAccessedAt: -1\n});\n// Virtual fields\nEnrollmentSchema.virtual(\"isActive\").get(function() {\n    return this.status === \"active\" && this.canAccess();\n});\nEnrollmentSchema.virtual(\"daysRemaining\").get(function() {\n    if (!this.accessExpiresAt) return null;\n    const now = new Date();\n    const diffTime = this.accessExpiresAt.getTime() - now.getTime();\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware\nEnrollmentSchema.pre(\"save\", async function(next) {\n    // Update progress when lessons array changes\n    if (this.isModified(\"progress.lessons\")) {\n        this.progress.completedLessons = this.progress.lessons.filter((lesson)=>lesson.status === \"completed\").length;\n        this.progress.completionPercentage = this.calculateCompletionPercentage();\n        // Update overall progress status\n        if (this.progress.completionPercentage === 0) {\n            this.progress.status = \"not_started\";\n        } else if (this.progress.completionPercentage === 100) {\n            this.progress.status = \"completed\";\n            this.status = \"completed\";\n        } else {\n            this.progress.status = \"in_progress\";\n        }\n    }\n    next();\n});\n// Instance methods\nEnrollmentSchema.methods.updateProgress = async function() {\n    const Lesson = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Lesson\");\n    // Get all lessons for this course\n    const lessons = await Lesson.find({\n        courseId: this.courseId,\n        status: \"published\"\n    }).sort({\n        order: 1\n    });\n    this.progress.totalLessons = lessons.length;\n    // Initialize lesson progress if not exists\n    for (const lesson of lessons){\n        const existingProgress = this.progress.lessons.find((p)=>p.lessonId.toString() === lesson._id.toString());\n        if (!existingProgress) {\n            this.progress.lessons.push({\n                lessonId: lesson._id,\n                status: \"not_started\",\n                watchTime: 0,\n                lastPosition: 0,\n                attempts: 0\n            });\n        }\n    }\n    // Calculate total watch time\n    this.progress.totalWatchTime = this.progress.lessons.reduce((total, lesson)=>total + lesson.watchTime, 0);\n    await this.save();\n};\nEnrollmentSchema.methods.calculateCompletionPercentage = function() {\n    if (this.progress.totalLessons === 0) return 0;\n    return Math.round(this.progress.completedLessons / this.progress.totalLessons * 100);\n};\nEnrollmentSchema.methods.isExpired = function() {\n    if (!this.accessExpiresAt) return false;\n    return new Date() > this.accessExpiresAt;\n};\nEnrollmentSchema.methods.canAccess = function() {\n    if (this.status !== \"active\") return false;\n    if (this.isExpired()) return false;\n    return true;\n};\nEnrollmentSchema.methods.generateCertificate = async function() {\n    if (this.progress.status !== \"completed\") {\n        throw new Error(\"Kh\\xf3a học chưa ho\\xe0n th\\xe0nh\");\n    }\n    if (this.certificate.issued) {\n        throw new Error(\"Chứng chỉ đ\\xe3 được cấp\");\n    }\n    // Generate certificate ID\n    const certificateId = `CERT-${this.courseId}-${this.userId}-${Date.now()}`;\n    this.certificate = {\n        issued: true,\n        issuedAt: new Date(),\n        certificateId,\n        downloadUrl: `/api/certificates/${certificateId}`,\n        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year\n    };\n    await this.save();\n};\nEnrollmentSchema.methods.extendAccess = async function(days) {\n    if (!this.accessExpiresAt) {\n        this.accessExpiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);\n    } else {\n        this.accessExpiresAt = new Date(this.accessExpiresAt.getTime() + days * 24 * 60 * 60 * 1000);\n    }\n    await this.save();\n};\n// Static methods\nEnrollmentSchema.statics.getActiveEnrollments = function(userId) {\n    return this.find({\n        userId,\n        status: \"active\",\n        $or: [\n            {\n                accessExpiresAt: {\n                    $exists: false\n                }\n            },\n            {\n                accessExpiresAt: {\n                    $gt: new Date()\n                }\n            }\n        ]\n    }).populate(\"courseId\");\n};\nEnrollmentSchema.statics.getCourseStats = async function(courseId) {\n    const stats = await this.aggregate([\n        {\n            $match: {\n                courseId: new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Types).ObjectId(courseId)\n            }\n        },\n        {\n            $group: {\n                _id: null,\n                totalEnrollments: {\n                    $sum: 1\n                },\n                activeEnrollments: {\n                    $sum: {\n                        $cond: [\n                            {\n                                $eq: [\n                                    \"$status\",\n                                    \"active\"\n                                ]\n                            },\n                            1,\n                            0\n                        ]\n                    }\n                },\n                completedEnrollments: {\n                    $sum: {\n                        $cond: [\n                            {\n                                $eq: [\n                                    \"$status\",\n                                    \"completed\"\n                                ]\n                            },\n                            1,\n                            0\n                        ]\n                    }\n                },\n                averageProgress: {\n                    $avg: \"$progress.completionPercentage\"\n                },\n                totalRevenue: {\n                    $sum: \"$payment.amount\"\n                }\n            }\n        }\n    ]);\n    return stats[0] || {\n        totalEnrollments: 0,\n        activeEnrollments: 0,\n        completedEnrollments: 0,\n        averageProgress: 0,\n        totalRevenue: 0\n    };\n};\n// Export model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Enrollment || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enrollment\", EnrollmentSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Enrollment.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageLevel: () => (/* binding */ LanguageLevel),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   UserStatus: () => (/* binding */ UserStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"STUDENT\"] = \"student\";\n    UserRole[\"INSTRUCTOR\"] = \"instructor\";\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n})(UserRole || (UserRole = {}));\nvar UserStatus;\n(function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n    UserStatus[\"PENDING_VERIFICATION\"] = \"pending_verification\";\n})(UserStatus || (UserStatus = {}));\nvar LanguageLevel;\n(function(LanguageLevel) {\n    LanguageLevel[\"BEGINNER\"] = \"beginner\";\n    LanguageLevel[\"ELEMENTARY\"] = \"elementary\";\n    LanguageLevel[\"INTERMEDIATE\"] = \"intermediate\";\n    LanguageLevel[\"UPPER_INTERMEDIATE\"] = \"upper_intermediate\";\n    LanguageLevel[\"ADVANCED\"] = \"advanced\";\n    LanguageLevel[\"PROFICIENT\"] = \"proficient\";\n})(LanguageLevel || (LanguageLevel = {}));\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: [\n            true,\n            \"Email l\\xe0 bắt buộc\"\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            \"Email kh\\xf4ng hợp lệ\"\n        ]\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"Mật khẩu l\\xe0 bắt buộc\"\n        ],\n        minlength: [\n            8,\n            \"Mật khẩu phải c\\xf3 \\xedt nhất 8 k\\xfd tự\"\n        ],\n        select: false // Không trả về password khi query\n    },\n    role: {\n        type: String,\n        enum: Object.values(UserRole),\n        default: \"student\"\n    },\n    status: {\n        type: String,\n        enum: Object.values(UserStatus),\n        default: \"pending_verification\"\n    },\n    profile: {\n        firstName: {\n            type: String,\n            required: [\n                true,\n                \"T\\xean l\\xe0 bắt buộc\"\n            ],\n            trim: true,\n            maxlength: [\n                50,\n                \"T\\xean kh\\xf4ng được vượt qu\\xe1 50 k\\xfd tự\"\n            ]\n        },\n        lastName: {\n            type: String,\n            required: [\n                true,\n                \"Họ l\\xe0 bắt buộc\"\n            ],\n            trim: true,\n            maxlength: [\n                50,\n                \"Họ kh\\xf4ng được vượt qu\\xe1 50 k\\xfd tự\"\n            ]\n        },\n        dateOfBirth: Date,\n        phoneNumber: {\n            type: String,\n            match: [\n                /^[+]?[\\d\\s\\-\\(\\)]+$/,\n                \"Số điện thoại kh\\xf4ng hợp lệ\"\n            ]\n        },\n        address: {\n            street: String,\n            city: String,\n            state: String,\n            country: String,\n            zipCode: String\n        },\n        avatar: String,\n        bio: {\n            type: String,\n            maxlength: [\n                500,\n                \"Bio kh\\xf4ng được vượt qu\\xe1 500 k\\xfd tự\"\n            ]\n        },\n        languagePreferences: {\n            native: [\n                String\n            ],\n            learning: [\n                String\n            ],\n            currentLevel: {\n                type: String,\n                enum: Object.values(LanguageLevel)\n            }\n        },\n        timezone: String\n    },\n    preferences: {\n        notifications: {\n            email: {\n                type: Boolean,\n                default: true\n            },\n            push: {\n                type: Boolean,\n                default: true\n            },\n            sms: {\n                type: Boolean,\n                default: false\n            }\n        },\n        privacy: {\n            profileVisibility: {\n                type: String,\n                enum: [\n                    \"public\",\n                    \"private\",\n                    \"friends\"\n                ],\n                default: \"public\"\n            },\n            showProgress: {\n                type: Boolean,\n                default: true\n            },\n            showAchievements: {\n                type: Boolean,\n                default: true\n            }\n        },\n        learning: {\n            dailyGoal: {\n                type: Number,\n                min: 5,\n                max: 480\n            },\n            reminderTime: String,\n            preferredDifficulty: {\n                type: String,\n                enum: [\n                    \"easy\",\n                    \"medium\",\n                    \"hard\"\n                ],\n                default: \"medium\"\n            }\n        }\n    },\n    emailVerified: {\n        type: Boolean,\n        default: false\n    },\n    emailVerificationToken: String,\n    passwordResetToken: String,\n    passwordResetExpires: Date,\n    lastLogin: Date,\n    loginAttempts: {\n        type: Number,\n        default: 0\n    },\n    lockUntil: Date\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\n// Note: email index is automatically created by unique: true in schema\nUserSchema.index({\n    \"profile.firstName\": 1,\n    \"profile.lastName\": 1\n});\nUserSchema.index({\n    role: 1,\n    status: 1\n});\nUserSchema.index({\n    createdAt: -1\n});\n// Virtual fields\nUserSchema.virtual(\"profile.fullName\").get(function() {\n    return `${this.profile.firstName} ${this.profile.lastName}`;\n});\nUserSchema.virtual(\"isAccountLocked\").get(function() {\n    return !!(this.lockUntil && this.lockUntil.getTime() > Date.now());\n});\n// Pre-save middleware\nUserSchema.pre(\"save\", async function(next) {\n    if (!this.isModified(\"password\")) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(candidatePassword, this.password);\n};\nUserSchema.methods.generatePasswordResetToken = function() {\n    const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.passwordResetToken = resetToken;\n    this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes\n    ;\n    return resetToken;\n};\nUserSchema.methods.generateEmailVerificationToken = function() {\n    const verificationToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.emailVerificationToken = verificationToken;\n    return verificationToken;\n};\nUserSchema.methods.isLocked = function() {\n    return !!(this.lockUntil && this.lockUntil > Date.now());\n};\nUserSchema.methods.incrementLoginAttempts = async function() {\n    // If we have a previous lock that has expired, restart at 1\n    if (this.lockUntil && this.lockUntil < Date.now()) {\n        return this.updateOne({\n            $unset: {\n                lockUntil: 1\n            },\n            $set: {\n                loginAttempts: 1\n            }\n        });\n    }\n    const updates = {\n        $inc: {\n            loginAttempts: 1\n        }\n    };\n    // Lock account after 5 failed attempts for 2 hours\n    if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {\n        updates.$set = {\n            lockUntil: Date.now() + 2 * 60 * 60 * 1000\n        } // 2 hours\n        ;\n    }\n    return this.updateOne(updates);\n};\n// Export model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/@next-auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstudent%2Froute&page=%2Fapi%2Fdashboard%2Fstudent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstudent%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();