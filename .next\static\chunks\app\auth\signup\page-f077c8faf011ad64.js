(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[271],{4630:function(e,r,t){Promise.resolve().then(t.bind(t,3669))},9376:function(e,r,t){"use strict";var s=t(5475);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},3669:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return m}});var s=t(7437),n=t(2265),a=t(9376),i=t(7648),l=t(6334),o=t(2827),c=t(9442),d=t(8629),u=t(1215);function m(){let e=(0,a.useRouter)(),[r,t]=(0,n.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"student"}),[m,h]=(0,n.useState)({}),[f,x]=(0,n.useState)(!1),[g,p]=(0,n.useState)(!1),b=e=>{let{name:r,value:s}=e.target;t(e=>({...e,[r]:s})),m[r]&&h(e=>({...e,[r]:""}))},v=()=>{let e={};return r.firstName.trim()||(e.firstName="T\xean l\xe0 bắt buộc"),r.lastName.trim()||(e.lastName="Họ l\xe0 bắt buộc"),r.email?/\S+@\S+\.\S+/.test(r.email)||(e.email="Email kh\xf4ng hợp lệ"):e.email="Email l\xe0 bắt buộc",r.password?r.password.length<8?e.password="Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(r.password)||(e.password="Mật khẩu phải chứa \xedt nhất 1 chữ hoa, 1 chữ thường v\xe0 1 số"):e.password="Mật khẩu l\xe0 bắt buộc",r.confirmPassword?r.password!==r.confirmPassword&&(e.confirmPassword="Mật khẩu x\xe1c nhận kh\xf4ng khớp"):e.confirmPassword="X\xe1c nhận mật khẩu l\xe0 bắt buộc",h(e),0===Object.keys(e).length},j=async e=>{if(e.preventDefault(),v()){x(!0);try{let e=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:r.firstName.trim(),lastName:r.lastName.trim(),email:r.email.toLowerCase(),password:r.password,role:r.role})}),t=await e.json();if(e.ok)p(!0);else if(t.details){let e={};t.details.forEach(r=>{r.path&&r.path.length>0&&(e[r.path[0]]=r.message)}),h(e)}else h({general:t.error||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng k\xfd"})}catch(e){h({general:"Đ\xe3 xảy ra lỗi kh\xf4ng mong muốn"})}finally{x(!1)}}};return g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full",children:(0,s.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 mb-4",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Đăng k\xfd th\xe0nh c\xf4ng!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Ch\xfang t\xf4i đ\xe3 gửi email x\xe1c thực đến địa chỉ email của bạn. Vui l\xf2ng kiểm tra email v\xe0 nhấp v\xe0o li\xean kết để k\xedch hoạt t\xe0i khoản."}),(0,s.jsx)(l.z,{onClick:()=>e.push("/auth/signin"),className:"w-full",children:"Đến trang đăng nhập"})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Tạo t\xe0i khoản mới"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Đ\xe3 c\xf3 t\xe0i khoản?"," ",(0,s.jsx)(i.default,{href:"/auth/signin",className:"font-medium text-primary hover:text-primary/80",children:"Đăng nhập ngay"})]})]}),(0,s.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg",children:[m.general&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(d.g7,{variant:"error",message:m.general})}),(0,s.jsxs)(c.l0,{onSubmit:j,children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)(c.Wi,{children:[(0,s.jsx)(c.lX,{htmlFor:"firstName",required:!0,children:"T\xean"}),(0,s.jsx)(o.I,{id:"firstName",name:"firstName",type:"text",autoComplete:"given-name",value:r.firstName,onChange:b,error:!!m.firstName,placeholder:"T\xean của bạn"}),(0,s.jsx)(c.Xq,{message:m.firstName})]}),(0,s.jsxs)(c.Wi,{children:[(0,s.jsx)(c.lX,{htmlFor:"lastName",required:!0,children:"Họ"}),(0,s.jsx)(o.I,{id:"lastName",name:"lastName",type:"text",autoComplete:"family-name",value:r.lastName,onChange:b,error:!!m.lastName,placeholder:"Họ của bạn"}),(0,s.jsx)(c.Xq,{message:m.lastName})]})]}),(0,s.jsxs)(c.Wi,{children:[(0,s.jsx)(c.lX,{htmlFor:"email",required:!0,children:"Email"}),(0,s.jsx)(o.I,{id:"email",name:"email",type:"email",autoComplete:"email",value:r.email,onChange:b,error:!!m.email,placeholder:"Nhập email của bạn"}),(0,s.jsx)(c.Xq,{message:m.email})]}),(0,s.jsxs)(c.Wi,{children:[(0,s.jsx)(c.lX,{htmlFor:"password",required:!0,children:"Mật khẩu"}),(0,s.jsx)(o.I,{id:"password",name:"password",type:"password",autoComplete:"new-password",value:r.password,onChange:b,error:!!m.password,placeholder:"Tạo mật khẩu"}),(0,s.jsx)(c.yv,{children:"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự, bao gồm chữ hoa, chữ thường v\xe0 số"}),(0,s.jsx)(c.Xq,{message:m.password})]}),(0,s.jsxs)(c.Wi,{children:[(0,s.jsx)(c.lX,{htmlFor:"confirmPassword",required:!0,children:"X\xe1c nhận mật khẩu"}),(0,s.jsx)(o.I,{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",value:r.confirmPassword,onChange:b,error:!!m.confirmPassword,placeholder:"Nhập lại mật khẩu"}),(0,s.jsx)(c.Xq,{message:m.confirmPassword})]}),(0,s.jsxs)(c.Wi,{children:[(0,s.jsx)(c.lX,{children:"Bạn l\xe0:"}),(0,s.jsx)(c.Ee,{name:"role",options:[{value:"student",label:"Học vi\xean - T\xf4i muốn học c\xe1c kh\xf3a học"},{value:"instructor",label:"Giảng vi\xean - T\xf4i muốn tạo v\xe0 b\xe1n kh\xf3a học"}],value:r.role,onChange:e=>{t(r=>({...r,role:e}))}})]}),(0,s.jsx)(l.z,{type:"submit",className:"w-full",disabled:f,children:f?(0,s.jsx)(u.gb,{size:"sm"}):"Tạo t\xe0i khoản"})]}),(0,s.jsxs)("p",{className:"mt-4 text-xs text-gray-500 text-center",children:["Bằng c\xe1ch tạo t\xe0i khoản, bạn đồng \xfd với"," ",(0,s.jsx)(i.default,{href:"/terms",className:"text-primary hover:text-primary/80",children:"Điều khoản sử dụng"})," ","v\xe0"," ",(0,s.jsx)(i.default,{href:"/privacy",className:"text-primary hover:text-primary/80",children:"Ch\xednh s\xe1ch bảo mật"})," ","của ch\xfang t\xf4i."]})]})]})})}},8629:function(e,r,t){"use strict";t.d(r,{g7:function(){return m}});var s=t(7437),n=t(2265),a=t(535),i=t(3448);let l=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),o=n.forwardRef((e,r)=>{let{className:t,variant:n,...a}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(l({variant:n}),t),...a})});o.displayName="Alert";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...n})});c.displayName="AlertTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...n})});d.displayName="AlertDescription";let u={success:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function m(e){let{title:r,message:t,variant:n="info",onClose:a}=e;return(0,s.jsxs)(o,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[n],className:"relative",children:[u[n],(0,s.jsxs)("div",{className:"flex-1",children:[r&&(0,s.jsx)(c,{children:r}),(0,s.jsx)(d,{children:t})]}),a&&(0,s.jsx)("button",{onClick:a,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},6334:function(e,r,t){"use strict";t.d(r,{z:function(){return o}});var s=t(7437),n=t(2265),a=t(535),i=t(3448);let l=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,r)=>{let{className:t,variant:n,size:a,asChild:o=!1,...c}=e;return(0,s.jsx)("button",{className:(0,i.cn)(l({variant:n,size:a,className:t})),ref:r,...c})});o.displayName="Button"},9442:function(e,r,t){"use strict";t.d(r,{Ee:function(){return u},Wi:function(){return l},Xq:function(){return c},l0:function(){return i},lX:function(){return o},yv:function(){return d}});var s=t(7437),n=t(2265),a=t(3448);function i(e){let{className:r,children:t,...n}=e;return(0,s.jsx)("form",{className:(0,a.cn)("space-y-6",r),...n,children:t})}function l(e){let{children:r,className:t}=e;return(0,s.jsx)("div",{className:(0,a.cn)("space-y-2",t),children:r})}function o(e){let{className:r,children:t,required:n,...i}=e;return(0,s.jsxs)("label",{className:(0,a.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",r),...i,children:[t,n&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]})}function c(e){let{message:r,className:t}=e;return r?(0,s.jsx)("p",{className:(0,a.cn)("text-sm text-red-600",t),children:r}):null}function d(e){let{children:r,className:t}=e;return(0,s.jsx)("p",{className:(0,a.cn)("text-sm text-gray-500",t),children:r})}function u(e){let{name:r,options:t,value:n,onChange:i,error:l,className:o}=e;return(0,s.jsx)("div",{className:(0,a.cn)("space-y-2",o),children:t.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"radio",id:"".concat(r,"-").concat(e.value),name:r,value:e.value,checked:n===e.value,onChange:e=>null==i?void 0:i(e.target.value),disabled:e.disabled,className:(0,a.cn)("h-4 w-4 text-primary focus:ring-primary focus:ring-2",l&&"border-red-500")}),(0,s.jsx)("label",{htmlFor:"".concat(r,"-").concat(e.value),className:(0,a.cn)("text-sm font-medium leading-none",e.disabled&&"opacity-50 cursor-not-allowed"),children:e.label})]},e.value))})}n.forwardRef((e,r)=>{let{className:t,error:n,...i}=e;return(0,s.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus-visible:ring-red-500",t),ref:r,...i})}).displayName="Textarea",n.forwardRef((e,r)=>{let{className:t,error:n,placeholder:i,children:l,...o}=e;return(0,s.jsxs)("select",{className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus-visible:ring-red-500",t),ref:r,...o,children:[i&&(0,s.jsx)("option",{value:"",disabled:!0,children:i}),l]})}).displayName="Select",n.forwardRef((e,r)=>{let{className:t,label:n,error:i,id:l,...o}=e,c=l||"checkbox-".concat(Math.random().toString(36).substr(2,9));return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",id:c,className:(0,a.cn)("h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary focus:ring-2",i&&"border-red-500",t),ref:r,...o}),n&&(0,s.jsx)("label",{htmlFor:c,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:n})]})}).displayName="Checkbox"},2827:function(e,r,t){"use strict";t.d(r,{I:function(){return i}});var s=t(7437),n=t(2265),a=t(3448);let i=n.forwardRef((e,r)=>{let{className:t,type:n,...i}=e;return(0,s.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},1215:function(e,r,t){"use strict";t.d(r,{gb:function(){return l}});var s=t(7437),n=t(535),a=t(3448);let i=(0,n.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function l(e){let{variant:r,size:t,className:n,text:l}=e;return(0,s.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",n),children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)("div",{className:(0,a.cn)(i({variant:r,size:t}))}),l&&(0,s.jsx)("p",{className:"text-sm text-gray-600",children:l})]})})}},3448:function(e,r,t){"use strict";t.d(r,{cn:function(){return a}});var s=t(1994),n=t(3335);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,s.W)(r))}}},function(e){e.O(0,[851,648,971,117,744],function(){return e(e.s=4630)}),_N_E=e.O()}]);