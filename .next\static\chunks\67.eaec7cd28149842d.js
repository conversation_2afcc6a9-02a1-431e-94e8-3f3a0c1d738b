"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[67],{5067:function(e,t,r){r.r(t),r.d(t,{VideoPlayer:function(){return i}});var s=r(7437),a=r(2265),n=r(3448),l=r(6334);function i(e){let{src:t,poster:r,title:i,className:c,autoPlay:u=!1,muted:o=!1,loop:d=!1,controls:m=!0,onTimeUpdate:h,onEnded:v,onPlay:p,onPause:f,startTime:g=0,playbackRate:b=1}=e,x=(0,a.useRef)(null),[y,j]=(0,a.useState)(!1),[N,w]=(0,a.useState)(0),[E,k]=(0,a.useState)(0),[D,L]=(0,a.useState)(1),[S,C]=(0,a.useState)(o),[z,F]=(0,a.useState)(!1),[M,T]=(0,a.useState)(!0),[R,_]=(0,a.useState)(0),[q,P]=(0,a.useState)(!0),[A,B]=(0,a.useState)(b),V=(0,a.useRef)();(0,a.useEffect)(()=>{let e=x.current;if(!e)return;let t=()=>{k(e.duration),P(!1),g>0&&(e.currentTime=g)},r=()=>{let t=e.currentTime;w(t),null==h||h(t,e.duration)},s=()=>{e.buffered.length>0&&_(e.buffered.end(e.buffered.length-1)/e.duration*100)},a=()=>{j(!0),null==p||p()},n=()=>{j(!1),null==f||f()},l=()=>{j(!1),null==v||v()},i=()=>{L(e.volume),C(e.muted)};return e.addEventListener("loadedmetadata",t),e.addEventListener("timeupdate",r),e.addEventListener("progress",s),e.addEventListener("play",a),e.addEventListener("pause",n),e.addEventListener("ended",l),e.addEventListener("volumechange",i),()=>{e.removeEventListener("loadedmetadata",t),e.removeEventListener("timeupdate",r),e.removeEventListener("progress",s),e.removeEventListener("play",a),e.removeEventListener("pause",n),e.removeEventListener("ended",l),e.removeEventListener("volumechange",i)}},[t,g,h,p,f,v]),(0,a.useEffect)(()=>{x.current&&(x.current.playbackRate=A)},[A]);let X=()=>{x.current&&(y?x.current.pause():x.current.play())},G=e=>"".concat(Math.floor(e/60),":").concat(Math.floor(e%60).toString().padStart(2,"0"));return(0,s.jsxs)("div",{className:(0,n.cn)("relative bg-black rounded-lg overflow-hidden group",c),onMouseMove:()=>{T(!0),V.current&&clearTimeout(V.current),V.current=setTimeout(()=>{y&&T(!1)},3e3)},onMouseEnter:()=>T(!0),onMouseLeave:()=>y&&T(!1),children:[(0,s.jsx)("video",{ref:x,src:t,poster:r,autoPlay:u,muted:o,loop:d,controls:!1,className:"w-full h-full object-contain",onClick:X}),q&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white"})}),i&&(0,s.jsx)("div",{className:"absolute top-0 left-0 right-0 bg-gradient-to-b from-black to-transparent p-4",children:(0,s.jsx)("h3",{className:"text-white text-lg font-semibold",children:i})}),m&&(0,s.jsxs)("div",{className:(0,n.cn)("absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 transition-opacity duration-300",M?"opacity-100":"opacity-0"),children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"w-full h-2 bg-gray-600 rounded-full cursor-pointer relative",onClick:e=>{if(!x.current)return;let t=e.currentTarget.getBoundingClientRect(),r=(e.clientX-t.left)/t.width;x.current.currentTime=r*E},children:[(0,s.jsx)("div",{className:"absolute top-0 left-0 h-full bg-gray-400 rounded-full",style:{width:"".concat(R,"%")}}),(0,s.jsx)("div",{className:"absolute top-0 left-0 h-full bg-primary rounded-full",style:{width:"".concat(N/E*100,"%")}}),(0,s.jsx)("div",{className:"absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-primary rounded-full shadow-lg",style:{left:"".concat(N/E*100,"%")}})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.z,{variant:"ghost",size:"sm",onClick:X,className:"text-white hover:bg-white hover:bg-opacity-20",children:y?"⏸️":"▶️"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.z,{variant:"ghost",size:"sm",onClick:()=>{x.current&&(x.current.muted=!S,C(!S))},className:"text-white hover:bg-white hover:bg-opacity-20",children:S||0===D?"\uD83D\uDD07":D<.5?"\uD83D\uDD09":"\uD83D\uDD0A"}),(0,s.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:S?0:D,onChange:e=>{if(!x.current)return;let t=parseFloat(e.target.value);x.current.volume=t,L(t),C(0===t)},className:"w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"})]}),(0,s.jsxs)("span",{className:"text-white text-sm",children:[G(N)," / ",G(E)]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("select",{value:A,onChange:e=>B(parseFloat(e.target.value)),className:"bg-transparent text-white text-sm border border-gray-600 rounded px-2 py-1",children:[.5,.75,1,1.25,1.5,2].map(e=>(0,s.jsxs)("option",{value:e,className:"bg-black",children:[e,"x"]},e))}),(0,s.jsx)(l.z,{variant:"ghost",size:"sm",onClick:()=>{x.current&&(z?document.exitFullscreen&&document.exitFullscreen():x.current.requestFullscreen&&x.current.requestFullscreen(),F(!z))},className:"text-white hover:bg-white hover:bg-opacity-20",children:z?"\uD83D\uDDD7":"⛶"})]})]})]}),!y&&!q&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)(l.z,{variant:"ghost",size:"lg",onClick:X,className:"text-white bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full w-16 h-16 text-2xl",children:"▶️"})})]})}t.default=i}}]);