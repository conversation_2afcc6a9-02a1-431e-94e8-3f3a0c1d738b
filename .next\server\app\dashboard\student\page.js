(()=>{var e={};e.id=665,e.ids=[665],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7443:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c}),t(30061),t(22834),t(39285),t(35866);var r=t(23191),a=t(88716),l=t(37922),n=t.n(l),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c=["",{children:["dashboard",{children:["student",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30061)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\student\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,22834)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\student\\page.tsx"],x="/dashboard/student/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/student/page",pathname:"/dashboard/student",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},18519:(e,s,t)=>{Promise.resolve().then(t.bind(t,7022))},29347:(e,s,t)=>{Promise.resolve().then(t.bind(t,19967))},7022:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(10326),a=t(77109),l=t(35047);t(17577);var n=t(90434),i=t(16545),d=t(99837);function c({children:e}){let{data:s,status:t}=(0,a.useSession)(),c=(0,l.useRouter)(),o=(0,l.usePathname)();if("loading"===t)return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:r.jsx(i.gb,{size:"lg"})});if(!s)return null;let x=s.user?.role||"student";return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("header",{className:"bg-white shadow-sm border-b",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(n.default,{href:"/",className:"flex items-center",children:[r.jsx("div",{className:"text-2xl font-bold text-primary",children:"WebTA"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"LMS"})]}),(0,r.jsxs)("nav",{className:"hidden md:flex space-x-1",children:[r.jsx(n.default,{href:"/dashboard/student",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard/student"===o?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard Học vi\xean"}),("instructor"===x||"admin"===x)&&r.jsx(n.default,{href:"/dashboard/instructor",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard/instructor"===o?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard Giảng vi\xean"}),r.jsx(n.default,{href:"/courses",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",children:"Kh\xf3a học"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"hidden sm:block text-sm text-gray-600",children:["Xin ch\xe0o, ",s.user?.name||s.user?.email]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(n.default,{href:"/profile",className:"text-sm text-gray-600 hover:text-gray-900 px-2 py-1 rounded transition-colors",children:"Hồ sơ"}),r.jsx(d.z,{variant:"outline",size:"sm",onClick:()=>c.push("/api/auth/signout"),className:"text-sm",children:"Đăng xuất"})]})]})]})})}),r.jsx("main",{className:"flex-1",children:e})]})}},19967:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(10326),a=t(17577),l=t(77109),n=t(35047),i=t(90434),d=t(46226),c=t(99837),o=t(47375),x=t(36792),h=t(8555),m=t(17334);function u(){let{data:e,status:s}=(0,l.useSession)();(0,n.useRouter)();let[t,u]=(0,a.useState)([]),[p,g]=(0,a.useState)(null),[b,j]=(0,a.useState)(!0),[f,v]=(0,a.useState)(null),[N,y]=(0,a.useState)("all"),D=e=>{let s=Math.floor(e/3600),t=Math.floor(e%3600/60);return s>0?`${s}h ${t}m`:`${t}m`},w=e=>e>=80?"bg-green-500":e>=50?"bg-yellow-500":"bg-blue-500",k=e=>{switch(e){case"completed":return r.jsx(x.C,{variant:"success",children:"Ho\xe0n th\xe0nh"});case"in_progress":return r.jsx(x.C,{variant:"warning",children:"Đang học"});case"not_started":return r.jsx(x.C,{variant:"secondary",children:"Chưa bắt đầu"});default:return r.jsx(x.C,{variant:"outline",children:e})}},C=t.filter(e=>"all"===N||("completed"===N?"completed"===e.progress.status:"in_progress"!==N||"in_progress"===e.progress.status));return"loading"===s||b?r.jsx(m.t6,{}):e?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"bg-white shadow-sm",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Xin ch\xe0o, ",e.user.name||"Học vi\xean","!"]}),r.jsx("p",{className:"mt-2 text-gray-600",children:"Theo d\xf5i tiến độ học tập của bạn"})]}),r.jsx(i.default,{href:"/courses",children:r.jsx(c.z,{children:"Kh\xe1m ph\xe1 kh\xf3a học mới"})})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[f&&r.jsx("div",{className:"mb-6",children:r.jsx(h.g7,{variant:"error",message:f})}),p&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[r.jsx(o.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDA"})}),(0,r.jsxs)("div",{className:"ml-4",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Tổng kh\xf3a học"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.totalCourses})]})]})}),r.jsx(o.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:r.jsx("span",{className:"text-2xl",children:"✅"})}),(0,r.jsxs)("div",{className:"ml-4",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Đ\xe3 ho\xe0n th\xe0nh"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.completedCourses})]})]})}),r.jsx(o.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:r.jsx("span",{className:"text-2xl",children:"⏱️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Thời gian học"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:D(p.totalWatchTime)})]})]})}),r.jsx(o.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,r.jsxs)("div",{className:"ml-4",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Tiến độ trung b\xecnh"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[Math.round(p.averageProgress),"%"]})]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Kh\xf3a học của t\xf4i"}),r.jsx("div",{className:"flex gap-2",children:[{key:"all",label:"Tất cả"},{key:"in_progress",label:"Đang học"},{key:"completed",label:"Ho\xe0n th\xe0nh"}].map(e=>r.jsx(c.z,{variant:N===e.key?"default":"outline",size:"sm",onClick:()=>y(e.key),children:e.label},e.key))})]}),C.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:C.filter(e=>e.courseId).map(e=>{let s=e.courseId;return r.jsx(o.Zb,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:(0,r.jsxs)(i.default,{href:`/learn/${s.slug}`,children:[(0,r.jsxs)("div",{className:"aspect-video bg-gray-200 relative",children:[s.thumbnail?r.jsx(d.default,{src:s.thumbnail,alt:s.title,fill:!0,className:"object-cover"}):r.jsx("div",{className:"flex items-center justify-center h-full text-gray-400 text-4xl",children:"\uD83D\uDCDA"}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,r.jsxs)("span",{children:[e.progress.completionPercentage,"% ho\xe0n th\xe0nh"]}),k(e.progress.status)]}),r.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2 mt-1",children:r.jsx("div",{className:`h-2 rounded-full ${w(e.progress.completionPercentage)}`,style:{width:`${e.progress.completionPercentage}%`}})})]})]}),(0,r.jsxs)("div",{className:"p-4",children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 line-clamp-2",children:s.title}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3 text-sm text-gray-500",children:[r.jsx("span",{children:"\uD83D\uDC68‍\uD83C\uDFEB"}),(0,r.jsxs)("span",{children:[s.instructor.profile.firstName," ",s.instructor.profile.lastName]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:[e.progress.completedLessons,"/",e.progress.totalLessons," b\xe0i học"]}),r.jsx("div",{children:D(e.progress.totalWatchTime)})]}),e.accessExpiresAt&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-orange-600",children:["Hết hạn: ",new Date(e.accessExpiresAt).toLocaleDateString("vi-VN")]})]})]})},e._id)})}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDA"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"all"===N?"Chưa c\xf3 kh\xf3a học n\xe0o":"completed"===N?"Chưa ho\xe0n th\xe0nh kh\xf3a học n\xe0o":"Chưa c\xf3 kh\xf3a học đang học"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"all"===N?"H\xe3y kh\xe1m ph\xe1 v\xe0 đăng k\xfd kh\xf3a học đầu ti\xean của bạn":"Thử thay đổi bộ lọc để xem kh\xf3a học kh\xe1c"}),"all"===N?r.jsx(i.default,{href:"/courses",children:r.jsx(c.z,{children:"Kh\xe1m ph\xe1 kh\xf3a học"})}):r.jsx(c.z,{onClick:()=>y("all"),children:"Xem tất cả kh\xf3a học"})]})]})]}):null}},16545:(e,s,t)=>{"use strict";t.d(s,{gb:()=>i});var r=t(10326),a=t(79360),l=t(51223);let n=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function i({variant:e,size:s,className:t,text:a}){return r.jsx("div",{className:(0,l.cn)("flex items-center justify-center",t),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[r.jsx("div",{className:(0,l.cn)(n({variant:e,size:s}))}),a&&r.jsx("p",{className:"text-sm text-gray-600",children:a})]})})}},22834:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\dashboard\layout.tsx#default`)},30061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\dashboard\student\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,105,226,826,631],()=>t(7443));module.exports=r})();