#!/usr/bin/env node

/**
 * Responsive Design Audit Script for WebTA LMS
 * Verifies responsive design implementation across all components and pages
 */

const fs = require('fs')
const path = require('path')

console.log('📱 Responsive Design Audit - WebTA LMS')
console.log('=' .repeat(50))

// Audit results
let totalChecks = 0
let passedChecks = 0
const issues = []
const recommendations = []

function check(name, condition, details = '', recommendation = '') {
  totalChecks++
  const passed = condition
  if (passed) passedChecks++
  
  const status = passed ? '✅ PASS' : '⚠️  ISSUE'
  console.log(`${status} ${name}`)
  if (details) console.log(`   ${details}`)
  
  if (!passed) {
    issues.push({ name, details, recommendation })
    if (recommendation) {
      recommendations.push(recommendation)
    }
  }
}

// 1. Mobile-First Approach
console.log('\n📱 1. Mobile-First Approach')
console.log('-'.repeat(40))

const pageFiles = [
  'src/app/page.tsx',
  'src/app/courses/page.tsx',
  'src/app/courses/[slug]/page.tsx',
  'src/app/dashboard/student/page.tsx',
  'src/app/dashboard/instructor/page.tsx',
  'src/app/test-layout/page.tsx'
]

let mobileFirstPages = 0
let responsiveGridPages = 0
let responsiveTextPages = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for mobile-first grid patterns
    if (content.includes('grid-cols-1') && (content.includes('md:grid-cols-2') || content.includes('lg:grid-cols-3'))) {
      mobileFirstPages++
    }
    
    // Check for responsive grid usage
    if (content.includes('grid') && (content.includes('sm:') || content.includes('md:') || content.includes('lg:'))) {
      responsiveGridPages++
    }
    
    // Check for responsive text sizing
    if (content.includes('text-') && (content.includes('sm:text-') || content.includes('md:text-') || content.includes('lg:text-'))) {
      responsiveTextPages++
    }
  }
})

check(
  'Mobile-first grid implementation',
  mobileFirstPages >= pageFiles.length * 0.6,
  `${mobileFirstPages}/${pageFiles.length} pages use mobile-first grid patterns`,
  'Implement mobile-first grid patterns (grid-cols-1 md:grid-cols-2 lg:grid-cols-3)'
)

check(
  'Responsive grid usage',
  responsiveGridPages >= pageFiles.length * 0.7,
  `${responsiveGridPages}/${pageFiles.length} pages use responsive grids`,
  'Add responsive breakpoints to grid layouts'
)

check(
  'Responsive typography',
  responsiveTextPages >= pageFiles.length * 0.4,
  `${responsiveTextPages}/${pageFiles.length} pages use responsive text sizing`,
  'Implement responsive typography with breakpoint-specific text sizes'
)

// 2. Breakpoint Coverage
console.log('\n💻 2. Breakpoint Coverage')
console.log('-'.repeat(40))

let smBreakpointUsage = 0
let mdBreakpointUsage = 0
let lgBreakpointUsage = 0
let xlBreakpointUsage = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('sm:')) smBreakpointUsage++
    if (content.includes('md:')) mdBreakpointUsage++
    if (content.includes('lg:')) lgBreakpointUsage++
    if (content.includes('xl:')) xlBreakpointUsage++
  }
})

check(
  'Small breakpoint (sm:) usage',
  smBreakpointUsage >= pageFiles.length * 0.5,
  `${smBreakpointUsage}/${pageFiles.length} pages use sm: breakpoint`,
  'Add small breakpoint responsive classes for better mobile experience'
)

check(
  'Medium breakpoint (md:) usage',
  mdBreakpointUsage >= pageFiles.length * 0.8,
  `${mdBreakpointUsage}/${pageFiles.length} pages use md: breakpoint`,
  'Implement medium breakpoint classes for tablet optimization'
)

check(
  'Large breakpoint (lg:) usage',
  lgBreakpointUsage >= pageFiles.length * 0.7,
  `${lgBreakpointUsage}/${pageFiles.length} pages use lg: breakpoint`,
  'Add large breakpoint classes for desktop optimization'
)

check(
  'Extra large breakpoint (xl:) usage',
  xlBreakpointUsage >= pageFiles.length * 0.3,
  `${xlBreakpointUsage}/${pageFiles.length} pages use xl: breakpoint`,
  'Consider xl: breakpoint for large desktop screens'
)

// 3. Component Responsiveness
console.log('\n🧩 3. Component Responsiveness')
console.log('-'.repeat(40))

const componentFiles = [
  'src/components/ui/Button.tsx',
  'src/components/ui/Card.tsx',
  'src/components/ui/Modal.tsx',
  'src/components/layout/Header.tsx',
  'src/components/layout/ConditionalLayout.tsx'
]

let responsiveComponents = 0
let componentsWithHiddenClasses = 0
let componentsWithFlexResponsive = 0

componentFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for responsive classes in components
    if (content.includes('sm:') || content.includes('md:') || content.includes('lg:')) {
      responsiveComponents++
    }
    
    // Check for responsive visibility
    if (content.includes('hidden') && (content.includes('sm:block') || content.includes('md:block') || content.includes('lg:block'))) {
      componentsWithHiddenClasses++
    }
    
    // Check for responsive flex patterns
    if (content.includes('flex') && (content.includes('sm:flex') || content.includes('md:flex') || content.includes('flex-col') && content.includes('md:flex-row'))) {
      componentsWithFlexResponsive++
    }
  }
})

check(
  'Component responsiveness',
  responsiveComponents >= componentFiles.length * 0.6,
  `${responsiveComponents}/${componentFiles.length} components have responsive classes`,
  'Add responsive behavior to UI components'
)

check(
  'Responsive visibility patterns',
  componentsWithHiddenClasses >= 1,
  `${componentsWithHiddenClasses} components use responsive visibility`,
  'Implement responsive show/hide patterns for better mobile UX'
)

check(
  'Responsive flex layouts',
  componentsWithFlexResponsive >= 1,
  `${componentsWithFlexResponsive} components use responsive flex patterns`,
  'Add responsive flex direction changes (flex-col md:flex-row)'
)

// 4. Navigation Responsiveness
console.log('\n🧭 4. Navigation Responsiveness')
console.log('-'.repeat(40))

// Check Header component for mobile navigation
const headerPath = 'src/components/layout/Header.tsx'
let hasMobileMenu = false
let hasBurgerMenu = false
let hasResponsiveNav = false

if (fs.existsSync(path.join(__dirname, '..', headerPath))) {
  const headerContent = fs.readFileSync(path.join(__dirname, '..', headerPath), 'utf8')
  
  hasMobileMenu = headerContent.includes('mobile') || headerContent.includes('Menu')
  hasBurgerMenu = headerContent.includes('hamburger') || headerContent.includes('burger') || headerContent.includes('☰')
  hasResponsiveNav = headerContent.includes('hidden') && headerContent.includes('md:flex')
}

check(
  'Mobile navigation implementation',
  hasMobileMenu,
  'Header component has mobile navigation support',
  'Implement mobile navigation menu for better mobile UX'
)

check(
  'Responsive navigation visibility',
  hasResponsiveNav,
  'Navigation uses responsive visibility classes',
  'Add responsive show/hide for navigation elements'
)

// Check Dashboard layout responsiveness
const dashboardLayoutPath = 'src/app/dashboard/layout.tsx'
let dashboardResponsive = false

if (fs.existsSync(path.join(__dirname, '..', dashboardLayoutPath))) {
  const dashboardContent = fs.readFileSync(path.join(__dirname, '..', dashboardLayoutPath), 'utf8')
  dashboardResponsive = dashboardContent.includes('hidden') && dashboardContent.includes('md:flex')
}

check(
  'Dashboard navigation responsiveness',
  dashboardResponsive,
  'Dashboard navigation adapts to screen size',
  'Make dashboard navigation responsive for mobile devices'
)

// 5. Content Layout Responsiveness
console.log('\n📄 5. Content Layout Responsiveness')
console.log('-'.repeat(40))

let pagesWithResponsiveContainers = 0
let pagesWithResponsivePadding = 0
let pagesWithResponsiveSpacing = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for responsive containers
    if (content.includes('max-w-') && (content.includes('sm:max-w-') || content.includes('md:max-w-') || content.includes('lg:max-w-'))) {
      pagesWithResponsiveContainers++
    }
    
    // Check for responsive padding
    if (content.includes('px-4') && content.includes('sm:px-6') && content.includes('lg:px-8')) {
      pagesWithResponsivePadding++
    }
    
    // Check for responsive spacing
    if (content.includes('space-y-') && (content.includes('sm:space-y-') || content.includes('md:space-y-'))) {
      pagesWithResponsiveSpacing++
    }
  }
})

check(
  'Responsive container widths',
  pagesWithResponsiveContainers >= pageFiles.length * 0.3,
  `${pagesWithResponsiveContainers}/${pageFiles.length} pages use responsive containers`,
  'Implement responsive container max-widths'
)

check(
  'Responsive padding patterns',
  pagesWithResponsivePadding >= pageFiles.length * 0.5,
  `${pagesWithResponsivePadding}/${pageFiles.length} pages use responsive padding`,
  'Use responsive padding pattern: px-4 sm:px-6 lg:px-8'
)

check(
  'Responsive spacing',
  pagesWithResponsiveSpacing >= pageFiles.length * 0.2,
  `${pagesWithResponsiveSpacing}/${pageFiles.length} pages use responsive spacing`,
  'Add responsive spacing classes for better layout control'
)

// 6. Image and Media Responsiveness
console.log('\n🖼️ 6. Image and Media Responsiveness')
console.log('-'.repeat(40))

let pagesWithResponsiveImages = 0
let pagesWithAspectRatio = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for responsive images
    if (content.includes('Image') && (content.includes('object-cover') || content.includes('object-contain'))) {
      pagesWithResponsiveImages++
    }
    
    // Check for aspect ratio classes
    if (content.includes('aspect-') || content.includes('aspect-video') || content.includes('aspect-square')) {
      pagesWithAspectRatio++
    }
  }
})

check(
  'Responsive image implementation',
  pagesWithResponsiveImages >= 2,
  `${pagesWithResponsiveImages} pages use responsive images`,
  'Implement responsive image patterns with object-cover/contain'
)

check(
  'Aspect ratio usage',
  pagesWithAspectRatio >= 2,
  `${pagesWithAspectRatio} pages use aspect ratio classes`,
  'Use aspect ratio classes for consistent media dimensions'
)

// Summary
console.log('\n' + '='.repeat(50))
console.log('📱 RESPONSIVE DESIGN AUDIT SUMMARY')
console.log('='.repeat(50))

const passRate = Math.round((passedChecks / totalChecks) * 100)
console.log(`Total Checks: ${totalChecks}`)
console.log(`Passed: ${passedChecks}`)
console.log(`Issues Found: ${totalChecks - passedChecks}`)
console.log(`Responsive Score: ${passRate}%`)

if (passRate >= 90) {
  console.log('\n🎉 EXCELLENT! Responsive design is very comprehensive!')
} else if (passRate >= 80) {
  console.log('\n✅ GOOD! Responsive design is well implemented!')
} else if (passRate >= 70) {
  console.log('\n⚠️  FAIR! Some responsive design improvements needed!')
} else {
  console.log('\n❌ POOR! Significant responsive design issues found!')
}

// Issues and Recommendations
if (issues.length > 0) {
  console.log('\n⚠️  Issues Found:')
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.name}`)
    if (issue.details) console.log(`   ${issue.details}`)
    if (issue.recommendation) console.log(`   💡 ${issue.recommendation}`)
  })
}

if (recommendations.length > 0) {
  console.log('\n💡 Key Recommendations:')
  const uniqueRecommendations = [...new Set(recommendations)]
  uniqueRecommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`)
  })
}

console.log('\n📋 Manual Testing Checklist:')
console.log('1. Test on mobile devices (320px - 768px)')
console.log('2. Test on tablets (768px - 1024px)')
console.log('3. Test on desktop (1024px+)')
console.log('4. Check navigation usability on mobile')
console.log('5. Verify content readability at all sizes')
console.log('6. Test touch targets on mobile devices')

console.log('\n🚀 Responsive Design Audit Complete!')

// Exit with appropriate code
process.exit(issues.length > 0 ? 1 : 0)
