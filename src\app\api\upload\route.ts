import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { uploadFile, validateFile, getFileType } from '@/lib/cloudinary'
import User, { UserRole } from '@/models/User'
import connectDB from '@/lib/mongodb'

// POST /api/upload - Upload file to Cloudinary
export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Check user permissions
    const user = await User.findById(session.user.id)
    if (!user || (user.role !== UserRole.INSTRUCTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({
        success: false,
        error: '<PERSON>ạn không có quyền upload file'
      }, { status: 403 })
    }
    
    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const folder = formData.get('folder') as string || 'general'
    const tags = formData.get('tags') as string || ''
    const context = formData.get('context') as string || '{}'
    
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'Không tìm thấy file'
      }, { status: 400 })
    }
    
    // Validate file
    const validation = validateFile(file)
    if (!validation.valid) {
      return NextResponse.json({
        success: false,
        error: validation.error
      }, { status: 400 })
    }
    
    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // Parse context
    let contextObj = {}
    try {
      contextObj = JSON.parse(context)
    } catch (error) {
      // Ignore invalid JSON
    }
    
    // Upload to Cloudinary
    const uploadResult = await uploadFile(buffer, file.name, {
      folder: `webta/${folder}`,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      context: {
        ...contextObj,
        uploaded_by: session.user.id,
        original_filename: file.name
      }
    })
    
    if (!uploadResult.success) {
      return NextResponse.json({
        success: false,
        error: uploadResult.error
      }, { status: 500 })
    }
    
    // Generate thumbnail for videos
    let thumbnailUrl = null
    if (validation.type === 'video' && uploadResult.data) {
      const { generateVideoThumbnail } = await import('@/lib/cloudinary')
      const thumbnailResult = await generateVideoThumbnail(uploadResult.data.public_id)
      if (thumbnailResult.success) {
        thumbnailUrl = thumbnailResult.thumbnailUrl
      }
    }
    
    // Return upload result
    return NextResponse.json({
      success: true,
      data: {
        ...uploadResult.data,
        thumbnailUrl,
        fileType: validation.type,
        originalName: file.name,
        uploadedBy: session.user.id,
        uploadedAt: new Date().toISOString()
      },
      message: 'Upload file thành công'
    })
    
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi upload file'
    }, { status: 500 })
  }
}

// GET /api/upload/signature - Get signed upload URL for direct uploads
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Check user permissions
    const user = await User.findById(session.user.id)
    if (!user || (user.role !== UserRole.INSTRUCTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({
        success: false,
        error: 'Bạn không có quyền upload file'
      }, { status: 403 })
    }
    
    const { searchParams } = new URL(request.url)
    const folder = searchParams.get('folder') || 'general'
    const resourceType = searchParams.get('resource_type') as 'image' | 'video' | 'raw' || 'image'
    const tags = searchParams.get('tags')?.split(',') || ['webta']
    
    const { generateSignedUploadUrl } = await import('@/lib/cloudinary')
    
    const signedUrl = generateSignedUploadUrl({
      folder: `webta/${folder}`,
      tags: [...tags, `user_${session.user.id}`],
      resource_type: resourceType
    })
    
    return NextResponse.json({
      success: true,
      data: signedUrl
    })
    
  } catch (error) {
    console.error('Signature generation error:', error)
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi tạo signature'
    }, { status: 500 })
  }
}
