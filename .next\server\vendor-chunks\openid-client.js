/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split(\".\").map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, \"access_token\", \"code\", \"error_description\", \"error_uri\", \"error\", \"expires_in\", \"id_token\", \"iss\", \"response\", \"session_state\", \"state\", \"token_type\");\n}\nfunction authorizationHeaderValue(token, tokenType = \"Bearer\") {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: \"openid\",\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === \"claims\" && typeof value === \"object\") {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === \"resource\" && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== \"string\") {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n        throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes(\"client_secret_post\")) {\n                properties.token_endpoint_auth_method = \"client_secret_post\";\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError(\"provide a redirect_uri or redirect_uris, not both\");\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError(\"provide a response_type or response_types, not both\");\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== \"string\" || !metadata.client_id) {\n            throw new TypeError(\"client_id is required\");\n        }\n        const properties = {\n            grant_types: [\n                \"authorization_code\"\n            ],\n            id_token_signed_response_alg: \"RS256\",\n            authorization_signed_response_alg: \"RS256\",\n            response_types: [\n                \"code\"\n            ],\n            token_endpoint_auth_method: \"client_secret_basic\",\n            ...this.fapi1() ? {\n                grant_types: [\n                    \"authorization_code\",\n                    \"implicit\"\n                ],\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                response_types: [\n                    \"code id_token\"\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case \"self_signed_tls_client_auth\":\n                case \"tls_client_auth\":\n                    break;\n                case \"private_key_jwt\":\n                    if (!jwks) {\n                        throw new TypeError(\"jwks is required\");\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError(\"token_endpoint_auth_method is required\");\n                default:\n                    throw new TypeError(\"invalid or unsupported token_endpoint_auth_method\");\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport(\"token\", this.issuer, properties);\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        assertIssuerConfiguration(this.issuer, \"authorization_endpoint\");\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, \"%20\");\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join(\"\\n\");\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, \"end_session_endpoint\");\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === \"string\";\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError(\"#callbackParams only accepts string urls, http.IncomingMessage or a lookalike\");\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case \"GET\":\n                    return pickCb(getSearchParams(input.url));\n                case \"POST\":\n                    if (input.body === undefined) {\n                        throw new TypeError(\"incoming message body missing, include a body parser prior to this method call\");\n                    }\n                    switch(typeof input.body){\n                        case \"object\":\n                        case \"string\":\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString(\"utf-8\")));\n                            }\n                            if (typeof input.body === \"string\") {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError(\"invalid IncomingMessage body object\");\n                    }\n                default:\n                    throw new TypeError(\"invalid IncomingMessage method\");\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            id_token: [\n                \"id_token\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"authorization\", checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"token\", checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === \"string\" && params.id_token.length) {\n            throw new RPError({\n                message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === \"string\" && tokenset.id_token.length) {\n                throw new RPError({\n                    message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = \"A128CBC-HS256\") {\n        const header = JSON.parse(base64url.decode(jwe.split(\".\")[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE enc received, expected %s, got: %s\",\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: \"enc\"\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === \"dir\" ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: \"failed to decrypt JWE\",\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === \"number\" || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: \"missing required JWT property auth_time\",\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== \"number\") {\n                throw new RPError({\n                    message: \"JWT auth_time claim must be a JSON numeric value\",\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === \"number\" && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    \"too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i\",\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    \"nonce mismatch, expected %s, got: %s\",\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === \"authorization\") {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: \"missing required property at_hash\",\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: \"missing required property c_hash\",\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: \"missing required property s_hash\",\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: \"s_hash\",\n                        source: \"state\"\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    \"JWT issued too far in the past, now %i, iat %i\",\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"at_hash\",\n                    source: \"access_token\"\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"c_hash\",\n                    source: \"code\"\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        \"iss\",\n        \"sub\",\n        \"aud\",\n        \"exp\",\n        \"iat\"\n    ]) {\n        const isSelfIssued = this.issuer.issuer === \"https://self-issued.me\";\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    \"failed to decode JWT (%s: %s)\",\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWT alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                \"sub_jwk\"\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace(\"{tenantid}\", payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        \"unexpected iss value, expected %s, got: %s\",\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== \"number\") {\n                throw new RPError({\n                    message: \"JWT iat claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== \"number\") {\n                throw new RPError({\n                    message: \"JWT nbf claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        \"JWT not active yet, now %i, nbf %i\",\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== \"number\") {\n                throw new RPError({\n                    message: \"JWT exp claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        \"JWT expired, now %i, exp %i\",\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: \"missing required JWT property azp\",\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            \"aud is missing the client_id, expected %s to be included in %j\",\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        \"aud mismatch, expected %s, got: %s\",\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === \"string\") {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        \"azp mismatch, got: %s\",\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, \"public\");\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: \"failed to use sub_jwk claim as an asymmetric JSON Web Key\",\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: \"failed to match the subject with sub_jwk\",\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith(\"HS\")) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== \"none\") {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: \"sig\"\n            });\n        }\n        if (!keys && header.alg === \"none\") {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: \"failed to validate JWT signature\",\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError(\"refresh_token not present in TokenSet\");\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: \"refresh_token\",\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, \"token\", skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            \"sub mismatch, expected %s, got: %s\",\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? \"DPoP\" : accessToken instanceof TokenSet ? accessToken.token_type : \"Bearer\" } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError(\"access_token not present in TokenSet\");\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError(\"no access token provided\");\n        } else if (typeof accessToken !== \"string\") {\n            throw new TypeError(\"invalid access token provided\");\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: \"buffer\",\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers[\"www-authenticate\"];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith(\"dpop \") && parseWwwAuthenticate(wwwAuthenticate).error === \"use_dpop_nonce\") {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = \"GET\", via = \"header\", tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"userinfo_endpoint\");\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== \"GET\" && options.method !== \"POST\") {\n            throw new TypeError(\"#userinfo() method can only be POST or a GET\");\n        }\n        if (via === \"body\" && options.method !== \"POST\") {\n            throw new TypeError(\"can only send body on POST\");\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: \"application/jwt\"\n            };\n        } else {\n            options.headers = {\n                Accept: \"application/json\"\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === \"body\") {\n            options.headers.Authorization = undefined;\n            options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n            options.body = new URLSearchParams();\n            options.body.append(\"access_token\", accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === \"GET\") {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers[\"content-type\"])) {\n                throw new RPError({\n                    message: \"expected application/jwt response from the userinfo_endpoint\",\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: \"failed to parse userinfo JWE payload as JSON\",\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, \"response\", {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        \"userinfo sub mismatch, expected %s, got: %s\",\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? \"sha256\" : len <= 384 ? \"sha384\" : len <= 512 ? \"sha512\" : false;\n        if (!hash) {\n            throw new Error(\"unsupported symmetric encryption key derivation\");\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError(\"client_secret is required\");\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const response = await authenticatedPost.call(this, \"token\", {\n            form: body,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === \"use_dpop_nonce\") {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"device_authorization_endpoint\");\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, \"device_authorization\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"revocation_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"revocation\", {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"introspection_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"introspection\", {\n            form,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, \"registration_endpoint\");\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: \"application/json\",\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: \"json\",\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: \"POST\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: registrationClientUri,\n            responseType: \"json\",\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: \"application/json\"\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || \"none\", encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || \"A128CBC-HS256\" } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError(\"requestObject must be a plain object\");\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: \"oauth-authz-req+jwt\"\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === \"none\") {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                \"\"\n            ].join(\".\");\n        } else {\n            const symmetric = signingAlgorithm.startsWith(\"HS\");\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: \"sig\"\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: \"oauth-authz-req+jwt\"\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: \"enc\"\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === \"dir\" ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"pushed_authorization_request_endpoint\");\n        const body = {\n            ...\"request\" in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, \"pushed_authorization_request\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!(\"expires_in\" in responseBody)) {\n            throw new RPError({\n                message: \"expected expires_in in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== \"number\") {\n            throw new RPError({\n                message: \"invalid expires_in value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (!(\"request_uri\" in responseBody)) {\n            throw new RPError({\n                message: \"expected request_uri in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== \"string\") {\n            throw new RPError({\n                message: \"invalid request_uri value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === \"FAPI1Client\";\n    }\n    fapi2() {\n        return this.constructor.name === \"FAPI2Client\";\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            \"iss\",\n            \"exp\",\n            \"aud\"\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError(\"payload must be a plain object\");\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === \"node:crypto\") {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError(\"unrecognized crypto runtime\");\n        }\n        if (privateKey.type !== \"private\") {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError(\"could not determine DPoP JWS Algorithm\");\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash(\"sha256\").update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: \"dpop+jwt\",\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case \"Ed25519\":\n        case \"Ed448\":\n            return \"EdDSA\";\n        case \"ECDSA\":\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case \"P-256\":\n                        return \"ES256\";\n                    case \"P-384\":\n                        return \"ES384\";\n                    case \"P-521\":\n                        return \"ES512\";\n                    default:\n                        break;\n                }\n                break;\n            }\n        case \"RSASSA-PKCS1-v1_5\":\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case \"RSA-PSS\":\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError(\"unsupported DPoP private key\");\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === \"node:crypto\") {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case \"ed25519\":\n            case \"ed448\":\n                return \"EdDSA\";\n            case \"ec\":\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case \"rsa\":\n            case rsaPssParams && \"rsa-pss\":\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError(\"unsupported DPoP private key\");\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === \"object\" && privateKeyInput.format === \"jwk\" && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === \"rsa-pss\") {\n                candidates = candidates.filter((value)=>value.startsWith(\"PS\"));\n            }\n            return [\n                \"PS256\",\n                \"PS384\",\n                \"PS512\",\n                \"RS256\",\n                \"RS384\",\n                \"RS384\"\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return \"PS256\";\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.key.crv){\n            case \"P-256\":\n                return \"ES256\";\n            case \"secp256k1\":\n                return \"ES256K\";\n            case \"P-384\":\n                return \"ES384\";\n            case \"P-512\":\n                return \"ES512\";\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: \"der\",\n            type: \"pkcs8\"\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return \"ES256\";\n        }\n        if (curveOid.equals(p384)) {\n            return \"ES384\";\n        }\n        if (curveOid.equals(p521)) {\n            return \"ES512\";\n        }\n        if (curveOid.equals(secp256k1)) {\n            return \"ES256K\";\n        }\n        throw new TypeError(\"unsupported DPoP private key curve\");\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === \"node:crypto\" && typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.format === \"jwk\") {\n        return pick(privateKeyInput.key, \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === \"WebCryptoAPI\") {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\n\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach((prop) => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(\n          `expected ${prop} string to be returned by Device Authorization Response, got %j`,\n          response[prop],\n        );\n      }\n    });\n\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError(\n        'expected expires_in number to be returned by Device Authorization Response, got %j',\n        response.expires_in,\n      );\n    }\n\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n\n  abort() {\n    this.#aborted = true;\n  }\n\n  async poll({ signal } = {}) {\n    if ((signal && signal.aborted) || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n\n    if (this.expired()) {\n      throw new RPError(\n        'the device code %j has expired and the device authorization session has concluded',\n        this.device_code,\n      );\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, this.#interval));\n\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant(\n        {\n          ...this.#exchangeBody,\n          grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n          device_code: this.device_code,\n        },\n        { clientAssertionPayload: this.#clientAssertionPayload, DPoP: this.#DPoP },\n      );\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({ signal });\n        default:\n          throw err;\n      }\n    }\n\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n\n    return tokenset;\n  }\n\n  get device_code() {\n    return this.#response.device_code;\n  }\n\n  get user_code() {\n    return this.#response.user_code;\n  }\n\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZGV2aWNlX2Zsb3dfaGFuZGxlLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsVUFBVSxFQUFFLG1CQUFPLENBQUMsa0JBQU07O0FBRWxDLFFBQVEsbUJBQW1CLEVBQUUsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQyxZQUFZLG1CQUFPLENBQUMsa0dBQTBCOztBQUU5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixzRUFBc0U7QUFDdEY7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLE1BQU07QUFDNUI7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxlQUFlLFNBQVMsSUFBSTtBQUM1QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFVBQVUsd0VBQXdFO0FBQ2xGO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLFFBQVE7QUFDckM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsY0FBYyx1QkFBdUIsRUFBRTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssRUFBRTtBQUNQO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZGV2aWNlX2Zsb3dfaGFuZGxlLmpzPzU5NzciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBpbnNwZWN0IH0gPSByZXF1aXJlKCd1dGlsJyk7XG5cbmNvbnN0IHsgUlBFcnJvciwgT1BFcnJvciB9ID0gcmVxdWlyZSgnLi9lcnJvcnMnKTtcbmNvbnN0IG5vdyA9IHJlcXVpcmUoJy4vaGVscGVycy91bml4X3RpbWVzdGFtcCcpO1xuXG5jbGFzcyBEZXZpY2VGbG93SGFuZGxlIHtcbiAgI2Fib3J0ZWQ7XG4gICNjbGllbnQ7XG4gICNjbGllbnRBc3NlcnRpb25QYXlsb2FkO1xuICAjRFBvUDtcbiAgI2V4Y2hhbmdlQm9keTtcbiAgI2V4cGlyZXNfYXQ7XG4gICNpbnRlcnZhbDtcbiAgI21heEFnZTtcbiAgI3Jlc3BvbnNlO1xuICBjb25zdHJ1Y3Rvcih7IGNsaWVudCwgZXhjaGFuZ2VCb2R5LCBjbGllbnRBc3NlcnRpb25QYXlsb2FkLCByZXNwb25zZSwgbWF4QWdlLCBEUG9QIH0pIHtcbiAgICBbJ3ZlcmlmaWNhdGlvbl91cmknLCAndXNlcl9jb2RlJywgJ2RldmljZV9jb2RlJ10uZm9yRWFjaCgocHJvcCkgPT4ge1xuICAgICAgaWYgKHR5cGVvZiByZXNwb25zZVtwcm9wXSAhPT0gJ3N0cmluZycgfHwgIXJlc3BvbnNlW3Byb3BdKSB7XG4gICAgICAgIHRocm93IG5ldyBSUEVycm9yKFxuICAgICAgICAgIGBleHBlY3RlZCAke3Byb3B9IHN0cmluZyB0byBiZSByZXR1cm5lZCBieSBEZXZpY2UgQXV0aG9yaXphdGlvbiBSZXNwb25zZSwgZ290ICVqYCxcbiAgICAgICAgICByZXNwb25zZVtwcm9wXSxcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGlmICghTnVtYmVyLmlzU2FmZUludGVnZXIocmVzcG9uc2UuZXhwaXJlc19pbikpIHtcbiAgICAgIHRocm93IG5ldyBSUEVycm9yKFxuICAgICAgICAnZXhwZWN0ZWQgZXhwaXJlc19pbiBudW1iZXIgdG8gYmUgcmV0dXJuZWQgYnkgRGV2aWNlIEF1dGhvcml6YXRpb24gUmVzcG9uc2UsIGdvdCAlaicsXG4gICAgICAgIHJlc3BvbnNlLmV4cGlyZXNfaW4sXG4gICAgICApO1xuICAgIH1cblxuICAgIHRoaXMuI2V4cGlyZXNfYXQgPSBub3coKSArIHJlc3BvbnNlLmV4cGlyZXNfaW47XG4gICAgdGhpcy4jY2xpZW50ID0gY2xpZW50O1xuICAgIHRoaXMuI0RQb1AgPSBEUG9QO1xuICAgIHRoaXMuI21heEFnZSA9IG1heEFnZTtcbiAgICB0aGlzLiNleGNoYW5nZUJvZHkgPSBleGNoYW5nZUJvZHk7XG4gICAgdGhpcy4jY2xpZW50QXNzZXJ0aW9uUGF5bG9hZCA9IGNsaWVudEFzc2VydGlvblBheWxvYWQ7XG4gICAgdGhpcy4jcmVzcG9uc2UgPSByZXNwb25zZTtcbiAgICB0aGlzLiNpbnRlcnZhbCA9IHJlc3BvbnNlLmludGVydmFsICogMTAwMCB8fCA1MDAwO1xuICB9XG5cbiAgYWJvcnQoKSB7XG4gICAgdGhpcy4jYWJvcnRlZCA9IHRydWU7XG4gIH1cblxuICBhc3luYyBwb2xsKHsgc2lnbmFsIH0gPSB7fSkge1xuICAgIGlmICgoc2lnbmFsICYmIHNpZ25hbC5hYm9ydGVkKSB8fCB0aGlzLiNhYm9ydGVkKSB7XG4gICAgICB0aHJvdyBuZXcgUlBFcnJvcigncG9sbGluZyBhYm9ydGVkJyk7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuZXhwaXJlZCgpKSB7XG4gICAgICB0aHJvdyBuZXcgUlBFcnJvcihcbiAgICAgICAgJ3RoZSBkZXZpY2UgY29kZSAlaiBoYXMgZXhwaXJlZCBhbmQgdGhlIGRldmljZSBhdXRob3JpemF0aW9uIHNlc3Npb24gaGFzIGNvbmNsdWRlZCcsXG4gICAgICAgIHRoaXMuZGV2aWNlX2NvZGUsXG4gICAgICApO1xuICAgIH1cblxuICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIHRoaXMuI2ludGVydmFsKSk7XG5cbiAgICBsZXQgdG9rZW5zZXQ7XG4gICAgdHJ5IHtcbiAgICAgIHRva2Vuc2V0ID0gYXdhaXQgdGhpcy4jY2xpZW50LmdyYW50KFxuICAgICAgICB7XG4gICAgICAgICAgLi4udGhpcy4jZXhjaGFuZ2VCb2R5LFxuICAgICAgICAgIGdyYW50X3R5cGU6ICd1cm46aWV0ZjpwYXJhbXM6b2F1dGg6Z3JhbnQtdHlwZTpkZXZpY2VfY29kZScsXG4gICAgICAgICAgZGV2aWNlX2NvZGU6IHRoaXMuZGV2aWNlX2NvZGUsXG4gICAgICAgIH0sXG4gICAgICAgIHsgY2xpZW50QXNzZXJ0aW9uUGF5bG9hZDogdGhpcy4jY2xpZW50QXNzZXJ0aW9uUGF5bG9hZCwgRFBvUDogdGhpcy4jRFBvUCB9LFxuICAgICAgKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHN3aXRjaCAoZXJyIGluc3RhbmNlb2YgT1BFcnJvciAmJiBlcnIuZXJyb3IpIHtcbiAgICAgICAgY2FzZSAnc2xvd19kb3duJzpcbiAgICAgICAgICB0aGlzLiNpbnRlcnZhbCArPSA1MDAwO1xuICAgICAgICBjYXNlICdhdXRob3JpemF0aW9uX3BlbmRpbmcnOlxuICAgICAgICAgIHJldHVybiB0aGlzLnBvbGwoeyBzaWduYWwgfSk7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmICgnaWRfdG9rZW4nIGluIHRva2Vuc2V0KSB7XG4gICAgICBhd2FpdCB0aGlzLiNjbGllbnQuZGVjcnlwdElkVG9rZW4odG9rZW5zZXQpO1xuICAgICAgYXdhaXQgdGhpcy4jY2xpZW50LnZhbGlkYXRlSWRUb2tlbih0b2tlbnNldCwgdW5kZWZpbmVkLCAndG9rZW4nLCB0aGlzLiNtYXhBZ2UpO1xuICAgIH1cblxuICAgIHJldHVybiB0b2tlbnNldDtcbiAgfVxuXG4gIGdldCBkZXZpY2VfY29kZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmVzcG9uc2UuZGV2aWNlX2NvZGU7XG4gIH1cblxuICBnZXQgdXNlcl9jb2RlKCkge1xuICAgIHJldHVybiB0aGlzLiNyZXNwb25zZS51c2VyX2NvZGU7XG4gIH1cblxuICBnZXQgdmVyaWZpY2F0aW9uX3VyaSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmVzcG9uc2UudmVyaWZpY2F0aW9uX3VyaTtcbiAgfVxuXG4gIGdldCB2ZXJpZmljYXRpb25fdXJpX2NvbXBsZXRlKCkge1xuICAgIHJldHVybiB0aGlzLiNyZXNwb25zZS52ZXJpZmljYXRpb25fdXJpX2NvbXBsZXRlO1xuICB9XG5cbiAgZ2V0IGV4cGlyZXNfaW4oKSB7XG4gICAgcmV0dXJuIE1hdGgubWF4LmFwcGx5KG51bGwsIFt0aGlzLiNleHBpcmVzX2F0IC0gbm93KCksIDBdKTtcbiAgfVxuXG4gIGV4cGlyZWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuZXhwaXJlc19pbiA9PT0gMDtcbiAgfVxuXG4gIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXG4gIFtpbnNwZWN0LmN1c3RvbV0oKSB7XG4gICAgcmV0dXJuIGAke3RoaXMuY29uc3RydWN0b3IubmFtZX0gJHtpbnNwZWN0KHRoaXMuI3Jlc3BvbnNlLCB7XG4gICAgICBkZXB0aDogSW5maW5pdHksXG4gICAgICBjb2xvcnM6IHByb2Nlc3Muc3Rkb3V0LmlzVFRZLFxuICAgICAgY29tcGFjdDogZmFsc2UsXG4gICAgICBzb3J0ZWQ6IHRydWUsXG4gICAgfSl9YDtcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IERldmljZUZsb3dIYW5kbGU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\n\nclass OPError extends Error {\n  constructor({ error_description, error, error_uri, session_state, state, scope }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n\n    Object.assign(\n      this,\n      { error },\n      error_description && { error_description },\n      error_uri && { error_uri },\n      state && { state },\n      scope && { scope },\n      session_state && { session_state },\n    );\n\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response,\n      });\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const { message, printf, response, ...rest } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response,\n        });\n      }\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nmodule.exports = {\n  OPError,\n  RPError,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(\n      `${easavs} must be configured on the issuer if ${easa} is not defined on a client`,\n    );\n  }\n}\n\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\n\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9hc3NlcnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxpQkFBaUIsU0FBUzs7QUFFMUIsaUJBQWlCLFNBQVM7QUFDMUIsa0JBQWtCLFNBQVM7QUFDM0Isb0JBQW9CLFNBQVM7O0FBRTdCO0FBQ0E7QUFDQSxTQUFTLFFBQVEsc0NBQXNDLE1BQU07QUFDN0Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwyQkFBMkIsVUFBVTtBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvYXNzZXJ0LmpzP2JjOTUiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYXNzZXJ0U2lnbmluZ0FsZ1ZhbHVlc1N1cHBvcnQoZW5kcG9pbnQsIGlzc3VlciwgcHJvcGVydGllcykge1xuICBpZiAoIWlzc3VlcltgJHtlbmRwb2ludH1fZW5kcG9pbnRgXSkgcmV0dXJuO1xuXG4gIGNvbnN0IGVhbSA9IGAke2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX21ldGhvZGA7XG4gIGNvbnN0IGVhc2EgPSBgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ2A7XG4gIGNvbnN0IGVhc2F2cyA9IGAke2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX3NpZ25pbmdfYWxnX3ZhbHVlc19zdXBwb3J0ZWRgO1xuXG4gIGlmIChwcm9wZXJ0aWVzW2VhbV0gJiYgcHJvcGVydGllc1tlYW1dLmVuZHNXaXRoKCdfand0JykgJiYgIXByb3BlcnRpZXNbZWFzYV0gJiYgIWlzc3VlcltlYXNhdnNdKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgIGAke2Vhc2F2c30gbXVzdCBiZSBjb25maWd1cmVkIG9uIHRoZSBpc3N1ZXIgaWYgJHtlYXNhfSBpcyBub3QgZGVmaW5lZCBvbiBhIGNsaWVudGAsXG4gICAgKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBhc3NlcnRJc3N1ZXJDb25maWd1cmF0aW9uKGlzc3VlciwgZW5kcG9pbnQpIHtcbiAgaWYgKCFpc3N1ZXJbZW5kcG9pbnRdKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgJHtlbmRwb2ludH0gbXVzdCBiZSBjb25maWd1cmVkIG9uIHRoZSBpc3N1ZXJgKTtcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgYXNzZXJ0U2lnbmluZ0FsZ1ZhbHVlc1N1cHBvcnQsXG4gIGFzc2VydElzc3VlckNvbmZpZ3VyYXRpb24sXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') =>\n    fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\n\nconst decode = (input) => Buffer.from(input, 'base64');\n\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLHFCQUFxQjtBQUNyQixxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanM/YjRiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZW5jb2RlO1xuaWYgKEJ1ZmZlci5pc0VuY29kaW5nKCdiYXNlNjR1cmwnKSkge1xuICBlbmNvZGUgPSAoaW5wdXQsIGVuY29kaW5nID0gJ3V0ZjgnKSA9PiBCdWZmZXIuZnJvbShpbnB1dCwgZW5jb2RpbmcpLnRvU3RyaW5nKCdiYXNlNjR1cmwnKTtcbn0gZWxzZSB7XG4gIGNvbnN0IGZyb21CYXNlNjQgPSAoYmFzZTY0KSA9PiBiYXNlNjQucmVwbGFjZSgvPS9nLCAnJykucmVwbGFjZSgvXFwrL2csICctJykucmVwbGFjZSgvXFwvL2csICdfJyk7XG4gIGVuY29kZSA9IChpbnB1dCwgZW5jb2RpbmcgPSAndXRmOCcpID0+XG4gICAgZnJvbUJhc2U2NChCdWZmZXIuZnJvbShpbnB1dCwgZW5jb2RpbmcpLnRvU3RyaW5nKCdiYXNlNjQnKSk7XG59XG5cbmNvbnN0IGRlY29kZSA9IChpbnB1dCkgPT4gQnVmZmVyLmZyb20oaW5wdXQsICdiYXNlNjQnKTtcblxubW9kdWxlLmV4cG9ydHMuZGVjb2RlID0gZGVjb2RlO1xubW9kdWxlLmV4cG9ydHMuZW5jb2RlID0gZW5jb2RlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value) => encodeURIComponent(value).replace(/%20/g, '+');\n\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(\n      this.issuer,\n      `${endpoint}_endpoint_auth_signing_alg_values_supported`,\n    );\n  }\n\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg =\n        Array.isArray(supported) && supported.find((signAlg) => /^HS(?:256|384|512)/.test(signAlg));\n    }\n\n    if (!alg) {\n      throw new RPError(\n        `failed to determine a JWS Algorithm to use for ${\n          this[`${endpoint}_endpoint_auth_method`]\n        } Client Assertion`,\n      );\n    }\n\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n      .setProtectedHeader({ alg })\n      .sign(this.secretForAlg(alg));\n  }\n\n  const keystore = await keystores.get(this);\n\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg =\n      Array.isArray(supported) &&\n      supported.find((signAlg) => keystore.get({ alg: signAlg, use: 'sig' }));\n  }\n\n  if (!alg) {\n    throw new RPError(\n      `failed to determine a JWS Algorithm to use for ${\n        this[`${endpoint}_endpoint_auth_method`]\n      } Client Assertion`,\n    );\n  }\n\n  const key = keystore.get({ alg, use: 'sig' });\n  if (!key) {\n    throw new RPError(\n      `no key found in client jwks to sign a client assertion with using alg ${alg}`,\n    );\n  }\n\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n    .setProtectedHeader({ alg, kid: key.jwk && key.jwk.kid })\n    .sign(await key.keyObject(alg));\n}\n\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return { form: { client_id: this.client_id } };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_post client authentication method requires a client_secret',\n        );\n      }\n      return { form: { client_id: this.client_id, client_secret: this.client_secret } };\n    case 'private_key_jwt':\n    case 'client_secret_jwt': {\n      const timestamp = now();\n\n      const assertion = await clientAssertion.call(this, endpoint, {\n        iat: timestamp,\n        exp: timestamp + 60,\n        jti: random(),\n        iss: this.client_id,\n        sub: this.client_id,\n        aud: this.issuer.issuer,\n        ...clientAssertionPayload,\n      });\n\n      return {\n        form: {\n          client_id: this.client_id,\n          client_assertion: assertion,\n          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',\n        },\n      };\n    }\n    case 'client_secret_basic': {\n      // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n      // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n      // > The client identifier is encoded using the\n      // > \"application/x-www-form-urlencoded\" encoding algorithm per\n      // > Appendix B, and the encoded value is used as the username; the client\n      // > password is encoded using the same algorithm and used as the\n      // > password.\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_basic client authentication method requires a client_secret',\n        );\n      }\n      const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n      const value = Buffer.from(encoded).toString('base64');\n      return { headers: { Authorization: `Basic ${value}` } };\n    }\n    default: {\n      throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n    }\n  }\n}\n\nfunction resolveResponseType() {\n  const { length, 0: value } = this.response_types;\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nfunction resolveRedirectUri() {\n  const { length, 0: value } = this.redirect_uris || [];\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nasync function authenticatedPost(\n  endpoint,\n  opts,\n  { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {},\n) {\n  const auth = await authFor.call(this, endpointAuthMethod, { clientAssertionPayload });\n  const requestOpts = merge(opts, auth);\n\n  const mTLS =\n    this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') ||\n    (endpoint === 'token' && this.tls_client_certificate_bound_access_tokens);\n\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n\n  return request.call(\n    this,\n    {\n      ...requestOpts,\n      method: 'POST',\n      url: targetUrl,\n      headers: {\n        ...(endpoint !== 'revocation'\n          ? {\n              Accept: 'application/json',\n            }\n          : undefined),\n        ...requestOpts.headers,\n      },\n    },\n    { mTLS, DPoP },\n  );\n}\n\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\n\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2NvbnN0cy5qcz9lY2QyIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEhUVFBfT1BUSU9OUyA9IFN5bWJvbCgpO1xuY29uc3QgQ0xPQ0tfVE9MRVJBTkNFID0gU3ltYm9sKCk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBDTE9DS19UT0xFUkFOQ0UsXG4gIEhUVFBfT1BUSU9OUyxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nmodule.exports = (token) => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n\n  const { 0: header, 1: payload, 2: signature, length } = token.split('.');\n\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature,\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWNvZGVfand0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLGdGQUFhOztBQUV2QztBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVLDhDQUE4Qzs7QUFFeEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvZGVjb2RlX2p3dC5qcz83NmVjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vYmFzZTY0dXJsJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gKHRva2VuKSA9PiB7XG4gIGlmICh0eXBlb2YgdG9rZW4gIT09ICdzdHJpbmcnIHx8ICF0b2tlbikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0pXVCBtdXN0IGJlIGEgc3RyaW5nJyk7XG4gIH1cblxuICBjb25zdCB7IDA6IGhlYWRlciwgMTogcGF5bG9hZCwgMjogc2lnbmF0dXJlLCBsZW5ndGggfSA9IHRva2VuLnNwbGl0KCcuJyk7XG5cbiAgaWYgKGxlbmd0aCA9PT0gNSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2VuY3J5cHRlZCBKV1RzIGNhbm5vdCBiZSBkZWNvZGVkJyk7XG4gIH1cblxuICBpZiAobGVuZ3RoICE9PSAzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdKV1RzIG11c3QgaGF2ZSB0aHJlZSBjb21wb25lbnRzJyk7XG4gIH1cblxuICB0cnkge1xuICAgIHJldHVybiB7XG4gICAgICBoZWFkZXI6IEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZShoZWFkZXIpKSxcbiAgICAgIHBheWxvYWQ6IEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZShwYXlsb2FkKSksXG4gICAgICBzaWduYXR1cmUsXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdKV1QgaXMgbWFsZm9ybWVkJyk7XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj) => JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvZGVlcF9jbG9uZS5qcz9kMjQ3Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZ2xvYmFsVGhpcy5zdHJ1Y3R1cmVkQ2xvbmUgfHwgKChvYmopID0+IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkob2JqKSkpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWZhdWx0cy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLG1CQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2RlZmF1bHRzLmpzPzE5MWEiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJy4vaXNfcGxhaW5fb2JqZWN0Jyk7XG5cbmZ1bmN0aW9uIGRlZmF1bHRzKGRlZXAsIHRhcmdldCwgLi4uc291cmNlcykge1xuICBmb3IgKGNvbnN0IHNvdXJjZSBvZiBzb3VyY2VzKSB7XG4gICAgaWYgKCFpc1BsYWluT2JqZWN0KHNvdXJjZSkpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhzb3VyY2UpKSB7XG4gICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgaWYgKi9cbiAgICAgIGlmIChrZXkgPT09ICdfX3Byb3RvX18nIHx8IGtleSA9PT0gJ2NvbnN0cnVjdG9yJykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmICh0eXBlb2YgdGFyZ2V0W2tleV0gPT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSB2YWx1ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGRlZXAgJiYgaXNQbGFpbk9iamVjdCh0YXJnZXRba2V5XSkgJiYgaXNQbGFpbk9iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgZGVmYXVsdHModHJ1ZSwgdGFyZ2V0W2tleV0sIHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGRlZmF1bHRzLmJpbmQodW5kZWZpbmVkLCBmYWxzZSk7XG5tb2R1bGUuZXhwb3J0cy5kZWVwID0gZGVmYXVsdHMuYmluZCh1bmRlZmluZWQsIHRydWUpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\n\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: (codeVerifier) =>\n    base64url.encode(createHash('sha256').update(codeVerifier).digest()),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsMEJBQTBCLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTs7QUFFcEQsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQWE7O0FBRXZDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzPzM3N2QiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjcmVhdGVIYXNoLCByYW5kb21CeXRlcyB9ID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbmNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vYmFzZTY0dXJsJyk7XG5cbmNvbnN0IHJhbmRvbSA9IChieXRlcyA9IDMyKSA9PiBiYXNlNjR1cmwuZW5jb2RlKHJhbmRvbUJ5dGVzKGJ5dGVzKSk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICByYW5kb20sXG4gIHN0YXRlOiByYW5kb20sXG4gIG5vbmNlOiByYW5kb20sXG4gIGNvZGVWZXJpZmllcjogcmFuZG9tLFxuICBjb2RlQ2hhbGxlbmdlOiAoY29kZVZlcmlmaWVyKSA9PlxuICAgIGJhc2U2NHVybC5lbmNvZGUoY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKGNvZGVWZXJpZmllcikuZGlnZXN0KCkpLFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nmodule.exports = util.types.isKeyObject || ((obj) => obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxrQkFBTTtBQUMzQixlQUFlLG1CQUFPLENBQUMsc0JBQVE7O0FBRS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvaXNfa2V5X29iamVjdC5qcz83MTk2Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5jb25zdCBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxubW9kdWxlLmV4cG9ydHMgPSB1dGlsLnR5cGVzLmlzS2V5T2JqZWN0IHx8ICgob2JqKSA9PiBvYmogJiYgb2JqIGluc3RhbmNlb2YgY3J5cHRvLktleU9iamVjdCk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a) => !!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanM/NmM1NyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IChhKSA9PiAhIWEgJiYgYS5jb25zdHJ1Y3RvciA9PT0gT2JqZWN0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx) => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({ max: 100 }));\n  }\n  return caches.get(ctx);\n};\n\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(\n      this,\n      (async () => {\n        const response = await request\n          .call(this, {\n            method: 'GET',\n            responseType: 'json',\n            url: this.jwks_uri,\n            headers: {\n              Accept: 'application/json, application/jwk-set+json',\n            },\n          })\n          .finally(() => {\n            inFlight.delete(this);\n          });\n        const jwks = processResponse(response);\n\n        const joseKeyStore = KeyStore.fromJWKS(jwks, { onlyPublic: true });\n        cache.set('throttle', true, 60 * 1000);\n        keystores.set(this, joseKeyStore);\n\n        return joseKeyStore;\n      })(),\n    );\n\n    return inFlight.get(this);\n  }\n\n  return keystore;\n}\n\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n  const cache = lrus(this);\n\n  const def = {\n    kid,\n    kty,\n    alg,\n    use,\n  };\n\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false,\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore,\n    });\n  }\n\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\n        \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n        def,\n      ],\n      jwks: keystore,\n    });\n  }\n\n  cache.set(defHash, true);\n\n  return keys;\n}\n\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nconst internal = Symbol();\n\nconst keyscore = (key, { alg, use }) => {\n  let score = 0;\n\n  if (alg && key.alg) {\n    score++;\n  }\n\n  if (use && key.use) {\n    score++;\n  }\n\n  return score;\n};\n\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\n\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n\n  switch (kty) {\n    case 'EC': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n\n      if (use === 'sig' || use === undefined) {\n        switch (crv) {\n          case 'P-256':\n          case 'P-384':\n            algs = algs.concat([`ES${crv.slice(-3)}`]);\n            break;\n          case 'P-521':\n            algs = algs.concat(['ES512']);\n            break;\n          case 'secp256k1':\n            if (jose.cryptoRuntime === 'node:crypto') {\n              algs = algs.concat(['ES256K']);\n            }\n            break;\n        }\n      }\n\n      return new Set(algs);\n    }\n    case 'OKP': {\n      return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n    }\n    case 'RSA': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n        if (jose.cryptoRuntime === 'node:crypto') {\n          algs = algs.concat(['RSA1_5']);\n        }\n      }\n\n      if (use === 'sig' || use === undefined) {\n        algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n      }\n\n      return new Set(algs);\n    }\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nmodule.exports = class KeyStore {\n  #keys;\n\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n\n  toJWKS() {\n    return {\n      keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } }) => jwk),\n    };\n  }\n\n  all({ alg, kid, use } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n\n    const kty = getKtyFromAlg(alg);\n\n    const search = { alg, use };\n    return this.filter((key) => {\n      let candidate = true;\n\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n\n  get(...args) {\n    return this.all(...args)[0];\n  }\n\n  static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n    if (\n      !isPlainObject(jwks) ||\n      !Array.isArray(jwks.keys) ||\n      jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n    ) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n\n    const keys = [];\n\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const { kty, kid, crv } = jwk;\n\n      let { alg, use } = jwk;\n\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n\n      keys.push({\n        jwk: { ...jwk, alg, use },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false,\n          });\n          return this.algorithms;\n        },\n      });\n    }\n\n    return new this(internal, keys);\n  }\n\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9rZXlzdG9yZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsOERBQU07O0FBRTNCLGNBQWMsbUJBQU8sQ0FBQyxrRkFBYztBQUNwQyxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEOztBQUVBLHlCQUF5QixVQUFVO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxjQUFjO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU8sK0JBQStCO0FBQzlEO0FBQ0E7O0FBRUEsUUFBUSxnQkFBZ0IsSUFBSTtBQUM1QjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEscUJBQXFCO0FBQ3JCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxnQ0FBZ0MsMENBQTBDLElBQUk7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLGNBQWMsZ0JBQWdCOztBQUU5QixZQUFZLFdBQVc7O0FBRXZCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxrQkFBa0I7QUFDakM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9rZXlzdG9yZS5qcz9jOTQxIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGpvc2UgPSByZXF1aXJlKCdqb3NlJyk7XG5cbmNvbnN0IGNsb25lID0gcmVxdWlyZSgnLi9kZWVwX2Nsb25lJyk7XG5jb25zdCBpc1BsYWluT2JqZWN0ID0gcmVxdWlyZSgnLi9pc19wbGFpbl9vYmplY3QnKTtcblxuY29uc3QgaW50ZXJuYWwgPSBTeW1ib2woKTtcblxuY29uc3Qga2V5c2NvcmUgPSAoa2V5LCB7IGFsZywgdXNlIH0pID0+IHtcbiAgbGV0IHNjb3JlID0gMDtcblxuICBpZiAoYWxnICYmIGtleS5hbGcpIHtcbiAgICBzY29yZSsrO1xuICB9XG5cbiAgaWYgKHVzZSAmJiBrZXkudXNlKSB7XG4gICAgc2NvcmUrKztcbiAgfVxuXG4gIHJldHVybiBzY29yZTtcbn07XG5cbmZ1bmN0aW9uIGdldEt0eUZyb21BbGcoYWxnKSB7XG4gIHN3aXRjaCAodHlwZW9mIGFsZyA9PT0gJ3N0cmluZycgJiYgYWxnLnNsaWNlKDAsIDIpKSB7XG4gICAgY2FzZSAnUlMnOlxuICAgIGNhc2UgJ1BTJzpcbiAgICAgIHJldHVybiAnUlNBJztcbiAgICBjYXNlICdFUyc6XG4gICAgICByZXR1cm4gJ0VDJztcbiAgICBjYXNlICdFZCc6XG4gICAgICByZXR1cm4gJ09LUCc7XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cbn1cblxuZnVuY3Rpb24gZ2V0QWxnb3JpdGhtcyh1c2UsIGFsZywga3R5LCBjcnYpIHtcbiAgLy8gRWQyNTUxOSwgRWQ0NDgsIGFuZCBzZWNwMjU2azEgYWx3YXlzIGhhdmUgXCJhbGdcIlxuICAvLyBPS1AgYWx3YXlzIGhhcyBcInVzZVwiXG4gIGlmIChhbGcpIHtcbiAgICByZXR1cm4gbmV3IFNldChbYWxnXSk7XG4gIH1cblxuICBzd2l0Y2ggKGt0eSkge1xuICAgIGNhc2UgJ0VDJzoge1xuICAgICAgbGV0IGFsZ3MgPSBbXTtcblxuICAgICAgaWYgKHVzZSA9PT0gJ2VuYycgfHwgdXNlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnRUNESC1FUycsICdFQ0RILUVTK0ExMjhLVycsICdFQ0RILUVTK0ExOTJLVycsICdFQ0RILUVTK0EyNTZLVyddKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHVzZSA9PT0gJ3NpZycgfHwgdXNlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgc3dpdGNoIChjcnYpIHtcbiAgICAgICAgICBjYXNlICdQLTI1Nic6XG4gICAgICAgICAgY2FzZSAnUC0zODQnOlxuICAgICAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFtgRVMke2Nydi5zbGljZSgtMyl9YF0pO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnUC01MjEnOlxuICAgICAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnRVM1MTInXSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdzZWNwMjU2azEnOlxuICAgICAgICAgICAgaWYgKGpvc2UuY3J5cHRvUnVudGltZSA9PT0gJ25vZGU6Y3J5cHRvJykge1xuICAgICAgICAgICAgICBhbGdzID0gYWxncy5jb25jYXQoWydFUzI1NksnXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gbmV3IFNldChhbGdzKTtcbiAgICB9XG4gICAgY2FzZSAnT0tQJzoge1xuICAgICAgcmV0dXJuIG5ldyBTZXQoWydFQ0RILUVTJywgJ0VDREgtRVMrQTEyOEtXJywgJ0VDREgtRVMrQTE5MktXJywgJ0VDREgtRVMrQTI1NktXJ10pO1xuICAgIH1cbiAgICBjYXNlICdSU0EnOiB7XG4gICAgICBsZXQgYWxncyA9IFtdO1xuXG4gICAgICBpZiAodXNlID09PSAnZW5jJyB8fCB1c2UgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICBhbGdzID0gYWxncy5jb25jYXQoWydSU0EtT0FFUCcsICdSU0EtT0FFUC0yNTYnLCAnUlNBLU9BRVAtMzg0JywgJ1JTQS1PQUVQLTUxMiddKTtcbiAgICAgICAgaWYgKGpvc2UuY3J5cHRvUnVudGltZSA9PT0gJ25vZGU6Y3J5cHRvJykge1xuICAgICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbJ1JTQTFfNSddKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAodXNlID09PSAnc2lnJyB8fCB1c2UgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICBhbGdzID0gYWxncy5jb25jYXQoWydQUzI1NicsICdQUzM4NCcsICdQUzUxMicsICdSUzI1NicsICdSUzM4NCcsICdSUzUxMiddKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIG5ldyBTZXQoYWxncyk7XG4gICAgfVxuICAgIGRlZmF1bHQ6XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ3VucmVhY2hhYmxlJyk7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBjbGFzcyBLZXlTdG9yZSB7XG4gICNrZXlzO1xuXG4gIGNvbnN0cnVjdG9yKGksIGtleXMpIHtcbiAgICBpZiAoaSAhPT0gaW50ZXJuYWwpIHRocm93IG5ldyBFcnJvcignaW52YWxpZCBjb25zdHJ1Y3RvciBjYWxsJyk7XG4gICAgdGhpcy4ja2V5cyA9IGtleXM7XG4gIH1cblxuICB0b0pXS1MoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGtleXM6IHRoaXMubWFwKCh7IGp3azogeyBkLCBwLCBxLCBkcCwgZHEsIHFpLCAuLi5qd2sgfSB9KSA9PiBqd2spLFxuICAgIH07XG4gIH1cblxuICBhbGwoeyBhbGcsIGtpZCwgdXNlIH0gPSB7fSkge1xuICAgIGlmICghdXNlIHx8ICFhbGcpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcigpO1xuICAgIH1cblxuICAgIGNvbnN0IGt0eSA9IGdldEt0eUZyb21BbGcoYWxnKTtcblxuICAgIGNvbnN0IHNlYXJjaCA9IHsgYWxnLCB1c2UgfTtcbiAgICByZXR1cm4gdGhpcy5maWx0ZXIoKGtleSkgPT4ge1xuICAgICAgbGV0IGNhbmRpZGF0ZSA9IHRydWU7XG5cbiAgICAgIGlmIChjYW5kaWRhdGUgJiYga3R5ICE9PSB1bmRlZmluZWQgJiYga2V5Lmp3ay5rdHkgIT09IGt0eSkge1xuICAgICAgICBjYW5kaWRhdGUgPSBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGNhbmRpZGF0ZSAmJiBraWQgIT09IHVuZGVmaW5lZCAmJiBrZXkuandrLmtpZCAhPT0ga2lkKSB7XG4gICAgICAgIGNhbmRpZGF0ZSA9IGZhbHNlO1xuICAgICAgfVxuXG4gICAgICBpZiAoY2FuZGlkYXRlICYmIHVzZSAhPT0gdW5kZWZpbmVkICYmIGtleS5qd2sudXNlICE9PSB1bmRlZmluZWQgJiYga2V5Lmp3ay51c2UgIT09IHVzZSkge1xuICAgICAgICBjYW5kaWRhdGUgPSBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGNhbmRpZGF0ZSAmJiBrZXkuandrLmFsZyAmJiBrZXkuandrLmFsZyAhPT0gYWxnKSB7XG4gICAgICAgIGNhbmRpZGF0ZSA9IGZhbHNlO1xuICAgICAgfSBlbHNlIGlmICgha2V5LmFsZ29yaXRobXMuaGFzKGFsZykpIHtcbiAgICAgICAgY2FuZGlkYXRlID0gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBjYW5kaWRhdGU7XG4gICAgfSkuc29ydCgoZmlyc3QsIHNlY29uZCkgPT4ga2V5c2NvcmUoc2Vjb25kLCBzZWFyY2gpIC0ga2V5c2NvcmUoZmlyc3QsIHNlYXJjaCkpO1xuICB9XG5cbiAgZ2V0KC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy5hbGwoLi4uYXJncylbMF07XG4gIH1cblxuICBzdGF0aWMgYXN5bmMgZnJvbUpXS1MoandrcywgeyBvbmx5UHVibGljID0gZmFsc2UsIG9ubHlQcml2YXRlID0gZmFsc2UgfSA9IHt9KSB7XG4gICAgaWYgKFxuICAgICAgIWlzUGxhaW5PYmplY3QoandrcykgfHxcbiAgICAgICFBcnJheS5pc0FycmF5KGp3a3Mua2V5cykgfHxcbiAgICAgIGp3a3Mua2V5cy5zb21lKChrKSA9PiAhaXNQbGFpbk9iamVjdChrKSB8fCAhKCdrdHknIGluIGspKVxuICAgICkge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignandrcyBtdXN0IGJlIGEgSlNPTiBXZWIgS2V5IFNldCBmb3JtYXR0ZWQgb2JqZWN0Jyk7XG4gICAgfVxuXG4gICAgY29uc3Qga2V5cyA9IFtdO1xuXG4gICAgZm9yIChsZXQgandrIG9mIGp3a3Mua2V5cykge1xuICAgICAgandrID0gY2xvbmUoandrKTtcbiAgICAgIGNvbnN0IHsga3R5LCBraWQsIGNydiB9ID0gandrO1xuXG4gICAgICBsZXQgeyBhbGcsIHVzZSB9ID0gandrO1xuXG4gICAgICBpZiAodHlwZW9mIGt0eSAhPT0gJ3N0cmluZycgfHwgIWt0eSkge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKHVzZSAhPT0gdW5kZWZpbmVkICYmIHVzZSAhPT0gJ3NpZycgJiYgdXNlICE9PSAnZW5jJykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiBhbGcgIT09ICdzdHJpbmcnICYmIGFsZyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuXG4gICAgICBpZiAodHlwZW9mIGtpZCAhPT0gJ3N0cmluZycgJiYga2lkICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGlmIChrdHkgPT09ICdFQycgJiYgdXNlID09PSAnc2lnJykge1xuICAgICAgICBzd2l0Y2ggKGNydikge1xuICAgICAgICAgIGNhc2UgJ1AtMjU2JzpcbiAgICAgICAgICAgIGFsZyA9ICdFUzI1Nic7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdQLTM4NCc6XG4gICAgICAgICAgICBhbGcgPSAnRVMzODQnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnUC01MjEnOlxuICAgICAgICAgICAgYWxnID0gJ0VTNTEyJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoY3J2ID09PSAnc2VjcDI1NmsxJykge1xuICAgICAgICB1c2UgPSAnc2lnJztcbiAgICAgICAgYWxnID0gJ0VTMjU2Syc7XG4gICAgICB9XG5cbiAgICAgIGlmIChrdHkgPT09ICdPS1AnKSB7XG4gICAgICAgIHN3aXRjaCAoY3J2KSB7XG4gICAgICAgICAgY2FzZSAnRWQyNTUxOSc6XG4gICAgICAgICAgY2FzZSAnRWQ0NDgnOlxuICAgICAgICAgICAgdXNlID0gJ3NpZyc7XG4gICAgICAgICAgICBhbGcgPSAnRWREU0EnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnWDI1NTE5JzpcbiAgICAgICAgICBjYXNlICdYNDQ4JzpcbiAgICAgICAgICAgIHVzZSA9ICdlbmMnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChhbGcgJiYgIXVzZSkge1xuICAgICAgICBzd2l0Y2ggKHRydWUpIHtcbiAgICAgICAgICBjYXNlIGFsZy5zdGFydHNXaXRoKCdFQ0RIJyk6XG4gICAgICAgICAgICB1c2UgPSAnZW5jJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgYWxnLnN0YXJ0c1dpdGgoJ1JTQScpOlxuICAgICAgICAgICAgdXNlID0gJ2VuYyc7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKG9ubHlQcml2YXRlICYmIChqd2sua3R5ID09PSAnb2N0JyB8fCAhandrLmQpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignandrcyBtdXN0IG9ubHkgY29udGFpbiBwcml2YXRlIGtleXMnKTtcbiAgICAgIH1cblxuICAgICAgaWYgKG9ubHlQdWJsaWMgJiYgKGp3ay5kIHx8IGp3ay5rKSkge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAga2V5cy5wdXNoKHtcbiAgICAgICAgandrOiB7IC4uLmp3aywgYWxnLCB1c2UgfSxcbiAgICAgICAgYXN5bmMga2V5T2JqZWN0KGFsZykge1xuICAgICAgICAgIGlmICh0aGlzW2FsZ10pIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzW2FsZ107XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc3Qga2V5T2JqZWN0ID0gYXdhaXQgam9zZS5pbXBvcnRKV0sodGhpcy5qd2ssIGFsZyk7XG4gICAgICAgICAgdGhpc1thbGddID0ga2V5T2JqZWN0O1xuICAgICAgICAgIHJldHVybiBrZXlPYmplY3Q7XG4gICAgICAgIH0sXG4gICAgICAgIGdldCBhbGdvcml0aG1zKCkge1xuICAgICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCAnYWxnb3JpdGhtcycsIHtcbiAgICAgICAgICAgIHZhbHVlOiBnZXRBbGdvcml0aG1zKHRoaXMuandrLnVzZSwgdGhpcy5qd2suYWxnLCB0aGlzLmp3ay5rdHksIHRoaXMuandrLmNydiksXG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gdGhpcy5hbGdvcml0aG1zO1xuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIG5ldyB0aGlzKGludGVybmFsLCBrZXlzKTtcbiAgfVxuXG4gIGZpbHRlciguLi5hcmdzKSB7XG4gICAgcmV0dXJuIHRoaXMuI2tleXMuZmlsdGVyKC4uLmFyZ3MpO1xuICB9XG5cbiAgZmluZCguLi5hcmdzKSB7XG4gICAgcmV0dXJuIHRoaXMuI2tleXMuZmluZCguLi5hcmdzKTtcbiAgfVxuXG4gIGV2ZXJ5KC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5ldmVyeSguLi5hcmdzKTtcbiAgfVxuXG4gIHNvbWUoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLnNvbWUoLi4uYXJncyk7XG4gIH1cblxuICBtYXAoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLm1hcCguLi5hcmdzKTtcbiAgfVxuXG4gIGZvckVhY2goLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLmZvckVhY2goLi4uYXJncyk7XG4gIH1cblxuICByZWR1Y2UoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLnJlZHVjZSguLi5hcmdzKTtcbiAgfVxuXG4gIHNvcnQoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLnNvcnQoLi4uYXJncyk7XG4gIH1cblxuICAqW1N5bWJvbC5pdGVyYXRvcl0oKSB7XG4gICAgZm9yIChjb25zdCBrZXkgb2YgdGhpcy4ja2V5cykge1xuICAgICAgeWllbGQga2V5O1xuICAgIH1cbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvbWVyZ2UuanM/MDZlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1BsYWluT2JqZWN0ID0gcmVxdWlyZSgnLi9pc19wbGFpbl9vYmplY3QnKTtcblxuZnVuY3Rpb24gbWVyZ2UodGFyZ2V0LCAuLi5zb3VyY2VzKSB7XG4gIGZvciAoY29uc3Qgc291cmNlIG9mIHNvdXJjZXMpIHtcbiAgICBpZiAoIWlzUGxhaW5PYmplY3Qoc291cmNlKSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHNvdXJjZSkpIHtcbiAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBpZiAqL1xuICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgICAgaWYgKGlzUGxhaW5PYmplY3QodGFyZ2V0W2tleV0pICYmIGlzUGxhaW5PYmplY3QodmFsdWUpKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gbWVyZ2UodGFyZ2V0W2tleV0sIHZhbHVlKTtcbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB0YXJnZXRba2V5XSA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB0YXJnZXQ7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gbWVyZ2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3BpY2suanM/YjlkMCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIHBpY2sob2JqZWN0LCAuLi5wYXRocykge1xuICBjb25zdCBvYmogPSB7fTtcbiAgZm9yIChjb25zdCBwYXRoIG9mIHBhdGhzKSB7XG4gICAgaWYgKG9iamVjdFtwYXRoXSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBvYmpbcGF0aF0gPSBvYmplY3RbcGF0aF07XG4gICAgfVxuICB9XG4gIHJldHVybiBvYmo7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\n\nconst throwAuthenticateErrors = (response) => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\n\nconst isStandardBodyError = (response) => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', { value: jsonbody, configurable: true });\n  } catch (err) {}\n\n  return result;\n};\n\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s, got: %i %s',\n          statusCode,\n          STATUS_CODES[statusCode],\n          response.statusCode,\n          STATUS_CODES[response.statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  if (body && !response.body) {\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s with body but no body was returned',\n          statusCode,\n          STATUS_CODES[statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  return response.body;\n}\n\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\n\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\n\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\n\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\n\nconst allowed = [\n  'agent',\n  'ca',\n  'cert',\n  'crl',\n  'headers',\n  'key',\n  'lookup',\n  'passphrase',\n  'pfx',\n  'timeout',\n];\n\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep(\n    {},\n    props.length ? pick(options, ...props) : options,\n    DEFAULT_HTTP_OPTIONS,\n  );\n};\n\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity',\n  },\n  timeout: 3500,\n});\n\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\n\nconst nonces = new LRU({ max: 100 });\n\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof(\n      {\n        htu: `${url.origin}${url.pathname}`,\n        htm: options.method || 'GET',\n        nonce: nonces.get(nonceKey),\n      },\n      DPoP,\n      accessToken,\n    );\n  }\n\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(\n      optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)),\n      ...allowed,\n    );\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({ form, responseType, json, body, ...opts } = opts);\n\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n\n    const parts = [];\n\n    for await (const part of response) {\n      parts.push(part);\n    }\n\n    if (parts.length) {\n      switch (responseType) {\n        case 'json': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              let value = Buffer.concat(parts);\n              try {\n                value = JSON.parse(value);\n              } catch (err) {\n                Object.defineProperty(err, 'response', { value: response });\n                throw err;\n              } finally {\n                Object.defineProperty(response, 'body', { value, configurable: true });\n              }\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        case undefined:\n        case 'buffer': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              const value = Buffer.concat(parts);\n              Object.defineProperty(response, 'body', { value, configurable: true });\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n\n    return response;\n  })()\n    .catch((err) => {\n      if (response) Object.defineProperty(err, 'response', { value: response });\n      throw err;\n    })\n    .finally(() => {\n      const dpopNonce = response && response.headers['dpop-nonce'];\n      if (dpopNonce && NQCHAR.test(dpopNonce)) {\n        nonces.set(nonceKey, dpopNonce);\n      }\n    });\n};\n\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = () => Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3VuaXhfdGltZXN0YW1wLmpzPzVkNzUiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSAoKSA9PiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBLHdCQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3dlYWtfY2FjaGUuanM/ZjY3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cy5rZXlzdG9yZXMgPSBuZXcgV2Vha01hcCgpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\n\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\n\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n\n  return output.split('#')[0];\n}\n\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\n\nmodule.exports = (wwwAuthenticate) => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n\n  return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvd3d3X2F1dGhlbnRpY2F0ZV9wYXJzZXIuanM/NWQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBSRUdFWFAgPSAvKFxcdyspPShcIlteXCJdKlwiKS9nO1xuXG5tb2R1bGUuZXhwb3J0cyA9ICh3d3dBdXRoZW50aWNhdGUpID0+IHtcbiAgY29uc3QgcGFyYW1zID0ge307XG4gIHRyeSB7XG4gICAgd2hpbGUgKFJFR0VYUC5leGVjKHd3d0F1dGhlbnRpY2F0ZSkgIT09IG51bGwpIHtcbiAgICAgIGlmIChSZWdFeHAuJDEgJiYgUmVnRXhwLiQyKSB7XG4gICAgICAgIHBhcmFtc1tSZWdFeHAuJDFdID0gUmVnRXhwLiQyLnNsaWNlKDEsIC0xKTtcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycikge31cblxuICByZXR1cm4gcGFyYW1zO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError,\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE,\n  },\n  generators,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxtQkFBTyxDQUFDLGtFQUFVO0FBQ2pDLFFBQVEsbUJBQW1CLEVBQUUsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQyxpQkFBaUIsbUJBQU8sQ0FBQyx3RkFBcUI7QUFDOUMsaUJBQWlCLG1CQUFPLENBQUMsd0VBQWE7QUFDdEMsUUFBUSxnQ0FBZ0MsRUFBRSxtQkFBTyxDQUFDLGtGQUFrQjtBQUNwRSxtQkFBbUIsbUJBQU8sQ0FBQywwRkFBc0I7QUFDakQsUUFBUSxjQUFjLEVBQUUsbUJBQU8sQ0FBQyxvRkFBbUI7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanM/M2QwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBJc3N1ZXIgPSByZXF1aXJlKCcuL2lzc3VlcicpO1xuY29uc3QgeyBPUEVycm9yLCBSUEVycm9yIH0gPSByZXF1aXJlKCcuL2Vycm9ycycpO1xuY29uc3QgU3RyYXRlZ3kgPSByZXF1aXJlKCcuL3Bhc3Nwb3J0X3N0cmF0ZWd5Jyk7XG5jb25zdCBUb2tlblNldCA9IHJlcXVpcmUoJy4vdG9rZW5fc2V0Jyk7XG5jb25zdCB7IENMT0NLX1RPTEVSQU5DRSwgSFRUUF9PUFRJT05TIH0gPSByZXF1aXJlKCcuL2hlbHBlcnMvY29uc3RzJyk7XG5jb25zdCBnZW5lcmF0b3JzID0gcmVxdWlyZSgnLi9oZWxwZXJzL2dlbmVyYXRvcnMnKTtcbmNvbnN0IHsgc2V0RGVmYXVsdHMgfSA9IHJlcXVpcmUoJy4vaGVscGVycy9yZXF1ZXN0Jyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBJc3N1ZXIsXG4gIFN0cmF0ZWd5LFxuICBUb2tlblNldCxcbiAgZXJyb3JzOiB7XG4gICAgT1BFcnJvcixcbiAgICBSUEVycm9yLFxuICB9LFxuICBjdXN0b206IHtcbiAgICBzZXRIdHRwT3B0aW9uc0RlZmF1bHRzOiBzZXREZWZhdWx0cyxcbiAgICBodHRwX29wdGlvbnM6IEhUVFBfT1BUSU9OUyxcbiAgICBjbG9ja190b2xlcmFuY2U6IENMT0NLX1RPTEVSQU5DRSxcbiAgfSxcbiAgZ2VuZXJhdG9ycyxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\n\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\n\nconst AAD_MULTITENANT_DISCOVERY = [\n  'https://login.microsoftonline.com/common/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration',\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic'],\n};\n\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (\n        meta[`${endpoint}_endpoint`] &&\n        meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined &&\n        meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined\n      ) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] =\n            meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] =\n            meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n\n    this.#metadata = new Map();\n\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    registry.set(this.issuer, this);\n\n    const Client = getClient(this, aadIssValidation);\n\n    Object.defineProperties(this, {\n      Client: { value: Client, enumerable: true },\n      FAPI1Client: { value: class FAPI1Client extends Client {}, enumerable: true },\n      FAPI2Client: { value: class FAPI2Client extends Client {}, enumerable: true },\n    });\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const { host } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: { resource, rel: 'http://openid.net/specs/connect/1.0/issuer' },\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n\n    const location =\n      Array.isArray(body.links) &&\n      body.links.find(\n        (link) =>\n          typeof link === 'object' &&\n          link.rel === 'http://openid.net/specs/connect/1.0/issuer' &&\n          link.href,\n      );\n\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body,\n      });\n    }\n\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body,\n      });\n    }\n\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n\n    const issuer = await this.discover(expectedIssuer);\n\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError(\n        'discovered issuer mismatch, expected %s, got: %s',\n        expectedIssuer,\n        issuer.issuer,\n      );\n    }\n    return issuer;\n  }\n\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL) =>\n        wellKnownUri.startsWith(discoveryURL),\n      ),\n    });\n  }\n\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({ ...parsed, pathname });\n  }\n}\n\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\n\nmodule.exports = new LRU({ max: 100 });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQyxxRkFBVzs7QUFFL0IsMkJBQTJCLFVBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZWJ0YS1sbXMvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzPzA2YmYiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTFJVID0gcmVxdWlyZSgnbHJ1LWNhY2hlJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gbmV3IExSVSh7IG1heDogMTAwIH0pO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\n\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\n\nfunction OpenIDConnectStrategy(\n  { client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {},\n  verify,\n) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n\n  this._extras = cloneDeep(extras);\n\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported)\n      ? this._issuer.code_challenge_methods_supported\n      : false;\n\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError(\n        'neither code_challenge_method supported by the client is supported by the issuer',\n      );\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\n\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n\n    const { 0: parameter, length } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || (length === 1 && parameter === 'iss')) {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options,\n      };\n\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(\n        format(\n          'did not find expected authorization request details in session, req.session[\"%s\"] is %j',\n          sessionKey,\n          session,\n        ),\n      );\n    }\n\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    } = session;\n\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options,\n    };\n\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    };\n\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n\n    const args = [tokenset, verified.bind(this)];\n\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message:\n            'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset,\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n\n    if (passReq) {\n      args.unshift(req);\n    }\n\n    this._verify(...args);\n    /* end authentication response */\n  })().catch((error) => {\n    if (\n      (error instanceof OPError &&\n        error.error !== 'server_error' &&\n        !error.error.startsWith('invalid')) ||\n      error instanceof RPError\n    ) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\n\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const { constructor, ...properties } = Object.getOwnPropertyDescriptors(\n      this.constructor.prototype,\n    );\n\n    Object.defineProperties(this, properties);\n  }\n\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\n\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLHdGQUFxQjtBQUMvQyxZQUFZLG1CQUFPLENBQUMsa0dBQTBCOztBQUU5QztBQUNBO0FBQ0E7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VidGEtbG1zLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL3Rva2VuX3NldC5qcz8xYWM2Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vaGVscGVycy9iYXNlNjR1cmwnKTtcbmNvbnN0IG5vdyA9IHJlcXVpcmUoJy4vaGVscGVycy91bml4X3RpbWVzdGFtcCcpO1xuXG5jbGFzcyBUb2tlblNldCB7XG4gIGNvbnN0cnVjdG9yKHZhbHVlcykge1xuICAgIE9iamVjdC5hc3NpZ24odGhpcywgdmFsdWVzKTtcbiAgICBjb25zdCB7IGNvbnN0cnVjdG9yLCAuLi5wcm9wZXJ0aWVzIH0gPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhcbiAgICAgIHRoaXMuY29uc3RydWN0b3IucHJvdG90eXBlLFxuICAgICk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0aGlzLCBwcm9wZXJ0aWVzKTtcbiAgfVxuXG4gIHNldCBleHBpcmVzX2luKHZhbHVlKSB7XG4gICAgdGhpcy5leHBpcmVzX2F0ID0gbm93KCkgKyBOdW1iZXIodmFsdWUpO1xuICB9XG5cbiAgZ2V0IGV4cGlyZXNfaW4oKSB7XG4gICAgcmV0dXJuIE1hdGgubWF4LmFwcGx5KG51bGwsIFt0aGlzLmV4cGlyZXNfYXQgLSBub3coKSwgMF0pO1xuICB9XG5cbiAgZXhwaXJlZCgpIHtcbiAgICByZXR1cm4gdGhpcy5leHBpcmVzX2luID09PSAwO1xuICB9XG5cbiAgY2xhaW1zKCkge1xuICAgIGlmICghdGhpcy5pZF90b2tlbikge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignaWRfdG9rZW4gbm90IHByZXNlbnQgaW4gVG9rZW5TZXQnKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShiYXNlNjR1cmwuZGVjb2RlKHRoaXMuaWRfdG9rZW4uc3BsaXQoJy4nKVsxXSkpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5TZXQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/lru-cache/index.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// A linked list to keep track of recently-used-ness\nconst Yallist = __webpack_require__(/*! yallist */ \"(rsc)/./node_modules/openid-client/node_modules/yallist/yallist.js\")\n\nconst MAX = Symbol('max')\nconst LENGTH = Symbol('length')\nconst LENGTH_CALCULATOR = Symbol('lengthCalculator')\nconst ALLOW_STALE = Symbol('allowStale')\nconst MAX_AGE = Symbol('maxAge')\nconst DISPOSE = Symbol('dispose')\nconst NO_DISPOSE_ON_SET = Symbol('noDisposeOnSet')\nconst LRU_LIST = Symbol('lruList')\nconst CACHE = Symbol('cache')\nconst UPDATE_AGE_ON_GET = Symbol('updateAgeOnGet')\n\nconst naiveLength = () => 1\n\n// lruList is a yallist where the head is the youngest\n// item, and the tail is the oldest.  the list contains the Hit\n// objects as the entries.\n// Each Hit object has a reference to its Yallist.Node.  This\n// never changes.\n//\n// cache is a Map (or PseudoMap) that matches the keys to\n// the Yallist.Node object.\nclass LRUCache {\n  constructor (options) {\n    if (typeof options === 'number')\n      options = { max: options }\n\n    if (!options)\n      options = {}\n\n    if (options.max && (typeof options.max !== 'number' || options.max < 0))\n      throw new TypeError('max must be a non-negative number')\n    // Kind of weird to have a default max of Infinity, but oh well.\n    const max = this[MAX] = options.max || Infinity\n\n    const lc = options.length || naiveLength\n    this[LENGTH_CALCULATOR] = (typeof lc !== 'function') ? naiveLength : lc\n    this[ALLOW_STALE] = options.stale || false\n    if (options.maxAge && typeof options.maxAge !== 'number')\n      throw new TypeError('maxAge must be a number')\n    this[MAX_AGE] = options.maxAge || 0\n    this[DISPOSE] = options.dispose\n    this[NO_DISPOSE_ON_SET] = options.noDisposeOnSet || false\n    this[UPDATE_AGE_ON_GET] = options.updateAgeOnGet || false\n    this.reset()\n  }\n\n  // resize the cache when the max changes.\n  set max (mL) {\n    if (typeof mL !== 'number' || mL < 0)\n      throw new TypeError('max must be a non-negative number')\n\n    this[MAX] = mL || Infinity\n    trim(this)\n  }\n  get max () {\n    return this[MAX]\n  }\n\n  set allowStale (allowStale) {\n    this[ALLOW_STALE] = !!allowStale\n  }\n  get allowStale () {\n    return this[ALLOW_STALE]\n  }\n\n  set maxAge (mA) {\n    if (typeof mA !== 'number')\n      throw new TypeError('maxAge must be a non-negative number')\n\n    this[MAX_AGE] = mA\n    trim(this)\n  }\n  get maxAge () {\n    return this[MAX_AGE]\n  }\n\n  // resize the cache when the lengthCalculator changes.\n  set lengthCalculator (lC) {\n    if (typeof lC !== 'function')\n      lC = naiveLength\n\n    if (lC !== this[LENGTH_CALCULATOR]) {\n      this[LENGTH_CALCULATOR] = lC\n      this[LENGTH] = 0\n      this[LRU_LIST].forEach(hit => {\n        hit.length = this[LENGTH_CALCULATOR](hit.value, hit.key)\n        this[LENGTH] += hit.length\n      })\n    }\n    trim(this)\n  }\n  get lengthCalculator () { return this[LENGTH_CALCULATOR] }\n\n  get length () { return this[LENGTH] }\n  get itemCount () { return this[LRU_LIST].length }\n\n  rforEach (fn, thisp) {\n    thisp = thisp || this\n    for (let walker = this[LRU_LIST].tail; walker !== null;) {\n      const prev = walker.prev\n      forEachStep(this, fn, walker, thisp)\n      walker = prev\n    }\n  }\n\n  forEach (fn, thisp) {\n    thisp = thisp || this\n    for (let walker = this[LRU_LIST].head; walker !== null;) {\n      const next = walker.next\n      forEachStep(this, fn, walker, thisp)\n      walker = next\n    }\n  }\n\n  keys () {\n    return this[LRU_LIST].toArray().map(k => k.key)\n  }\n\n  values () {\n    return this[LRU_LIST].toArray().map(k => k.value)\n  }\n\n  reset () {\n    if (this[DISPOSE] &&\n        this[LRU_LIST] &&\n        this[LRU_LIST].length) {\n      this[LRU_LIST].forEach(hit => this[DISPOSE](hit.key, hit.value))\n    }\n\n    this[CACHE] = new Map() // hash of items by key\n    this[LRU_LIST] = new Yallist() // list of items in order of use recency\n    this[LENGTH] = 0 // length of items in the list\n  }\n\n  dump () {\n    return this[LRU_LIST].map(hit =>\n      isStale(this, hit) ? false : {\n        k: hit.key,\n        v: hit.value,\n        e: hit.now + (hit.maxAge || 0)\n      }).toArray().filter(h => h)\n  }\n\n  dumpLru () {\n    return this[LRU_LIST]\n  }\n\n  set (key, value, maxAge) {\n    maxAge = maxAge || this[MAX_AGE]\n\n    if (maxAge && typeof maxAge !== 'number')\n      throw new TypeError('maxAge must be a number')\n\n    const now = maxAge ? Date.now() : 0\n    const len = this[LENGTH_CALCULATOR](value, key)\n\n    if (this[CACHE].has(key)) {\n      if (len > this[MAX]) {\n        del(this, this[CACHE].get(key))\n        return false\n      }\n\n      const node = this[CACHE].get(key)\n      const item = node.value\n\n      // dispose of the old one before overwriting\n      // split out into 2 ifs for better coverage tracking\n      if (this[DISPOSE]) {\n        if (!this[NO_DISPOSE_ON_SET])\n          this[DISPOSE](key, item.value)\n      }\n\n      item.now = now\n      item.maxAge = maxAge\n      item.value = value\n      this[LENGTH] += len - item.length\n      item.length = len\n      this.get(key)\n      trim(this)\n      return true\n    }\n\n    const hit = new Entry(key, value, len, now, maxAge)\n\n    // oversized objects fall out of cache automatically.\n    if (hit.length > this[MAX]) {\n      if (this[DISPOSE])\n        this[DISPOSE](key, value)\n\n      return false\n    }\n\n    this[LENGTH] += hit.length\n    this[LRU_LIST].unshift(hit)\n    this[CACHE].set(key, this[LRU_LIST].head)\n    trim(this)\n    return true\n  }\n\n  has (key) {\n    if (!this[CACHE].has(key)) return false\n    const hit = this[CACHE].get(key).value\n    return !isStale(this, hit)\n  }\n\n  get (key) {\n    return get(this, key, true)\n  }\n\n  peek (key) {\n    return get(this, key, false)\n  }\n\n  pop () {\n    const node = this[LRU_LIST].tail\n    if (!node)\n      return null\n\n    del(this, node)\n    return node.value\n  }\n\n  del (key) {\n    del(this, this[CACHE].get(key))\n  }\n\n  load (arr) {\n    // reset the cache\n    this.reset()\n\n    const now = Date.now()\n    // A previous serialized cache has the most recent items first\n    for (let l = arr.length - 1; l >= 0; l--) {\n      const hit = arr[l]\n      const expiresAt = hit.e || 0\n      if (expiresAt === 0)\n        // the item was created without expiration in a non aged cache\n        this.set(hit.k, hit.v)\n      else {\n        const maxAge = expiresAt - now\n        // dont add already expired items\n        if (maxAge > 0) {\n          this.set(hit.k, hit.v, maxAge)\n        }\n      }\n    }\n  }\n\n  prune () {\n    this[CACHE].forEach((value, key) => get(this, key, false))\n  }\n}\n\nconst get = (self, key, doUse) => {\n  const node = self[CACHE].get(key)\n  if (node) {\n    const hit = node.value\n    if (isStale(self, hit)) {\n      del(self, node)\n      if (!self[ALLOW_STALE])\n        return undefined\n    } else {\n      if (doUse) {\n        if (self[UPDATE_AGE_ON_GET])\n          node.value.now = Date.now()\n        self[LRU_LIST].unshiftNode(node)\n      }\n    }\n    return hit.value\n  }\n}\n\nconst isStale = (self, hit) => {\n  if (!hit || (!hit.maxAge && !self[MAX_AGE]))\n    return false\n\n  const diff = Date.now() - hit.now\n  return hit.maxAge ? diff > hit.maxAge\n    : self[MAX_AGE] && (diff > self[MAX_AGE])\n}\n\nconst trim = self => {\n  if (self[LENGTH] > self[MAX]) {\n    for (let walker = self[LRU_LIST].tail;\n      self[LENGTH] > self[MAX] && walker !== null;) {\n      // We know that we're about to delete this one, and also\n      // what the next least recently used key will be, so just\n      // go ahead and set it now.\n      const prev = walker.prev\n      del(self, walker)\n      walker = prev\n    }\n  }\n}\n\nconst del = (self, node) => {\n  if (node) {\n    const hit = node.value\n    if (self[DISPOSE])\n      self[DISPOSE](hit.key, hit.value)\n\n    self[LENGTH] -= hit.length\n    self[CACHE].delete(hit.key)\n    self[LRU_LIST].removeNode(node)\n  }\n}\n\nclass Entry {\n  constructor (key, value, length, now, maxAge) {\n    this.key = key\n    this.value = value\n    this.length = length\n    this.now = now\n    this.maxAge = maxAge || 0\n  }\n}\n\nconst forEachStep = (self, fn, node, thisp) => {\n  let hit = node.value\n  if (isStale(self, hit)) {\n    del(self, node)\n    if (!self[ALLOW_STALE])\n      hit = undefined\n  }\n  if (hit)\n    fn.call(thisp, hit.value, hit.key, self)\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/node_modules/yallist/iterator.js":
/*!*********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/yallist/iterator.js ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
eval("\nmodule.exports = function (Yallist) {\n  Yallist.prototype[Symbol.iterator] = function* () {\n    for (let walker = this.head; walker; walker = walker.next) {\n      yield walker.value\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9ub2RlX21vZHVsZXMveWFsbGlzdC9pdGVyYXRvci5qcyIsIm1hcHBpbmdzIjoiQUFBWTtBQUNaO0FBQ0E7QUFDQSxpQ0FBaUMsUUFBUTtBQUN6QztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlYnRhLWxtcy8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L25vZGVfbW9kdWxlcy95YWxsaXN0L2l0ZXJhdG9yLmpzP2M1NDQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIChZYWxsaXN0KSB7XG4gIFlhbGxpc3QucHJvdG90eXBlW1N5bWJvbC5pdGVyYXRvcl0gPSBmdW5jdGlvbiogKCkge1xuICAgIGZvciAobGV0IHdhbGtlciA9IHRoaXMuaGVhZDsgd2Fsa2VyOyB3YWxrZXIgPSB3YWxrZXIubmV4dCkge1xuICAgICAgeWllbGQgd2Fsa2VyLnZhbHVlXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/node_modules/yallist/iterator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/node_modules/yallist/yallist.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/yallist/yallist.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nmodule.exports = Yallist\n\nYallist.Node = Node\nYallist.create = Yallist\n\nfunction Yallist (list) {\n  var self = this\n  if (!(self instanceof Yallist)) {\n    self = new Yallist()\n  }\n\n  self.tail = null\n  self.head = null\n  self.length = 0\n\n  if (list && typeof list.forEach === 'function') {\n    list.forEach(function (item) {\n      self.push(item)\n    })\n  } else if (arguments.length > 0) {\n    for (var i = 0, l = arguments.length; i < l; i++) {\n      self.push(arguments[i])\n    }\n  }\n\n  return self\n}\n\nYallist.prototype.removeNode = function (node) {\n  if (node.list !== this) {\n    throw new Error('removing node which does not belong to this list')\n  }\n\n  var next = node.next\n  var prev = node.prev\n\n  if (next) {\n    next.prev = prev\n  }\n\n  if (prev) {\n    prev.next = next\n  }\n\n  if (node === this.head) {\n    this.head = next\n  }\n  if (node === this.tail) {\n    this.tail = prev\n  }\n\n  node.list.length--\n  node.next = null\n  node.prev = null\n  node.list = null\n\n  return next\n}\n\nYallist.prototype.unshiftNode = function (node) {\n  if (node === this.head) {\n    return\n  }\n\n  if (node.list) {\n    node.list.removeNode(node)\n  }\n\n  var head = this.head\n  node.list = this\n  node.next = head\n  if (head) {\n    head.prev = node\n  }\n\n  this.head = node\n  if (!this.tail) {\n    this.tail = node\n  }\n  this.length++\n}\n\nYallist.prototype.pushNode = function (node) {\n  if (node === this.tail) {\n    return\n  }\n\n  if (node.list) {\n    node.list.removeNode(node)\n  }\n\n  var tail = this.tail\n  node.list = this\n  node.prev = tail\n  if (tail) {\n    tail.next = node\n  }\n\n  this.tail = node\n  if (!this.head) {\n    this.head = node\n  }\n  this.length++\n}\n\nYallist.prototype.push = function () {\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    push(this, arguments[i])\n  }\n  return this.length\n}\n\nYallist.prototype.unshift = function () {\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    unshift(this, arguments[i])\n  }\n  return this.length\n}\n\nYallist.prototype.pop = function () {\n  if (!this.tail) {\n    return undefined\n  }\n\n  var res = this.tail.value\n  this.tail = this.tail.prev\n  if (this.tail) {\n    this.tail.next = null\n  } else {\n    this.head = null\n  }\n  this.length--\n  return res\n}\n\nYallist.prototype.shift = function () {\n  if (!this.head) {\n    return undefined\n  }\n\n  var res = this.head.value\n  this.head = this.head.next\n  if (this.head) {\n    this.head.prev = null\n  } else {\n    this.tail = null\n  }\n  this.length--\n  return res\n}\n\nYallist.prototype.forEach = function (fn, thisp) {\n  thisp = thisp || this\n  for (var walker = this.head, i = 0; walker !== null; i++) {\n    fn.call(thisp, walker.value, i, this)\n    walker = walker.next\n  }\n}\n\nYallist.prototype.forEachReverse = function (fn, thisp) {\n  thisp = thisp || this\n  for (var walker = this.tail, i = this.length - 1; walker !== null; i--) {\n    fn.call(thisp, walker.value, i, this)\n    walker = walker.prev\n  }\n}\n\nYallist.prototype.get = function (n) {\n  for (var i = 0, walker = this.head; walker !== null && i < n; i++) {\n    // abort out of the list early if we hit a cycle\n    walker = walker.next\n  }\n  if (i === n && walker !== null) {\n    return walker.value\n  }\n}\n\nYallist.prototype.getReverse = function (n) {\n  for (var i = 0, walker = this.tail; walker !== null && i < n; i++) {\n    // abort out of the list early if we hit a cycle\n    walker = walker.prev\n  }\n  if (i === n && walker !== null) {\n    return walker.value\n  }\n}\n\nYallist.prototype.map = function (fn, thisp) {\n  thisp = thisp || this\n  var res = new Yallist()\n  for (var walker = this.head; walker !== null;) {\n    res.push(fn.call(thisp, walker.value, this))\n    walker = walker.next\n  }\n  return res\n}\n\nYallist.prototype.mapReverse = function (fn, thisp) {\n  thisp = thisp || this\n  var res = new Yallist()\n  for (var walker = this.tail; walker !== null;) {\n    res.push(fn.call(thisp, walker.value, this))\n    walker = walker.prev\n  }\n  return res\n}\n\nYallist.prototype.reduce = function (fn, initial) {\n  var acc\n  var walker = this.head\n  if (arguments.length > 1) {\n    acc = initial\n  } else if (this.head) {\n    walker = this.head.next\n    acc = this.head.value\n  } else {\n    throw new TypeError('Reduce of empty list with no initial value')\n  }\n\n  for (var i = 0; walker !== null; i++) {\n    acc = fn(acc, walker.value, i)\n    walker = walker.next\n  }\n\n  return acc\n}\n\nYallist.prototype.reduceReverse = function (fn, initial) {\n  var acc\n  var walker = this.tail\n  if (arguments.length > 1) {\n    acc = initial\n  } else if (this.tail) {\n    walker = this.tail.prev\n    acc = this.tail.value\n  } else {\n    throw new TypeError('Reduce of empty list with no initial value')\n  }\n\n  for (var i = this.length - 1; walker !== null; i--) {\n    acc = fn(acc, walker.value, i)\n    walker = walker.prev\n  }\n\n  return acc\n}\n\nYallist.prototype.toArray = function () {\n  var arr = new Array(this.length)\n  for (var i = 0, walker = this.head; walker !== null; i++) {\n    arr[i] = walker.value\n    walker = walker.next\n  }\n  return arr\n}\n\nYallist.prototype.toArrayReverse = function () {\n  var arr = new Array(this.length)\n  for (var i = 0, walker = this.tail; walker !== null; i++) {\n    arr[i] = walker.value\n    walker = walker.prev\n  }\n  return arr\n}\n\nYallist.prototype.slice = function (from, to) {\n  to = to || this.length\n  if (to < 0) {\n    to += this.length\n  }\n  from = from || 0\n  if (from < 0) {\n    from += this.length\n  }\n  var ret = new Yallist()\n  if (to < from || to < 0) {\n    return ret\n  }\n  if (from < 0) {\n    from = 0\n  }\n  if (to > this.length) {\n    to = this.length\n  }\n  for (var i = 0, walker = this.head; walker !== null && i < from; i++) {\n    walker = walker.next\n  }\n  for (; walker !== null && i < to; i++, walker = walker.next) {\n    ret.push(walker.value)\n  }\n  return ret\n}\n\nYallist.prototype.sliceReverse = function (from, to) {\n  to = to || this.length\n  if (to < 0) {\n    to += this.length\n  }\n  from = from || 0\n  if (from < 0) {\n    from += this.length\n  }\n  var ret = new Yallist()\n  if (to < from || to < 0) {\n    return ret\n  }\n  if (from < 0) {\n    from = 0\n  }\n  if (to > this.length) {\n    to = this.length\n  }\n  for (var i = this.length, walker = this.tail; walker !== null && i > to; i--) {\n    walker = walker.prev\n  }\n  for (; walker !== null && i > from; i--, walker = walker.prev) {\n    ret.push(walker.value)\n  }\n  return ret\n}\n\nYallist.prototype.splice = function (start, deleteCount, ...nodes) {\n  if (start > this.length) {\n    start = this.length - 1\n  }\n  if (start < 0) {\n    start = this.length + start;\n  }\n\n  for (var i = 0, walker = this.head; walker !== null && i < start; i++) {\n    walker = walker.next\n  }\n\n  var ret = []\n  for (var i = 0; walker && i < deleteCount; i++) {\n    ret.push(walker.value)\n    walker = this.removeNode(walker)\n  }\n  if (walker === null) {\n    walker = this.tail\n  }\n\n  if (walker !== this.head && walker !== this.tail) {\n    walker = walker.prev\n  }\n\n  for (var i = 0; i < nodes.length; i++) {\n    walker = insert(this, walker, nodes[i])\n  }\n  return ret;\n}\n\nYallist.prototype.reverse = function () {\n  var head = this.head\n  var tail = this.tail\n  for (var walker = head; walker !== null; walker = walker.prev) {\n    var p = walker.prev\n    walker.prev = walker.next\n    walker.next = p\n  }\n  this.head = tail\n  this.tail = head\n  return this\n}\n\nfunction insert (self, node, value) {\n  var inserted = node === self.head ?\n    new Node(value, null, node, self) :\n    new Node(value, node, node.next, self)\n\n  if (inserted.next === null) {\n    self.tail = inserted\n  }\n  if (inserted.prev === null) {\n    self.head = inserted\n  }\n\n  self.length++\n\n  return inserted\n}\n\nfunction push (self, item) {\n  self.tail = new Node(item, self.tail, null, self)\n  if (!self.head) {\n    self.head = self.tail\n  }\n  self.length++\n}\n\nfunction unshift (self, item) {\n  self.head = new Node(item, null, self.head, self)\n  if (!self.tail) {\n    self.tail = self.head\n  }\n  self.length++\n}\n\nfunction Node (value, prev, next, list) {\n  if (!(this instanceof Node)) {\n    return new Node(value, prev, next, list)\n  }\n\n  this.list = list\n  this.value = value\n\n  if (prev) {\n    prev.next = this\n    this.prev = prev\n  } else {\n    this.prev = null\n  }\n\n  if (next) {\n    next.prev = this\n    this.next = next\n  } else {\n    this.next = null\n  }\n}\n\ntry {\n  // add if support for Symbol.iterator is present\n  __webpack_require__(/*! ./iterator.js */ \"(rsc)/./node_modules/openid-client/node_modules/yallist/iterator.js\")(Yallist)\n} catch (er) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/node_modules/yallist/yallist.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;