(()=>{var e={};e.id=702,e.ids=[702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},64117:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>l}),s(38256),s(22834),s(39285),s(35866);var t=s(23191),a=s(88716),n=s(37922),i=s.n(n),d=s(95231),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(r,o);let l=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38256)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,22834)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\dashboard\\page.tsx"],x="/dashboard/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},18519:(e,r,s)=>{Promise.resolve().then(s.bind(s,7022))},11660:(e,r,s)=>{Promise.resolve().then(s.bind(s,59530))},7022:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(10326),a=s(77109),n=s(35047);s(17577);var i=s(90434),d=s(16545),o=s(99837);function l({children:e}){let{data:r,status:s}=(0,a.useSession)(),l=(0,n.useRouter)(),c=(0,n.usePathname)();if("loading"===s)return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:t.jsx(d.gb,{size:"lg"})});if(!r)return null;let x=r.user?.role||"student";return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx("header",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,t.jsxs)(i.default,{href:"/",className:"flex items-center",children:[t.jsx("div",{className:"text-2xl font-bold text-primary",children:"WebTA"}),t.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"LMS"})]}),(0,t.jsxs)("nav",{className:"hidden md:flex space-x-1",children:[t.jsx(i.default,{href:"/dashboard/student",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard/student"===c?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard Học vi\xean"}),("instructor"===x||"admin"===x)&&t.jsx(i.default,{href:"/dashboard/instructor",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard/instructor"===c?"bg-primary text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard Giảng vi\xean"}),t.jsx(i.default,{href:"/courses",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",children:"Kh\xf3a học"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"hidden sm:block text-sm text-gray-600",children:["Xin ch\xe0o, ",r.user?.name||r.user?.email]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(i.default,{href:"/profile",className:"text-sm text-gray-600 hover:text-gray-900 px-2 py-1 rounded transition-colors",children:"Hồ sơ"}),t.jsx(o.z,{variant:"outline",size:"sm",onClick:()=>l.push("/api/auth/signout"),className:"text-sm",children:"Đăng xuất"})]})]})]})})}),t.jsx("main",{className:"flex-1",children:e})]})}},59530:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var t=s(10326),a=s(77109),n=s(35047);s(17577);var i=s(16545);function d(){let{data:e,status:r}=(0,a.useSession)();return(0,n.useRouter)(),t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx(i.gb,{size:"lg"}),t.jsx("p",{className:"mt-4 text-gray-600",children:"Đang chuyển hướng đến dashboard..."})]})})}},16545:(e,r,s)=>{"use strict";s.d(r,{gb:()=>d});var t=s(10326),a=s(79360),n=s(51223);let i=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function d({variant:e,size:r,className:s,text:a}){return t.jsx("div",{className:(0,n.cn)("flex items-center justify-center",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[t.jsx("div",{className:(0,n.cn)(i({variant:e,size:r}))}),a&&t.jsx("p",{className:"text-sm text-gray-600",children:a})]})})}},22834:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\dashboard\layout.tsx#default`)},38256:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\dashboard\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,105,826],()=>s(64117));module.exports=t})();