import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from './providers'
import { ConditionalLayout } from '@/components/layout/ConditionalLayout'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'WebTA LMS - <PERSON>ệ thống Quản lý Học tập',
  description: '<PERSON><PERSON> thống LMS toàn diện cho việc bán và quản lý khóa học ngoại ngữ với tính năng đánh giá tự động bằng AI',
  keywords: ['LMS', 'học tập', 'ngoại ngữ', 'AI', 'đánh giá'],
  authors: [{ name: 'WebTA Team' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi">
      <body className={inter.className}>
        <Providers session={null}>
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
        </Providers>
      </body>
    </html>
  )
}
