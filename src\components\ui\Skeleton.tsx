import { cn } from '@/lib/utils'

interface SkeletonProps {
  className?: string
}

export function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse rounded-md bg-gray-200',
        className
      )}
    />
  )
}

// Predefined skeleton components for common use cases
export function SkeletonText({ lines = 1, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className={cn(
            'h-4',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  )
}

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn('card-base card-padding-md space-y-4', className)}>
      <Skeleton className="h-48 w-full" />
      <div className="space-y-2">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <SkeletonText lines={2} />
      </div>
    </div>
  )
}

export function SkeletonCourseCard({ className }: { className?: string }) {
  return (
    <div className={cn('card-base overflow-hidden', className)}>
      {/* Thumbnail skeleton */}
      <Skeleton className="aspect-video w-full" />
      
      {/* Content skeleton */}
      <div className="p-4 space-y-3">
        {/* Title */}
        <Skeleton className="h-6 w-full" />
        
        {/* Instructor */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>
        
        {/* Description */}
        <SkeletonText lines={2} />
        
        {/* Stats */}
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-12" />
        </div>
        
        {/* Price */}
        <Skeleton className="h-6 w-20" />
      </div>
    </div>
  )
}

export function SkeletonDashboardStats({ className }: { className?: string }) {
  return (
    <div className={cn('dashboard-stats', className)}>
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="card-base card-padding-md">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-8 w-12" />
            </div>
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      ))}
    </div>
  )
}

export function SkeletonTable({ rows = 5, cols = 4, className }: { 
  rows?: number; 
  cols?: number; 
  className?: string 
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {/* Table header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>
        {Array.from({ length: cols }).map((_, i) => (
          <Skeleton key={i} className="h-6 w-full" />
        ))}
      </div>
      
      {/* Table rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>
          {Array.from({ length: cols }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-4 w-full" />
          ))}
        </div>
      ))}
    </div>
  )
}

export function SkeletonProfile({ className }: { className?: string }) {
  return (
    <div className={cn('card-base card-padding-lg', className)}>
      <div className="flex items-start gap-6">
        {/* Avatar */}
        <Skeleton className="h-24 w-24 rounded-full" />
        
        {/* Profile info */}
        <div className="flex-1 space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
          
          <SkeletonText lines={3} />
          
          <div className="flex gap-4">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      </div>
    </div>
  )
}

export function SkeletonChart({ className }: { className?: string }) {
  return (
    <div className={cn('card-base card-padding-md space-y-4', className)}>
      {/* Chart title */}
      <Skeleton className="h-6 w-48" />
      
      {/* Chart area */}
      <div className="space-y-2">
        {/* Y-axis labels and bars */}
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="flex items-center gap-4">
            <Skeleton className="h-4 w-12" />
            <Skeleton 
              className="h-6" 
              style={{ width: `${Math.random() * 60 + 20}%` }}
            />
          </div>
        ))}
      </div>
      
      {/* X-axis */}
      <div className="flex justify-between">
        {Array.from({ length: 7 }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-8" />
        ))}
      </div>
    </div>
  )
}

export function SkeletonList({ items = 5, className }: { 
  items?: number; 
  className?: string 
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      ))}
    </div>
  )
}

export function SkeletonNavigation({ className }: { className?: string }) {
  return (
    <div className={cn('flex items-center justify-between p-4', className)}>
      {/* Logo */}
      <Skeleton className="h-8 w-32" />
      
      {/* Navigation items */}
      <div className="hidden md:flex items-center gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-16" />
        ))}
      </div>
      
      {/* User menu */}
      <div className="flex items-center gap-4">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
    </div>
  )
}

// Loading page skeletons
export function SkeletonCoursePage({ className }: { className?: string }) {
  return (
    <div className={cn('container-7xl section-padding', className)}>
      {/* Header */}
      <div className="text-center mb-12">
        <Skeleton className="h-12 w-96 mx-auto mb-4" />
        <Skeleton className="h-6 w-128 mx-auto" />
      </div>
      
      {/* Filters */}
      <div className="flex gap-4 mb-8">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-10 w-24" />
        ))}
      </div>
      
      {/* Course grid */}
      <div className="course-grid">
        {Array.from({ length: 8 }).map((_, i) => (
          <SkeletonCourseCard key={i} />
        ))}
      </div>
    </div>
  )
}

export function SkeletonDashboardPage({ className }: { className?: string }) {
  return (
    <div className={cn('container-7xl section-padding', className)}>
      {/* Header */}
      <div className="mb-8">
        <Skeleton className="h-10 w-64 mb-2" />
        <Skeleton className="h-6 w-48" />
      </div>
      
      {/* Stats */}
      <SkeletonDashboardStats className="mb-8" />
      
      {/* Content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <SkeletonChart />
          <SkeletonTable />
        </div>
        <div className="space-y-6">
          <SkeletonList />
        </div>
      </div>
    </div>
  )
}
