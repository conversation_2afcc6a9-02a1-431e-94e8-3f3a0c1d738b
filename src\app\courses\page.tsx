'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Loading } from '@/components/ui/Loading'
import { AlertMessage } from '@/components/ui/Alert'
import { SkeletonCoursePage } from '@/components/ui/Skeleton'

interface Course {
  _id: string
  title: string
  slug: string
  shortDescription: string
  thumbnail?: string
  instructor: {
    profile: {
      firstName: string
      lastName: string
      avatar?: string
    }
  }
  category: {
    name: string
    slug: string
  }
  level: string
  language: string
  pricing: {
    basePrice: number
    currency: string
  }
  stats: {
    totalStudents: number
    averageRating: number
    totalRatings: number
  }
  createdAt: string
}

interface CoursesResponse {
  success: boolean
  data: {
    courses: Course[]
    pagination: {
      currentPage: number
      totalPages: number
      totalCount: number
      limit: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
  error?: string
}

// Component that uses useSearchParams
function CoursesContent() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  // Filter states
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '')
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '')
  const [selectedLevel, setSelectedLevel] = useState(searchParams.get('level') || '')
  const [selectedLanguage, setSelectedLanguage] = useState(searchParams.get('language') || '')
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'createdAt')
  const [sortOrder, setSortOrder] = useState(searchParams.get('sortOrder') || 'desc')
  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page') || '1'))

  // Fetch courses
  const fetchCourses = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        sortBy,
        sortOrder
      })
      
      if (searchQuery) params.append('search', searchQuery)
      if (selectedCategory) params.append('category', selectedCategory)
      if (selectedLevel) params.append('level', selectedLevel)
      if (selectedLanguage) params.append('language', selectedLanguage)
      
      const response = await fetch(`/api/courses?${params}`)
      const data: CoursesResponse = await response.json()
      
      if (data.success) {
        setCourses(data.data.courses)
        setPagination(data.data.pagination)
      } else {
        setError(data.error || 'Lỗi khi tải danh sách khóa học')
      }
    } catch (err) {
      setError('Lỗi kết nối server')
    } finally {
      setLoading(false)
    }
  }

  // Update URL params
  const updateURL = () => {
    const params = new URLSearchParams()
    if (searchQuery) params.append('search', searchQuery)
    if (selectedCategory) params.append('category', selectedCategory)
    if (selectedLevel) params.append('level', selectedLevel)
    if (selectedLanguage) params.append('language', selectedLanguage)
    if (sortBy !== 'createdAt') params.append('sortBy', sortBy)
    if (sortOrder !== 'desc') params.append('sortOrder', sortOrder)
    if (currentPage !== 1) params.append('page', currentPage.toString())
    
    const newURL = params.toString() ? `/courses?${params}` : '/courses'
    router.push(newURL, { scroll: false })
  }

  // Effects
  useEffect(() => {
    fetchCourses()
  }, [currentPage, sortBy, sortOrder])

  useEffect(() => {
    updateURL()
  }, [searchQuery, selectedCategory, selectedLevel, selectedLanguage, sortBy, sortOrder, currentPage])

  // Handlers
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchCourses()
  }

  const handleFilterChange = () => {
    setCurrentPage(1)
    fetchCourses()
  }

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return 'Miễn phí'
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency === 'VND' ? 'VND' : 'USD'
    }).format(price)
  }

  const formatRating = (rating: number, totalRatings: number) => {
    if (totalRatings === 0) return 'Chưa có đánh giá'
    return `${rating.toFixed(1)} ⭐ (${totalRatings} đánh giá)`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl font-bold text-gray-900">Khóa học</h1>
          <p className="mt-2 text-gray-600">
            Khám phá các khóa học ngoại ngữ chất lượng cao
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow p-6 sticky top-4">
              <h3 className="text-lg font-semibold mb-4">Bộ lọc</h3>
              
              {/* Search */}
              <form onSubmit={handleSearch} className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tìm kiếm
                </label>
                <div className="flex gap-2">
                  <Input
                    type="text"
                    placeholder="Tìm khóa học..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit" size="sm">
                    Tìm
                  </Button>
                </div>
              </form>

              {/* Category Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Danh mục
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value)
                    handleFilterChange()
                  }}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Tất cả danh mục</option>
                  <option value="english">Tiếng Anh</option>
                  <option value="chinese">Tiếng Trung</option>
                  <option value="japanese">Tiếng Nhật</option>
                </select>
              </div>

              {/* Level Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trình độ
                </label>
                <select
                  value={selectedLevel}
                  onChange={(e) => {
                    setSelectedLevel(e.target.value)
                    handleFilterChange()
                  }}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Tất cả trình độ</option>
                  <option value="beginner">Cơ bản</option>
                  <option value="intermediate">Trung cấp</option>
                  <option value="advanced">Nâng cao</option>
                </select>
              </div>

              {/* Language Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ngôn ngữ
                </label>
                <select
                  value={selectedLanguage}
                  onChange={(e) => {
                    setSelectedLanguage(e.target.value)
                    handleFilterChange()
                  }}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Tất cả ngôn ngữ</option>
                  <option value="english">Tiếng Anh</option>
                  <option value="chinese">Tiếng Trung</option>
                  <option value="japanese">Tiếng Nhật</option>
                </select>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Sort and Results Info */}
            <div className="flex justify-between items-center mb-6">
              <div className="text-gray-600">
                {pagination && (
                  <span>
                    Hiển thị {((pagination.currentPage - 1) * pagination.limit) + 1}-
                    {Math.min(pagination.currentPage * pagination.limit, pagination.totalCount)} 
                    trong tổng số {pagination.totalCount} khóa học
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-700">Sắp xếp:</label>
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [newSortBy, newSortOrder] = e.target.value.split('-')
                    setSortBy(newSortBy)
                    setSortOrder(newSortOrder)
                  }}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="createdAt-desc">Mới nhất</option>
                  <option value="createdAt-asc">Cũ nhất</option>
                  <option value="stats.totalStudents-desc">Nhiều học viên nhất</option>
                  <option value="stats.averageRating-desc">Đánh giá cao nhất</option>
                  <option value="pricing.basePrice-asc">Giá thấp nhất</option>
                  <option value="pricing.basePrice-desc">Giá cao nhất</option>
                </select>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <AlertMessage variant="error" message={error} className="mb-6" />
            )}

            {/* Loading State */}
            {loading && <SkeletonCoursePage />}

            {/* Courses Grid */}
            {!loading && courses.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                {courses.map((course) => (
                  <Card key={course._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <Link href={`/courses/${course.slug}`}>
                      <div className="aspect-video bg-gray-200 relative">
                        {course.thumbnail ? (
                          <Image
                            src={course.thumbnail}
                            alt={course.title}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full text-gray-400">
                            📚
                          </div>
                        )}
                      </div>
                      
                      <div className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="secondary" size="sm">
                            {course.category.name}
                          </Badge>
                          <Badge variant="outline" size="sm">
                            {course.level}
                          </Badge>
                        </div>
                        
                        <h3 className="font-semibold text-lg mb-2 line-clamp-2">
                          {course.title}
                        </h3>
                        
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {course.shortDescription}
                        </p>
                        
                        <div className="flex items-center gap-2 mb-3 text-sm text-gray-500">
                          <span>👨‍🏫</span>
                          <span>
                            {course.instructor.profile.firstName} {course.instructor.profile.lastName}
                          </span>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-semibold text-primary">
                              {formatPrice(course.pricing.basePrice, course.pricing.currency)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {formatRating(course.stats.averageRating, course.stats.totalRatings)}
                            </div>
                          </div>
                          
                          <div className="text-xs text-gray-500">
                            {course.stats.totalStudents} học viên
                          </div>
                        </div>
                      </div>
                    </Link>
                  </Card>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!loading && courses.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Không tìm thấy khóa học nào
                </h3>
                <p className="text-gray-600 mb-4">
                  Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
                </p>
                <Button
                  onClick={() => {
                    setSearchQuery('')
                    setSelectedCategory('')
                    setSelectedLevel('')
                    setSelectedLanguage('')
                    setCurrentPage(1)
                    fetchCourses()
                  }}
                >
                  Xóa bộ lọc
                </Button>
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex justify-center items-center gap-2">
                <Button
                  variant="outline"
                  disabled={!pagination.hasPrevPage}
                  onClick={() => setCurrentPage(currentPage - 1)}
                >
                  Trước
                </Button>
                
                <span className="px-4 py-2 text-sm text-gray-600">
                  Trang {pagination.currentPage} / {pagination.totalPages}
                </span>
                
                <Button
                  variant="outline"
                  disabled={!pagination.hasNextPage}
                  onClick={() => setCurrentPage(currentPage + 1)}
                >
                  Sau
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Main component with Suspense boundary
export default function CoursesPage() {
  return (
    <Suspense fallback={<SkeletonCoursePage />}>
      <CoursesContent />
    </Suspense>
  )
}
