import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import { MongoDBAdapter } from '@next-auth/mongodb-adapter'
import { MongoClient } from 'mongodb'
import connectDB from './mongodb'
import User, { UserRole, UserStatus } from '@/models/User'
import bcrypt from 'bcryptjs'

// MongoDB client for NextAuth adapter
const client = new MongoClient(process.env.MONGODB_URI!)
const clientPromise = client.connect()

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('<PERSON><PERSON> và mật khẩu là bắt buộc')
        }

        try {
          await connectDB()
          
          // Find user with password field
          const user = await User.findOne({ 
            email: credentials.email.toLowerCase() 
          }).select('+password')

          if (!user) {
            throw new Error('Email hoặc mật khẩu không đúng')
          }

          // Check if account is locked
          if (user.isLocked()) {
            throw new Error('Tài khoản đã bị khóa do đăng nhập sai quá nhiều lần')
          }

          // Check if account is active
          if (user.status !== UserStatus.ACTIVE) {
            throw new Error('Tài khoản chưa được kích hoạt')
          }

          // Verify password
          const isPasswordValid = await user.comparePassword(credentials.password)
          
          if (!isPasswordValid) {
            // Increment login attempts
            await user.incrementLoginAttempts()
            throw new Error('Email hoặc mật khẩu không đúng')
          }

          // Reset login attempts on successful login
          if (user.loginAttempts > 0) {
            await user.updateOne({
              $unset: { loginAttempts: 1, lockUntil: 1 }
            })
          }

          // Update last login
          await user.updateOne({ lastLogin: new Date() })

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.profile.fullName,
            role: user.role,
            status: user.status,
            emailVerified: user.emailVerified,
            image: user.profile.avatar
          }
        } catch (error: any) {
          throw new Error(error.message || 'Đã xảy ra lỗi trong quá trình đăng nhập')
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code'
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/welcome'
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        token.role = user.role
        token.status = user.status
        token.emailVerified = user.emailVerified
      }

      // Return previous token if the access token has not expired yet
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.status = token.status as UserStatus
        session.user.emailVerified = token.emailVerified as boolean
      }
      return session
    },
    async signIn({ user, account, profile }) {
      // Allow OAuth without email verification
      if (account?.provider !== 'credentials') {
        return true
      }

      // For credentials, check email verification
      return user.emailVerified === true
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    }
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log(`User ${user.email} signed in with ${account?.provider}`)
    },
    async signOut({ session, token }) {
      console.log(`User signed out`)
    },
    async createUser({ user }) {
      console.log(`New user created: ${user.email}`)
    }
  },
  debug: process.env.NODE_ENV === 'development',
}

// Extend NextAuth types
declare module 'next-auth' {
  interface User {
    role: UserRole
    status: UserStatus
    emailVerified: boolean
  }

  interface Session {
    user: {
      id: string
      email: string
      name: string
      image?: string
      role: UserRole
      status: UserStatus
      emailVerified: boolean
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    status: UserStatus
    emailVerified: boolean
  }
}
