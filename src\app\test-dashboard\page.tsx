'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Loading } from '@/components/ui/Loading'

export default function TestDashboardPage() {
  const { data: session, status } = useSession()
  const [studentData, setStudentData] = useState<any>(null)
  const [instructorData, setInstructorData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testStudentAPI = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dashboard/student')
      const data = await response.json()
      
      if (data.success) {
        setStudentData(data.data)
      } else {
        setError(`Student API Error: ${data.error}`)
      }
    } catch (err) {
      setError(`Student API Error: ${err}`)
    } finally {
      setLoading(false)
    }
  }

  const testInstructorAPI = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dashboard/instructor')
      const data = await response.json()
      
      if (data.success) {
        setInstructorData(data.data)
      } else {
        setError(`Instructor API Error: ${data.error}`)
      }
    } catch (err) {
      setError(`Instructor API Error: ${err}`)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8">
          <h1 className="text-2xl font-bold mb-4">Dashboard Test</h1>
          <p className="text-red-600">Bạn cần đăng nhập để test dashboard</p>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Card className="p-8">
          <h1 className="text-3xl font-bold mb-6">Dashboard API Test</h1>
          
          {/* Session Info */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Session Information</h2>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-auto">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="flex gap-4 mb-8">
            <Button 
              onClick={testStudentAPI}
              disabled={loading}
            >
              {loading ? <Loading size="sm" /> : 'Test Student API'}
            </Button>
            
            <Button 
              onClick={testInstructorAPI}
              disabled={loading}
              variant="outline"
            >
              {loading ? <Loading size="sm" /> : 'Test Instructor API'}
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-red-600 mb-2">Error:</h3>
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}

          {/* Student Data */}
          {studentData && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">Student Dashboard Data:</h3>
              <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(studentData, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Instructor Data */}
          {instructorData && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">Instructor Dashboard Data:</h3>
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(instructorData, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Navigation Links */}
          <div className="mt-8 pt-8 border-t">
            <h3 className="text-lg font-semibold mb-4">Navigation Test:</h3>
            <div className="flex gap-4">
              <a 
                href="/dashboard/student"
                className="text-blue-600 hover:underline"
              >
                Go to Student Dashboard
              </a>
              <a 
                href="/dashboard/instructor"
                className="text-blue-600 hover:underline"
              >
                Go to Instructor Dashboard
              </a>
              <a 
                href="/dashboard"
                className="text-blue-600 hover:underline"
              >
                Go to Dashboard (Auto-redirect)
              </a>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
