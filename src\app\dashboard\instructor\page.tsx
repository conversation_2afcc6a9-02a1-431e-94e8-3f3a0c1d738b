'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Loading } from '@/components/ui/Loading'
import { AlertMessage } from '@/components/ui/Alert'

interface CourseData {
  _id: string
  title: string
  slug: string
  status: string
  thumbnail?: string
  stats: {
    totalStudents: number
    totalLessons: number
    averageRating: number
    totalRatings: number
  }
  pricing: {
    basePrice: number
    currency: string
  }
  createdAt: string
  publishedAt?: string
}

interface InstructorStats {
  totalCourses: number
  publishedCourses: number
  totalStudents: number
  totalRevenue: number
  averageRating: number
  totalRatings: number
}

interface RecentActivity {
  type: 'enrollment' | 'completion' | 'review'
  studentName: string
  courseName: string
  timestamp: string
  details?: any
}

export default function InstructorDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [courses, setCourses] = useState<CourseData[]>([])
  const [stats, setStats] = useState<InstructorStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'published' | 'draft'>('all')

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!session?.user?.id) return

      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/dashboard/instructor')
        const data = await response.json()

        if (data.success) {
          setCourses(data.data.courses)
          setStats(data.data.stats)
          setRecentActivity(data.data.recentActivity || [])
        } else {
          setError(data.error || 'Lỗi khi tải dữ liệu dashboard')
        }
      } catch (err) {
        setError('Lỗi kết nối server')
      } finally {
        setLoading(false)
      }
    }

    if (session?.user?.id) {
      fetchDashboardData()
    }
  }, [session])

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return 'Miễn phí'
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency === 'VND' ? 'VND' : 'USD'
    }).format(price)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="success">Đã xuất bản</Badge>
      case 'draft':
        return <Badge variant="secondary">Bản nháp</Badge>
      case 'archived':
        return <Badge variant="outline">Đã lưu trữ</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const filteredCourses = courses.filter(course => {
    if (filter === 'all') return true
    return course.status === filter
  })

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Dashboard Giảng viên
              </h1>
              <p className="mt-2 text-gray-600">
                Quản lý khóa học và theo dõi hiệu suất
              </p>
            </div>
            <Link href="/instructor/courses/create">
              <Button>
                Tạo khóa học mới
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6">
            <AlertMessage variant="error" message={error} />
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <span className="text-2xl">📚</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tổng khóa học</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCourses}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <span className="text-2xl">✅</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Đã xuất bản</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.publishedCourses}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <span className="text-2xl">👥</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tổng học viên</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalStudents}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <span className="text-2xl">💰</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Doanh thu</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPrice(stats.totalRevenue, 'VND')}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <span className="text-2xl">⭐</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Đánh giá TB</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.averageRating > 0 ? stats.averageRating.toFixed(1) : '0.0'}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <span className="text-2xl">📝</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Lượt đánh giá</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalRatings}</p>
                </div>
              </div>
            </Card>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Courses Section */}
          <div className="lg:col-span-2">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Khóa học của tôi</h2>
              
              <div className="flex gap-2">
                {[
                  { key: 'all', label: 'Tất cả' },
                  { key: 'published', label: 'Đã xuất bản' },
                  { key: 'draft', label: 'Bản nháp' }
                ].map((option) => (
                  <Button
                    key={option.key}
                    variant={filter === option.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilter(option.key as any)}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {filteredCourses.length > 0 ? (
              <div className="space-y-4">
                {filteredCourses.map((course) => (
                  <Card key={course._id} className="p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0 relative overflow-hidden">
                        {course.thumbnail ? (
                          <Image
                            src={course.thumbnail}
                            alt={course.title}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full text-gray-400 text-2xl">
                            📚
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-lg text-gray-900 mb-1">
                              {course.title}
                            </h3>
                            <div className="flex items-center gap-2 mb-2">
                              {getStatusBadge(course.status)}
                              <span className="text-sm text-gray-500">
                                {formatPrice(course.pricing.basePrice, course.pricing.currency)}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            <Link href={`/instructor/courses/${course.slug}/edit`}>
                              <Button variant="outline" size="sm">
                                Chỉnh sửa
                              </Button>
                            </Link>
                            <Link href={`/courses/${course.slug}`}>
                              <Button variant="outline" size="sm">
                                Xem
                              </Button>
                            </Link>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">{course.stats.totalStudents}</span>
                            <span className="ml-1">học viên</span>
                          </div>
                          <div>
                            <span className="font-medium">{course.stats.totalLessons}</span>
                            <span className="ml-1">bài học</span>
                          </div>
                          <div>
                            <span className="font-medium">
                              {course.stats.averageRating > 0 ? course.stats.averageRating.toFixed(1) : '0.0'}
                            </span>
                            <span className="ml-1">⭐ ({course.stats.totalRatings})</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {filter === 'all' 
                    ? 'Chưa có khóa học nào'
                    : filter === 'published'
                    ? 'Chưa có khóa học đã xuất bản'
                    : 'Chưa có bản nháp nào'
                  }
                </h3>
                <p className="text-gray-600 mb-4">
                  {filter === 'all'
                    ? 'Hãy tạo khóa học đầu tiên của bạn'
                    : 'Thử thay đổi bộ lọc để xem khóa học khác'
                  }
                </p>
                {filter === 'all' ? (
                  <Link href="/instructor/courses/create">
                    <Button>Tạo khóa học mới</Button>
                  </Link>
                ) : (
                  <Button onClick={() => setFilter('all')}>
                    Xem tất cả khóa học
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Recent Activity Sidebar */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Hoạt động gần đây</h3>
              
              {recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.slice(0, 10).map((activity, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-sm">
                          {activity.type === 'enrollment' ? '👥' : 
                           activity.type === 'completion' ? '✅' : '⭐'}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">
                          <span className="font-medium">{activity.studentName}</span>
                          {activity.type === 'enrollment' && ' đã đăng ký '}
                          {activity.type === 'completion' && ' đã hoàn thành '}
                          {activity.type === 'review' && ' đã đánh giá '}
                          <span className="font-medium">{activity.courseName}</span>
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(activity.timestamp).toLocaleDateString('vi-VN')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-2">📊</div>
                  <p className="text-gray-500 text-sm">
                    Chưa có hoạt động nào
                  </p>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
