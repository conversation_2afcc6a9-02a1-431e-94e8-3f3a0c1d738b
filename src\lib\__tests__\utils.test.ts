import { 
  cn, 
  formatCurrency, 
  validateEmail, 
  generateSlug, 
  truncateText, 
  formatDuration 
} from '../utils'

describe('Utils Functions', () => {
  describe('cn (className merger)', () => {
    it('should merge classes correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2')
    })

    it('should handle conditional classes', () => {
      expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3')
    })

    it('should resolve Tailwind conflicts', () => {
      expect(cn('p-2', 'p-4')).toBe('p-4')
    })
  })

  describe('formatCurrency', () => {
    it('should format VND currency correctly', () => {
      expect(formatCurrency(100000)).toBe('100.000 ₫')
    })

    it('should format USD currency correctly', () => {
      expect(formatCurrency(100, 'USD')).toBe('100,00 US$')
    })
  })

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('test@')).toBe(false)
      expect(validateEmail('@domain.com')).toBe(false)
    })
  })

  describe('generateSlug', () => {
    it('should generate slug from Vietnamese text', () => {
      expect(generateSlug('Khóa học tiếng Anh cơ bản')).toBe('khoa-hoc-tieng-anh-co-ban')
    })

    it('should handle special characters', () => {
      expect(generateSlug('Test & Special Characters!')).toBe('test-special-characters')
    })

    it('should handle multiple spaces', () => {
      expect(generateSlug('Multiple   Spaces   Here')).toBe('multiple-spaces-here')
    })
  })

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const longText = 'This is a very long text that should be truncated'
      expect(truncateText(longText, 20)).toBe('This is a very long...')
    })

    it('should not truncate short text', () => {
      const shortText = 'Short text'
      expect(truncateText(shortText, 20)).toBe('Short text')
    })
  })

  describe('formatDuration', () => {
    it('should format seconds to MM:SS', () => {
      expect(formatDuration(90)).toBe('1:30')
      expect(formatDuration(30)).toBe('0:30')
    })

    it('should format seconds to HH:MM:SS', () => {
      expect(formatDuration(3661)).toBe('1:01:01')
      expect(formatDuration(7200)).toBe('2:00:00')
    })
  })
})
