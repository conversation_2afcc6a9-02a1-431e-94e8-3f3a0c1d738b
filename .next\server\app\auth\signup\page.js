(()=>{var e={};e.id=271,e.ids=[271],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},86949:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d}),s(93722),s(39285),s(35866);var t=s(23191),a=s(88716),n=s(37922),i=s.n(n),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let d=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93722)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\auth\\signup\\page.tsx"],m="/auth/signup/page",u={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42989:(e,r,s)=>{Promise.resolve().then(s.bind(s,12058))},12058:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(10326),a=s(17577),n=s(35047),i=s(90434),l=s(99837),o=s(89175),d=s(158),c=s(8555),m=s(16545);function u(){let e=(0,n.useRouter)(),[r,s]=(0,a.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"student"}),[u,h]=(0,a.useState)({}),[x,p]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),b=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t})),u[r]&&h(e=>({...e,[r]:""}))},v=()=>{let e={};return r.firstName.trim()||(e.firstName="T\xean l\xe0 bắt buộc"),r.lastName.trim()||(e.lastName="Họ l\xe0 bắt buộc"),r.email?/\S+@\S+\.\S+/.test(r.email)||(e.email="Email kh\xf4ng hợp lệ"):e.email="Email l\xe0 bắt buộc",r.password?r.password.length<8?e.password="Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(r.password)||(e.password="Mật khẩu phải chứa \xedt nhất 1 chữ hoa, 1 chữ thường v\xe0 1 số"):e.password="Mật khẩu l\xe0 bắt buộc",r.confirmPassword?r.password!==r.confirmPassword&&(e.confirmPassword="Mật khẩu x\xe1c nhận kh\xf4ng khớp"):e.confirmPassword="X\xe1c nhận mật khẩu l\xe0 bắt buộc",h(e),0===Object.keys(e).length},j=async e=>{if(e.preventDefault(),v()){p(!0);try{let e=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:r.firstName.trim(),lastName:r.lastName.trim(),email:r.email.toLowerCase(),password:r.password,role:r.role})}),s=await e.json();if(e.ok)f(!0);else if(s.details){let e={};s.details.forEach(r=>{r.path&&r.path.length>0&&(e[r.path[0]]=r.message)}),h(e)}else h({general:s.error||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng k\xfd"})}catch(e){h({general:"Đ\xe3 xảy ra lỗi kh\xf4ng mong muốn"})}finally{p(!1)}}};return g?t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:t.jsx("div",{className:"max-w-md w-full",children:(0,t.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg text-center",children:[t.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 mb-4",children:t.jsx("svg",{className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),t.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Đăng k\xfd th\xe0nh c\xf4ng!"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"Ch\xfang t\xf4i đ\xe3 gửi email x\xe1c thực đến địa chỉ email của bạn. Vui l\xf2ng kiểm tra email v\xe0 nhấp v\xe0o li\xean kết để k\xedch hoạt t\xe0i khoản."}),t.jsx(l.z,{onClick:()=>e.push("/auth/signin"),className:"w-full",children:"Đến trang đăng nhập"})]})})}):t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary",children:t.jsx("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})}),t.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Tạo t\xe0i khoản mới"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Đ\xe3 c\xf3 t\xe0i khoản?"," ",t.jsx(i.default,{href:"/auth/signin",className:"font-medium text-primary hover:text-primary/80",children:"Đăng nhập ngay"})]})]}),(0,t.jsxs)("div",{className:"bg-white py-8 px-6 shadow rounded-lg",children:[u.general&&t.jsx("div",{className:"mb-6",children:t.jsx(c.g7,{variant:"error",message:u.general})}),(0,t.jsxs)(d.l0,{onSubmit:j,children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)(d.Wi,{children:[t.jsx(d.lX,{htmlFor:"firstName",required:!0,children:"T\xean"}),t.jsx(o.I,{id:"firstName",name:"firstName",type:"text",autoComplete:"given-name",value:r.firstName,onChange:b,error:!!u.firstName,placeholder:"T\xean của bạn"}),t.jsx(d.Xq,{message:u.firstName})]}),(0,t.jsxs)(d.Wi,{children:[t.jsx(d.lX,{htmlFor:"lastName",required:!0,children:"Họ"}),t.jsx(o.I,{id:"lastName",name:"lastName",type:"text",autoComplete:"family-name",value:r.lastName,onChange:b,error:!!u.lastName,placeholder:"Họ của bạn"}),t.jsx(d.Xq,{message:u.lastName})]})]}),(0,t.jsxs)(d.Wi,{children:[t.jsx(d.lX,{htmlFor:"email",required:!0,children:"Email"}),t.jsx(o.I,{id:"email",name:"email",type:"email",autoComplete:"email",value:r.email,onChange:b,error:!!u.email,placeholder:"Nhập email của bạn"}),t.jsx(d.Xq,{message:u.email})]}),(0,t.jsxs)(d.Wi,{children:[t.jsx(d.lX,{htmlFor:"password",required:!0,children:"Mật khẩu"}),t.jsx(o.I,{id:"password",name:"password",type:"password",autoComplete:"new-password",value:r.password,onChange:b,error:!!u.password,placeholder:"Tạo mật khẩu"}),t.jsx(d.yv,{children:"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự, bao gồm chữ hoa, chữ thường v\xe0 số"}),t.jsx(d.Xq,{message:u.password})]}),(0,t.jsxs)(d.Wi,{children:[t.jsx(d.lX,{htmlFor:"confirmPassword",required:!0,children:"X\xe1c nhận mật khẩu"}),t.jsx(o.I,{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",value:r.confirmPassword,onChange:b,error:!!u.confirmPassword,placeholder:"Nhập lại mật khẩu"}),t.jsx(d.Xq,{message:u.confirmPassword})]}),(0,t.jsxs)(d.Wi,{children:[t.jsx(d.lX,{children:"Bạn l\xe0:"}),t.jsx(d.Ee,{name:"role",options:[{value:"student",label:"Học vi\xean - T\xf4i muốn học c\xe1c kh\xf3a học"},{value:"instructor",label:"Giảng vi\xean - T\xf4i muốn tạo v\xe0 b\xe1n kh\xf3a học"}],value:r.role,onChange:e=>{s(r=>({...r,role:e}))}})]}),t.jsx(l.z,{type:"submit",className:"w-full",disabled:x,children:x?t.jsx(m.gb,{size:"sm"}):"Tạo t\xe0i khoản"})]}),(0,t.jsxs)("p",{className:"mt-4 text-xs text-gray-500 text-center",children:["Bằng c\xe1ch tạo t\xe0i khoản, bạn đồng \xfd với"," ",t.jsx(i.default,{href:"/terms",className:"text-primary hover:text-primary/80",children:"Điều khoản sử dụng"})," ","v\xe0"," ",t.jsx(i.default,{href:"/privacy",className:"text-primary hover:text-primary/80",children:"Ch\xednh s\xe1ch bảo mật"})," ","của ch\xfang t\xf4i."]})]})]})})}},8555:(e,r,s)=>{"use strict";s.d(r,{g7:()=>u});var t=s(10326),a=s(17577),n=s(79360),i=s(51223);let l=(0,n.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:r,...s},a)=>t.jsx("div",{ref:a,role:"alert",className:(0,i.cn)(l({variant:r}),e),...s}));o.displayName="Alert";let d=a.forwardRef(({className:e,...r},s)=>t.jsx("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...r}));c.displayName="AlertDescription";let m={success:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function u({title:e,message:r,variant:s="info",onClose:a}){return(0,t.jsxs)(o,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[s],className:"relative",children:[m[s],(0,t.jsxs)("div",{className:"flex-1",children:[e&&t.jsx(d,{children:e}),t.jsx(c,{children:r})]}),a&&t.jsx("button",{onClick:a,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:t.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},158:(e,r,s)=>{"use strict";s.d(r,{Ee:()=>m,Wi:()=>l,Xq:()=>d,l0:()=>i,lX:()=>o,yv:()=>c});var t=s(10326),a=s(17577),n=s(51223);function i({className:e,children:r,...s}){return t.jsx("form",{className:(0,n.cn)("space-y-6",e),...s,children:r})}function l({children:e,className:r}){return t.jsx("div",{className:(0,n.cn)("space-y-2",r),children:e})}function o({className:e,children:r,required:s,...a}){return(0,t.jsxs)("label",{className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...a,children:[r,s&&t.jsx("span",{className:"text-red-500 ml-1",children:"*"})]})}function d({message:e,className:r}){return e?t.jsx("p",{className:(0,n.cn)("text-sm text-red-600",r),children:e}):null}function c({children:e,className:r}){return t.jsx("p",{className:(0,n.cn)("text-sm text-gray-500",r),children:e})}function m({name:e,options:r,value:s,onChange:a,error:i,className:l}){return t.jsx("div",{className:(0,n.cn)("space-y-2",l),children:r.map(r=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"radio",id:`${e}-${r.value}`,name:e,value:r.value,checked:s===r.value,onChange:e=>a?.(e.target.value),disabled:r.disabled,className:(0,n.cn)("h-4 w-4 text-primary focus:ring-primary focus:ring-2",i&&"border-red-500")}),t.jsx("label",{htmlFor:`${e}-${r.value}`,className:(0,n.cn)("text-sm font-medium leading-none",r.disabled&&"opacity-50 cursor-not-allowed"),children:r.label})]},r.value))})}a.forwardRef(({className:e,error:r,...s},a)=>t.jsx("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",e),ref:a,...s})).displayName="Textarea",a.forwardRef(({className:e,error:r,placeholder:s,children:a,...i},l)=>(0,t.jsxs)("select",{className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",e),ref:l,...i,children:[s&&t.jsx("option",{value:"",disabled:!0,children:s}),a]})).displayName="Select",a.forwardRef(({className:e,label:r,error:s,id:a,...i},l)=>{let o=a||`checkbox-${Math.random().toString(36).substr(2,9)}`;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:o,className:(0,n.cn)("h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary focus:ring-2",s&&"border-red-500",e),ref:l,...i}),r&&t.jsx("label",{htmlFor:o,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r})]})}).displayName="Checkbox"},89175:(e,r,s)=>{"use strict";s.d(r,{I:()=>i});var t=s(10326),a=s(17577),n=s(51223);let i=a.forwardRef(({className:e,type:r,...s},a)=>t.jsx("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));i.displayName="Input"},16545:(e,r,s)=>{"use strict";s.d(r,{gb:()=>l});var t=s(10326),a=s(79360),n=s(51223);let i=(0,a.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function l({variant:e,size:r,className:s,text:a}){return t.jsx("div",{className:(0,n.cn)("flex items-center justify-center",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[t.jsx("div",{className:(0,n.cn)(i({variant:e,size:r}))}),a&&t.jsx("p",{className:"text-sm text-gray-600",children:a})]})})}},93722:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\auth\signup\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,105,826],()=>s(86949));module.exports=t})();