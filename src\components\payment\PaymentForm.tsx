'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Loading } from '@/components/ui/Loading'
import { AlertMessage } from '@/components/ui/Alert'
import { useToast } from '@/components/ui/Toast'

interface PaymentFormProps {
  courseId: string
  courseTitle: string
  amount: number
  currency: string
  onSuccess?: (enrollmentId: string) => void
  onError?: (error: string) => void
  onCancel?: () => void
}

interface PaymentIntent {
  clientSecret: string
  paymentIntentId: string
  paymentId: string
  amount: number
  currency: string
}

export function PaymentForm({
  courseId,
  courseTitle,
  amount,
  currency,
  onSuccess,
  onError,
  onCancel
}: PaymentFormProps) {
  const { data: session } = useSession()
  const { addToast } = useToast()
  
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null)
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'activation_code'>('card')
  const [activationCode, setActivationCode] = useState('')

  // Create payment intent when component mounts
  useEffect(() => {
    if (paymentMethod === 'card' && !paymentIntent) {
      createPaymentIntent()
    }
  }, [paymentMethod])

  const createPaymentIntent = async () => {
    if (!session?.user?.id) {
      setError('Vui lòng đăng nhập để thanh toán')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/payment/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          courseId,
          currency
        })
      })

      const data = await response.json()

      if (data.success) {
        setPaymentIntent(data.data)
      } else {
        setError(data.error || 'Lỗi khi tạo payment intent')
        if (onError) {
          onError(data.error || 'Lỗi khi tạo payment intent')
        }
      }
    } catch (err) {
      const errorMessage = 'Lỗi kết nối server'
      setError(errorMessage)
      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleCardPayment = async () => {
    if (!paymentIntent) {
      setError('Payment intent chưa được tạo')
      return
    }

    try {
      setProcessing(true)
      setError(null)

      // In a real implementation, you would use Stripe Elements here
      // For now, we'll simulate a successful payment
      
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Simulate successful payment (90% success rate)
      const isSuccess = Math.random() > 0.1
      
      if (isSuccess) {
        addToast({
          message: 'Thanh toán thành công!',
          variant: 'success'
        })
        
        if (onSuccess) {
          onSuccess('enrollment_id_placeholder')
        }
      } else {
        throw new Error('Thanh toán thất bại. Vui lòng thử lại.')
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi thanh toán'
      setError(errorMessage)
      addToast({
        message: errorMessage,
        variant: 'error'
      })
      
      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setProcessing(false)
    }
  }

  const handleActivationCode = async () => {
    if (!activationCode.trim()) {
      setError('Vui lòng nhập mã kích hoạt')
      return
    }

    try {
      setProcessing(true)
      setError(null)

      const response = await fetch(`/api/courses/${courseId}/enroll`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'activation_code',
          activationCode: activationCode.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        addToast({
          message: 'Đăng ký khóa học thành công!',
          variant: 'success'
        })
        
        if (onSuccess) {
          onSuccess(data.data._id)
        }
      } else {
        setError(data.error || 'Mã kích hoạt không hợp lệ')
        if (onError) {
          onError(data.error || 'Mã kích hoạt không hợp lệ')
        }
      }
    } catch (err) {
      const errorMessage = 'Lỗi kết nối server'
      setError(errorMessage)
      if (onError) {
        onError(errorMessage)
      }
    } finally {
      setProcessing(false)
    }
  }

  const formatPrice = (price: number, curr: string) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: curr === 'VND' ? 'VND' : 'USD'
    }).format(price)
  }

  if (!session) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Đăng nhập để thanh toán</h3>
          <p className="text-gray-600 mb-4">
            Bạn cần đăng nhập để có thể đăng ký khóa học này
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            Đăng nhập
          </Button>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Thanh toán khóa học</h3>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium">{courseTitle}</span>
            <span className="text-xl font-bold text-primary">
              {formatPrice(amount, currency)}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Method Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Phương thức thanh toán
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="card"
              checked={paymentMethod === 'card'}
              onChange={(e) => setPaymentMethod(e.target.value as 'card')}
              className="mr-2"
            />
            <span>Thanh toán bằng thẻ</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="activation_code"
              checked={paymentMethod === 'activation_code'}
              onChange={(e) => setPaymentMethod(e.target.value as 'activation_code')}
              className="mr-2"
            />
            <span>Mã kích hoạt</span>
          </label>
        </div>
      </div>

      {error && (
        <div className="mb-4">
          <AlertMessage variant="error" message={error} />
        </div>
      )}

      {/* Card Payment */}
      {paymentMethod === 'card' && (
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-4">
              <Loading size="sm" />
              <p className="text-sm text-gray-600 mt-2">Đang tạo payment intent...</p>
            </div>
          ) : paymentIntent ? (
            <div>
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg mb-4">
                <p className="text-sm text-blue-800">
                  💳 Trong phiên bản demo này, thanh toán sẽ được mô phỏng.
                  Trong production, đây sẽ là form Stripe Elements thực tế.
                </p>
              </div>
              
              <Button
                onClick={handleCardPayment}
                disabled={processing}
                className="w-full"
              >
                {processing ? (
                  <>
                    <Loading size="sm" />
                    <span className="ml-2">Đang xử lý...</span>
                  </>
                ) : (
                  `Thanh toán ${formatPrice(amount, currency)}`
                )}
              </Button>
            </div>
          ) : (
            <Button onClick={createPaymentIntent} className="w-full">
              Tạo payment intent
            </Button>
          )}
        </div>
      )}

      {/* Activation Code */}
      {paymentMethod === 'activation_code' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mã kích hoạt
            </label>
            <input
              type="text"
              value={activationCode}
              onChange={(e) => setActivationCode(e.target.value)}
              placeholder="Nhập mã kích hoạt của bạn"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={processing}
            />
          </div>
          
          <Button
            onClick={handleActivationCode}
            disabled={processing || !activationCode.trim()}
            className="w-full"
          >
            {processing ? (
              <>
                <Loading size="sm" />
                <span className="ml-2">Đang xử lý...</span>
              </>
            ) : (
              'Kích hoạt khóa học'
            )}
          </Button>
        </div>
      )}

      {/* Cancel Button */}
      {onCancel && (
        <div className="mt-4 pt-4 border-t">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={processing}
            className="w-full"
          >
            Hủy
          </Button>
        </div>
      )}
    </Card>
  )
}

export default PaymentForm
