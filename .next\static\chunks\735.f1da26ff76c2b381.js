"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[735],{9735:function(e,t,r){r.r(t),r.d(t,{DataTable:function(){return c}});var s=r(7437),l=r(2265),a=r(3448),n=r(6334),i=r(2827);function c(e){let{data:t,columns:r,className:c,loading:d=!1,emptyMessage:o="Kh\xf4ng c\xf3 dữ liệu",pageSize:u=10,showPagination:m=!0,showSearch:x=!0,searchPlaceholder:g="T\xecm kiếm...",onRowClick:h,rowClassName:p,stickyHeader:f=!1,maxHeight:b}=e,[y,v]=(0,l.useState)(null),[j,N]=(0,l.useState)(null),[k,w]=(0,l.useState)(""),[S,C]=(0,l.useState)(1),[M,z]=(0,l.useState)({}),L=(0,l.useMemo)(()=>{let e=t;return k&&(e=e.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(k.toLowerCase())))),Object.entries(M).forEach(t=>{let[r,s]=t;s&&(e=e.filter(e=>String(e[r]||"").toLowerCase().includes(s.toLowerCase())))}),e},[t,k,M]),I=(0,l.useMemo)(()=>y&&j?[...L].sort((e,t)=>{let r=e[y],s=t[y];if(r===s)return 0;let l=0;return l="number"==typeof r&&"number"==typeof s?r-s:String(r).localeCompare(String(s)),"asc"===j?l:-l}):L,[L,y,j]),_=(0,l.useMemo)(()=>{if(!m)return I;let e=(S-1)*u;return I.slice(e,e+u)},[I,S,u,m]),E=Math.ceil(I.length/u),T=e=>{let t=r.find(t=>t.key===e);(null==t?void 0:t.sortable)&&(y===e?"asc"===j?N("desc"):"desc"===j?(N(null),v(null)):N("asc"):(v(e),N("asc")))},H=(e,t)=>{z(r=>({...r,[e]:t})),C(1)},O=e=>y!==e?"↕️":"asc"===j?"↑":"desc"===j?"↓":"↕️",q=(e,t,r)=>{let s=t[e.key];return e.render?e.render(s,t,r):String(s||"")};return d?(0,s.jsx)("div",{className:(0,a.cn)("border rounded-lg",c),children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Đang tải dữ liệu..."})]})}):(0,s.jsxs)("div",{className:(0,a.cn)("border rounded-lg overflow-hidden",c),children:[x&&(0,s.jsx)("div",{className:"p-4 border-b bg-gray-50",children:(0,s.jsx)(i.I,{type:"text",placeholder:g,value:k,onChange:e=>{w(e.target.value),C(1)},className:"max-w-sm"})}),(0,s.jsx)("div",{className:"overflow-auto",style:{maxHeight:b},children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:(0,a.cn)("bg-gray-50",f&&"sticky top-0 z-10"),children:(0,s.jsx)("tr",{children:r.map(e=>(0,s.jsxs)("th",{className:(0,a.cn)("px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b",e.sortable&&"cursor-pointer hover:bg-gray-100","center"===e.align&&"text-center","right"===e.align&&"text-right",e.className),style:{width:e.width},onClick:()=>e.sortable&&T(String(e.key)),children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:e.title}),e.sortable&&(0,s.jsx)("span",{className:"text-gray-400",children:O(String(e.key))})]}),e.filterable&&(0,s.jsx)("div",{className:"mt-2",onClick:e=>e.stopPropagation(),children:(0,s.jsx)(i.I,{type:"text",placeholder:"Lọc...",value:M[String(e.key)]||"",onChange:t=>H(String(e.key),t.target.value),className:"text-xs h-6"})})]},String(e.key)))})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===_.length?(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:r.length,className:"px-4 py-8 text-center text-gray-500",children:o})}):_.map((e,t)=>(0,s.jsx)("tr",{className:(0,a.cn)("hover:bg-gray-50 transition-colors",h&&"cursor-pointer",null==p?void 0:p(e,t)),onClick:()=>null==h?void 0:h(e,t),children:r.map(r=>(0,s.jsx)("td",{className:(0,a.cn)("px-4 py-3 whitespace-nowrap text-sm text-gray-900","center"===r.align&&"text-center","right"===r.align&&"text-right",r.className),children:q(r,e,t)},String(r.key)))},t))})]})}),m&&E>1&&(0,s.jsxs)("div",{className:"px-4 py-3 border-t bg-gray-50 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Hiển thị ",(S-1)*u+1," đến ",Math.min(S*u,I.length)," trong tổng số ",I.length," kết quả"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.z,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.max(e-1,1)),disabled:1===S,children:"Trước"}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,E)},(e,t)=>{let r;return r=E<=5?t+1:S<=3?t+1:S>=E-2?E-4+t:S-2+t,(0,s.jsx)(n.z,{variant:S===r?"default":"outline",size:"sm",onClick:()=>C(r),className:"w-8 h-8 p-0",children:r},r)})}),(0,s.jsx)(n.z,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.min(e+1,E)),disabled:S===E,children:"Sau"})]})]})]})}t.default=c},2827:function(e,t,r){r.d(t,{I:function(){return n}});var s=r(7437),l=r(2265),a=r(3448);let n=l.forwardRef((e,t)=>{let{className:r,type:l,...n}=e;return(0,s.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"}}]);