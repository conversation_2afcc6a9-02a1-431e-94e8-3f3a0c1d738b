#!/usr/bin/env node

/**
 * Phase 1 Testing Script for WebTA LMS
 * Tests all major features implemented in Phase 1
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing Phase 1 Features - WebTA LMS')
console.log('=' .repeat(50))

// Test results
let totalTests = 0
let passedTests = 0
const results = []

function test(name, condition, details = '') {
  totalTests++
  const passed = condition
  if (passed) passedTests++
  
  const status = passed ? '✅ PASS' : '❌ FAIL'
  console.log(`${status} ${name}`)
  if (details) console.log(`   ${details}`)
  
  results.push({ name, passed, details })
}

// 1. Database Models Tests
console.log('\n📊 1. Database Models')
console.log('-'.repeat(30))

test(
  'Category Model exists',
  fs.existsSync(path.join(__dirname, '../src/models/Category.ts')),
  'Hierarchical category system with parent/child relationships'
)

test(
  'Lesson Model exists',
  fs.existsSync(path.join(__dirname, '../src/models/Lesson.ts')),
  'Multi-type lesson support (video, audio, text, quiz, assignment)'
)

test(
  'Enrollment Model exists',
  fs.existsSync(path.join(__dirname, '../src/models/Enrollment.ts')),
  'Complete enrollment lifecycle with progress tracking'
)

test(
  'Payment Model exists',
  fs.existsSync(path.join(__dirname, '../src/models/Payment.ts')),
  'Comprehensive payment handling with multiple methods'
)

test(
  'Database Seeding Script exists',
  fs.existsSync(path.join(__dirname, '../scripts/seed-database.ts')),
  'Sample data generation for development'
)

// 2. Course Management System Tests
console.log('\n📚 2. Course Management System')
console.log('-'.repeat(30))

test(
  'Course API Routes exist',
  fs.existsSync(path.join(__dirname, '../src/app/api/courses/route.ts')),
  'CRUD operations with advanced filtering'
)

test(
  'Course Detail API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/courses/[id]/route.ts')),
  'Course detail with access control'
)

test(
  'Enrollment API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/courses/[id]/enroll/route.ts')),
  'Multi-type enrollment system'
)

test(
  'Course Listing Page exists',
  fs.existsSync(path.join(__dirname, '../src/app/courses/page.tsx')),
  'Responsive course listing with filters'
)

test(
  'Course Detail Page exists',
  fs.existsSync(path.join(__dirname, '../src/app/courses/[slug]/page.tsx')),
  'Detailed course view with enrollment flow'
)

// 3. Dashboard System Tests
console.log('\n👤 3. Dashboard System')
console.log('-'.repeat(30))

test(
  'Dashboard Layout exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/layout.tsx')),
  'Dashboard navigation and authentication'
)

test(
  'Dashboard Redirect exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/page.tsx')),
  'Role-based dashboard routing'
)

test(
  'Student Dashboard exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/student/page.tsx')),
  'Student progress tracking and course management'
)

test(
  'Instructor Dashboard exists',
  fs.existsSync(path.join(__dirname, '../src/app/dashboard/instructor/page.tsx')),
  'Instructor analytics and course management'
)

test(
  'Student Dashboard API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/dashboard/student/route.ts')),
  'Student analytics and progress data'
)

test(
  'Instructor Dashboard API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/dashboard/instructor/route.ts')),
  'Instructor performance metrics and analytics'
)

test(
  'Dashboard Test Page exists',
  fs.existsSync(path.join(__dirname, '../src/app/test-dashboard/page.tsx')),
  'Dashboard functionality testing interface'
)

// 4. File Upload System Tests
console.log('\n📁 4. File Upload System')
console.log('-'.repeat(30))

test(
  'Cloudinary Configuration exists',
  fs.existsSync(path.join(__dirname, '../src/lib/cloudinary.ts')),
  'Multi-format file support with CDN optimization'
)

test(
  'Upload API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/upload/route.ts')),
  'Secure file upload with validation'
)

test(
  'File Upload Component exists',
  fs.existsSync(path.join(__dirname, '../src/components/ui/FileUpload.tsx')),
  'Drag & drop interface with progress tracking'
)

// 5. Payment Integration Tests
console.log('\n💳 5. Payment Integration')
console.log('-'.repeat(30))

test(
  'Stripe Configuration exists',
  fs.existsSync(path.join(__dirname, '../src/lib/stripe.ts')),
  'Stripe integration with error handling'
)

test(
  'Payment Intent API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/payment/create-intent/route.ts')),
  'Payment intent creation and management'
)

test(
  'Payment Webhook API exists',
  fs.existsSync(path.join(__dirname, '../src/app/api/payment/webhook/route.ts')),
  'Webhook handling for payment events'
)

test(
  'Payment Form Component exists',
  fs.existsSync(path.join(__dirname, '../src/components/payment/PaymentForm.tsx')),
  'Payment UI with card and activation code support'
)

// 6. Configuration Tests
console.log('\n⚙️ 6. Configuration & Environment')
console.log('-'.repeat(30))

test(
  'Environment Template exists',
  fs.existsSync(path.join(__dirname, '../.env.example')),
  'Complete environment variables template'
)

// Check if .env.example contains required variables
const envExample = fs.readFileSync(path.join(__dirname, '../.env.example'), 'utf8')

test(
  'MongoDB configuration present',
  envExample.includes('MONGODB_URI'),
  'Database connection configuration'
)

test(
  'NextAuth configuration present',
  envExample.includes('NEXTAUTH_SECRET'),
  'Authentication configuration'
)

test(
  'Cloudinary configuration present',
  envExample.includes('CLOUDINARY_CLOUD_NAME'),
  'File upload service configuration'
)

test(
  'Stripe configuration present',
  envExample.includes('STRIPE_SECRET_KEY'),
  'Payment gateway configuration'
)

// 7. Package Dependencies Tests
console.log('\n📦 7. Dependencies & Packages')
console.log('-'.repeat(30))

const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'))

test(
  'Cloudinary dependency installed',
  packageJson.dependencies.cloudinary !== undefined,
  'File upload service integration'
)

test(
  'Stripe dependency installed',
  packageJson.dependencies.stripe !== undefined,
  'Payment processing integration'
)

test(
  'Multer dependency installed',
  packageJson.dependencies.multer !== undefined,
  'File upload handling'
)

// 8. TypeScript Configuration Tests
console.log('\n🔧 8. TypeScript & Code Quality')
console.log('-'.repeat(30))

test(
  'TypeScript config exists',
  fs.existsSync(path.join(__dirname, '../tsconfig.json')),
  'TypeScript strict mode configuration'
)

test(
  'ESLint config exists',
  fs.existsSync(path.join(__dirname, '../.eslintrc.json')),
  'Code quality and linting rules'
)

test(
  'Tailwind config exists',
  fs.existsSync(path.join(__dirname, '../tailwind.config.ts')),
  'CSS framework configuration'
)

// 9. Documentation Tests
console.log('\n📚 9. Documentation')
console.log('-'.repeat(30))

test(
  'Phase 1 Progress Report exists',
  fs.existsSync(path.join(__dirname, '../PHASE-1-PROGRESS-REPORT.md')),
  'Detailed progress tracking documentation'
)

test(
  'Phase 1 Completion Report exists',
  fs.existsSync(path.join(__dirname, '../PHASE-1-COMPLETION-REPORT.md')),
  'Final completion status and achievements'
)

test(
  'README exists',
  fs.existsSync(path.join(__dirname, '../README.md')),
  'Project documentation and setup instructions'
)

// Summary
console.log('\n' + '='.repeat(50))
console.log('📊 PHASE 1 TESTING SUMMARY')
console.log('='.repeat(50))

const passRate = Math.round((passedTests / totalTests) * 100)
console.log(`Total Tests: ${totalTests}`)
console.log(`Passed: ${passedTests}`)
console.log(`Failed: ${totalTests - passedTests}`)
console.log(`Pass Rate: ${passRate}%`)

if (passRate >= 90) {
  console.log('\n🎉 EXCELLENT! Phase 1 implementation is highly successful!')
} else if (passRate >= 80) {
  console.log('\n✅ GOOD! Phase 1 implementation is mostly complete!')
} else if (passRate >= 70) {
  console.log('\n⚠️  FAIR! Phase 1 needs some improvements!')
} else {
  console.log('\n❌ POOR! Phase 1 requires significant work!')
}

// Failed tests details
const failedTests = results.filter(r => !r.passed)
if (failedTests.length > 0) {
  console.log('\n❌ Failed Tests:')
  failedTests.forEach(test => {
    console.log(`   - ${test.name}`)
    if (test.details) console.log(`     ${test.details}`)
  })
}

console.log('\n🚀 Phase 1 Testing Complete!')
console.log('Ready for Phase 2 development!')

// Exit with appropriate code
process.exit(failedTests.length > 0 ? 1 : 0)
