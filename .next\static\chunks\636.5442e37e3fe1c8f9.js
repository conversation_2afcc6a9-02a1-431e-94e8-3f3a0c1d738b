"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{9636:function(e,t,n){n.r(t),n.d(t,{PaymentForm:function(){return h}});var s=n(7437),a=n(2265),r=n(605),l=n(6334),i=n(757),c=n(1215),o=n(8629),d=n(9356);function h(e){let{courseId:t,courseTitle:n,amount:h,currency:m,onSuccess:u,onError:x,onCancel:g}=e,{data:p}=(0,r.useSession)(),{addToast:v}=(0,d.pm)(),[f,j]=(0,a.useState)(null),[b,y]=(0,a.useState)(!1),[N,k]=(0,a.useState)(!1),[w,C]=(0,a.useState)(null),[L,M]=(0,a.useState)("card"),[T,S]=(0,a.useState)("");(0,a.useEffect)(()=>{"card"!==L||f||_()},[L]);let _=async()=>{var e;if(!(null==p?void 0:null===(e=p.user)||void 0===e?void 0:e.id)){C("Vui l\xf2ng đăng nhập để thanh to\xe1n");return}try{y(!0),C(null);let e=await fetch("/api/payment/create-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({courseId:t,currency:m})}),n=await e.json();n.success?j(n.data):(C(n.error||"Lỗi khi tạo payment intent"),x&&x(n.error||"Lỗi khi tạo payment intent"))}catch(t){let e="Lỗi kết nối server";C(e),x&&x(e)}finally{y(!1)}},z=async()=>{if(!f){C("Payment intent chưa được tạo");return}try{if(k(!0),C(null),await new Promise(e=>setTimeout(e,2e3)),Math.random()>.1)v({message:"Thanh to\xe1n th\xe0nh c\xf4ng!",variant:"success"}),u&&u("enrollment_id_placeholder");else throw Error("Thanh to\xe1n thất bại. Vui l\xf2ng thử lại.")}catch(t){let e=t instanceof Error?t.message:"Lỗi thanh to\xe1n";C(e),v({message:e,variant:"error"}),x&&x(e)}finally{k(!1)}},B=async()=>{if(!T.trim()){C("Vui l\xf2ng nhập m\xe3 k\xedch hoạt");return}try{k(!0),C(null);let e=await fetch("/api/courses/".concat(t,"/enroll"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"activation_code",activationCode:T.trim()})}),n=await e.json();n.success?(v({message:"Đăng k\xfd kh\xf3a học th\xe0nh c\xf4ng!",variant:"success"}),u&&u(n.data._id)):(C(n.error||"M\xe3 k\xedch hoạt kh\xf4ng hợp lệ"),x&&x(n.error||"M\xe3 k\xedch hoạt kh\xf4ng hợp lệ"))}catch(t){let e="Lỗi kết nối server";C(e),x&&x(e)}finally{k(!1)}},D=(e,t)=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"===t?"VND":"USD"}).format(e);return p?(0,s.jsxs)(i.Zb,{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Thanh to\xe1n kh\xf3a học"}),(0,s.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"font-medium",children:n}),(0,s.jsx)("span",{className:"text-xl font-bold text-primary",children:D(h,m)})]})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Phương thức thanh to\xe1n"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"paymentMethod",value:"card",checked:"card"===L,onChange:e=>M(e.target.value),className:"mr-2"}),(0,s.jsx)("span",{children:"Thanh to\xe1n bằng thẻ"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"paymentMethod",value:"activation_code",checked:"activation_code"===L,onChange:e=>M(e.target.value),className:"mr-2"}),(0,s.jsx)("span",{children:"M\xe3 k\xedch hoạt"})]})]})]}),w&&(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)(o.g7,{variant:"error",message:w})}),"card"===L&&(0,s.jsx)("div",{className:"space-y-4",children:b?(0,s.jsxs)("div",{className:"text-center py-4",children:[(0,s.jsx)(c.gb,{size:"sm"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Đang tạo payment intent..."})]}):f?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 p-4 rounded-lg mb-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-800",children:"\uD83D\uDCB3 Trong phi\xean bản demo n\xe0y, thanh to\xe1n sẽ được m\xf4 phỏng. Trong production, đ\xe2y sẽ l\xe0 form Stripe Elements thực tế."})}),(0,s.jsx)(l.z,{onClick:z,disabled:N,className:"w-full",children:N?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.gb,{size:"sm"}),(0,s.jsx)("span",{className:"ml-2",children:"Đang xử l\xfd..."})]}):"Thanh to\xe1n ".concat(D(h,m))})]}):(0,s.jsx)(l.z,{onClick:_,className:"w-full",children:"Tạo payment intent"})}),"activation_code"===L&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xe3 k\xedch hoạt"}),(0,s.jsx)("input",{type:"text",value:T,onChange:e=>S(e.target.value),placeholder:"Nhập m\xe3 k\xedch hoạt của bạn",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",disabled:N})]}),(0,s.jsx)(l.z,{onClick:B,disabled:N||!T.trim(),className:"w-full",children:N?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.gb,{size:"sm"}),(0,s.jsx)("span",{className:"ml-2",children:"Đang xử l\xfd..."})]}):"K\xedch hoạt kh\xf3a học"})]}),g&&(0,s.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,s.jsx)(l.z,{variant:"outline",onClick:g,disabled:N,className:"w-full",children:"Hủy"})})]}):(0,s.jsx)(i.Zb,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Đăng nhập để thanh to\xe1n"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn cần đăng nhập để c\xf3 thể đăng k\xfd kh\xf3a học n\xe0y"}),(0,s.jsx)(l.z,{onClick:()=>window.location.href="/auth/signin",children:"Đăng nhập"})]})})}t.default=h},8629:function(e,t,n){n.d(t,{g7:function(){return m}});var s=n(7437),a=n(2265),r=n(535),l=n(3448);let i=(0,r.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,t)=>{let{className:n,variant:a,...r}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(i({variant:a}),n),...r})});c.displayName="Alert";let o=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,s.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",n),...a})});o.displayName="AlertTitle";let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",n),...a})});d.displayName="AlertDescription";let h={success:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function m(e){let{title:t,message:n,variant:a="info",onClose:r}=e;return(0,s.jsxs)(c,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[a],className:"relative",children:[h[a],(0,s.jsxs)("div",{className:"flex-1",children:[t&&(0,s.jsx)(o,{children:t}),(0,s.jsx)(d,{children:n})]}),r&&(0,s.jsx)("button",{onClick:r,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}}}]);