"use strict";(()=>{var e={};e.id=740,e.ids=[740],e.modules={38013:e=>{e.exports=require("mongodb")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},29648:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>q,patchFetch:()=>T,requestAsyncStorage:()=>D,routeModule:()=>x,serverHooks:()=>P,staticGenerationAsyncStorage:()=>R});var i,n,a={};r.r(a),r.d(a,{GET:()=>N,POST:()=>k,revalidate:()=>S});var s=r(49303),o=r(88716),u=r(60670),l=r(87070),c=r(75571),p=r(95456),g=r(14184),d=r(89332),m=r(11185),h=r.n(m);(function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.ARCHIVED="archived"})(i||(i={})),function(e){e.LANGUAGE="language",e.SKILL_LEVEL="skill_level",e.SUBJECT="subject",e.FORMAT="format"}(n||(n={}));let f=new m.Schema({name:{type:String,required:[!0,"T\xean danh mục l\xe0 bắt buộc"],trim:!0,maxlength:[100,"T\xean danh mục kh\xf4ng được vượt qu\xe1 100 k\xfd tự"]},slug:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},type:{type:String,enum:Object.values(n),required:[!0,"Loại danh mục l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(i),default:"active"},parent:{type:m.Schema.Types.ObjectId,ref:"Category",default:null},metadata:{description:{type:String,maxlength:[500,"M\xf4 tả kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},icon:String,color:{type:String,match:[/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"M\xe0u sắc phải l\xe0 m\xe3 hex hợp lệ"]},order:{type:Number,default:0,min:[0,"Thứ tự kh\xf4ng được \xe2m"]},isFeature:{type:Boolean,default:!1},seoTitle:{type:String,maxlength:[60,"SEO title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},seoDescription:{type:String,maxlength:[160,"SEO description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},stats:{totalCourses:{type:Number,default:0},totalStudents:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRevenue:{type:Number,default:0},lastUpdated:{type:Date,default:Date.now}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});f.index({slug:1}),f.index({type:1,status:1}),f.index({parent:1}),f.index({"metadata.order":1}),f.index({"stats.totalCourses":-1}),f.index({"stats.averageRating":-1}),f.virtual("isParentCategory").get(function(){return!this.parent}),f.virtual("fullPath").get(async function(){return await this.getFullPath()}),f.pre("save",function(e){this.isModified("name")&&!this.slug&&(this.slug=this.generateSlug()),e()}),f.methods.generateSlug=function(){return this.name.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},f.methods.updateStats=async function(){let e=h().model("Course"),t=h().model("Enrollment");try{let r=await e.countDocuments({category:this._id,status:"published"}),i=await t.aggregate([{$lookup:{from:"courses",localField:"courseId",foreignField:"_id",as:"course"}},{$match:{"course.category":this._id}},{$group:{_id:null,totalStudents:{$sum:1},totalRevenue:{$sum:"$payment.amount"}}}]),n=await e.aggregate([{$match:{category:this._id,status:"published"}},{$group:{_id:null,averageRating:{$avg:"$stats.averageRating"}}}]);this.stats={totalCourses:r,totalStudents:i[0]?.totalStudents||0,averageRating:n[0]?.averageRating||0,totalRevenue:i[0]?.totalRevenue||0,lastUpdated:new Date},await this.save()}catch(e){console.error("Error updating category stats:",e)}},f.methods.getChildren=async function(){return await h().model("Category").find({parent:this._id,status:"active"}).sort({"metadata.order":1})},f.methods.getFullPath=async function(){if(!this.parent)return this.name;let e=await h().model("Category").findById(this.parent);if(!e)return this.name;let t=await e.getFullPath();return`${t} > ${this.name}`},f.methods.isParent=function(){return!this.parent},f.statics.getHierarchy=async function(){let e=await this.find({status:"active"}).sort({"metadata.order":1}).lean(),t=(r=null)=>e.filter(e=>String(e.parent||null)===String(r)).map(e=>({...e,children:t(e._id)}));return t()},f.statics.getFeatured=async function(){return await this.find({status:"active","metadata.isFeature":!0}).sort({"metadata.order":1}).limit(6)};let y=h().models.Category||h().model("Category",f);var b=r(93330),v=r(9133);let S=300,w=v.z.object({title:v.z.string().min(1,"Ti\xeau đề l\xe0 bắt buộc").max(200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"),shortDescription:v.z.string().min(1,"M\xf4 tả ngắn l\xe0 bắt buộc").max(500,"M\xf4 tả ngắn kh\xf4ng được vượt qu\xe1 500 k\xfd tự"),content:v.z.object({description:v.z.string().min(1,"M\xf4 tả chi tiết l\xe0 bắt buộc"),objectives:v.z.array(v.z.string()).min(1,"Phải c\xf3 \xedt nhất 1 mục ti\xeau"),prerequisites:v.z.array(v.z.string()),syllabus:v.z.array(v.z.object({week:v.z.number().min(1),title:v.z.string().min(1),topics:v.z.array(v.z.string()),duration:v.z.number().min(0)}))}),category:v.z.string().min(1,"Danh mục l\xe0 bắt buộc"),language:v.z.enum(Object.values(d.ij)),level:v.z.enum(Object.values(d.f)),pricing:v.z.object({basePrice:v.z.number().min(0,"Gi\xe1 kh\xf4ng được \xe2m"),currency:v.z.string().default("VND")}),thumbnail:v.z.string().url().optional(),tags:v.z.array(v.z.string()).optional()}),E=v.z.object({page:v.z.string().optional().transform(e=>e?parseInt(e):1),limit:v.z.string().optional().transform(e=>e?parseInt(e):10),search:v.z.string().optional(),category:v.z.string().optional(),level:v.z.string().optional(),language:v.z.string().optional(),status:v.z.string().optional(),instructor:v.z.string().optional(),sortBy:v.z.string().optional().default("createdAt"),sortOrder:v.z.enum(["asc","desc"]).optional().default("desc"),featured:v.z.string().optional().transform(e=>"true"===e),minPrice:v.z.string().optional().transform(e=>e?parseFloat(e):void 0),maxPrice:v.z.string().optional().transform(e=>e?parseFloat(e):void 0)});async function N(e){try{await (0,g.ZP)();let{searchParams:t}=new URL(e.url),r=E.parse(Object.fromEntries(t)),i={};r.search&&(i.$or=[{title:{$regex:r.search,$options:"i"}},{shortDescription:{$regex:r.search,$options:"i"}},{"content.description":{$regex:r.search,$options:"i"}},{tags:{$in:[RegExp(r.search,"i")]}}]),r.category&&(i.category=r.category),r.level&&(i.level=r.level),r.language&&(i.language=r.language),r.status?i.status=r.status:i.status=d.D4.PUBLISHED,r.instructor&&(i.instructor=r.instructor),r.featured&&(i["metadata.isFeatured"]=!0),(void 0!==r.minPrice||void 0!==r.maxPrice)&&(i["pricing.basePrice"]={},void 0!==r.minPrice&&(i["pricing.basePrice"].$gte=r.minPrice),void 0!==r.maxPrice&&(i["pricing.basePrice"].$lte=r.maxPrice));let n={};n[r.sortBy]="asc"===r.sortOrder?1:-1;let a=(r.page-1)*r.limit,[s,o]=await Promise.all([d.ZP.find(i).populate("instructor","profile.firstName profile.lastName profile.avatar").populate("category","name slug").sort(n).skip(a).limit(r.limit).lean(),d.ZP.countDocuments(i)]),u=Math.ceil(o/r.limit),c=r.page<u,p=r.page>1;return l.NextResponse.json({success:!0,data:{courses:s,pagination:{currentPage:r.page,totalPages:u,totalCount:o,limit:r.limit,hasNextPage:c,hasPrevPage:p}}})}catch(e){if(console.error("Error fetching courses:",e),e instanceof v.z.ZodError)return l.NextResponse.json({success:!1,error:"Dữ liệu kh\xf4ng hợp lệ",details:e.errors},{status:400});return l.NextResponse.json({success:!1,error:"Lỗi server khi lấy danh s\xe1ch kh\xf3a học"},{status:500})}}async function k(e){try{await (0,g.ZP)();let t=await (0,c.getServerSession)(p.L);if(!t?.user?.id)return l.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});let r=await b.ZP.findById(t.user.id);if(!r||r.role!==b.i4.INSTRUCTOR&&r.role!==b.i4.ADMIN)return l.NextResponse.json({success:!1,error:"Bạn kh\xf4ng c\xf3 quyền tạo kh\xf3a học"},{status:403});let i=await e.json(),n=w.parse(i);if(!await y.findById(n.category))return l.NextResponse.json({success:!1,error:"Danh mục kh\xf4ng tồn tại"},{status:400});let a=new d.ZP({...n,instructor:t.user.id,slug:n.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-"),status:d.D4.DRAFT,stats:{totalStudents:0,totalLessons:0,totalDuration:0,averageRating:0,totalRatings:0,completionRate:0,lastUpdated:new Date}});return await a.save(),await a.populate("instructor","profile.firstName profile.lastName profile.avatar"),await a.populate("category","name slug"),l.NextResponse.json({success:!0,data:a,message:"Kh\xf3a học đ\xe3 được tạo th\xe0nh c\xf4ng"},{status:201})}catch(e){if(console.error("Error creating course:",e),e instanceof v.z.ZodError)return l.NextResponse.json({success:!1,error:"Dữ liệu kh\xf4ng hợp lệ",details:e.errors},{status:400});if(11e3===e.code&&e.keyPattern?.slug)return l.NextResponse.json({success:!1,error:"Ti\xeau đề kh\xf3a học đ\xe3 tồn tại, vui l\xf2ng chọn ti\xeau đề kh\xe1c"},{status:400});return l.NextResponse.json({success:!1,error:"Lỗi server khi tạo kh\xf3a học"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/courses/route",pathname:"/api/courses",filename:"route",bundlePath:"app/api/courses/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\courses\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:D,staticGenerationAsyncStorage:R,serverHooks:P}=x,q="/api/courses/route";function T(){return(0,u.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:R})}},95456:(e,t,r)=>{r.d(t,{L:()=>c});var i=r(53797),n=r(77234),a=r(41017),s=r(38013),o=r(14184),u=r(93330);let l=new s.MongoClient(process.env.MONGODB_URI).connect(),c={adapter:(0,a.dJ)(l),secret:process.env.NEXTAUTH_SECRET,providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email v\xe0 mật khẩu l\xe0 bắt buộc");try{await (0,o.ZP)();let t=await u.ZP.findOne({email:e.email.toLowerCase()}).select("+password");if(!t)throw Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");if(t.isLocked())throw Error("T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần");if(t.status!==u.J0.ACTIVE)throw Error("T\xe0i khoản chưa được k\xedch hoạt");if(!await t.comparePassword(e.password))throw await t.incrementLoginAttempts(),Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");return t.loginAttempts>0&&await t.updateOne({$unset:{loginAttempts:1,lockUntil:1}}),await t.updateOne({lastLogin:new Date}),{id:t._id.toString(),email:t.email,name:t.profile.fullName,role:t.role,status:t.status,emailVerified:t.emailVerified,image:t.profile.avatar}}catch(e){throw Error(e.message||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập")}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:e,user:t,account:r})=>(r&&t&&(e.role=t.role,e.status=t.status,e.emailVerified=t.emailVerified),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.status=t.status,e.user.emailVerified=t.emailVerified),e),signIn:async({user:e,account:t,profile:r})=>t?.provider!=="credentials"||!0===e.emailVerified,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:i}){console.log(`User ${e.email} signed in with ${t?.provider}`)},async signOut({session:e,token:t}){console.log("User signed out")},async createUser({user:e}){console.log(`New user created: ${e.email}`)}},debug:!1}},14184:(e,t,r)=>{r.d(t,{ZP:()=>o});var i=r(11185),n=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let o=async function(){if(s.conn)return s.conn;s.promise||(s.promise=n().connect(a,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},89332:(e,t,r)=>{r.d(t,{D4:()=>i,ZP:()=>l,f:()=>n,ij:()=>a});var i,n,a,s=r(11185),o=r.n(s);(function(e){e.DRAFT="draft",e.PUBLISHED="published",e.ARCHIVED="archived",e.SUSPENDED="suspended"})(i||(i={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(n||(n={})),function(e){e.ENGLISH="english",e.VIETNAMESE="vietnamese",e.CHINESE="chinese",e.JAPANESE="japanese",e.KOREAN="korean",e.FRENCH="french",e.GERMAN="german",e.SPANISH="spanish"}(a||(a={}));let u=new s.Schema({title:{type:String,required:[!0,"Ti\xeau đề kh\xf3a học l\xe0 bắt buộc"],trim:!0,maxlength:[200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"]},slug:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},shortDescription:{type:String,required:[!0,"M\xf4 tả ngắn l\xe0 bắt buộc"],maxlength:[500,"M\xf4 tả ngắn kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},content:{description:{type:String,required:[!0,"M\xf4 tả chi tiết l\xe0 bắt buộc"]},objectives:[{type:String,required:!0}],prerequisites:[String],syllabus:[{week:{type:Number,required:!0},title:{type:String,required:!0},topics:[String],duration:{type:Number,required:!0}}]},instructor:{type:s.Schema.Types.ObjectId,ref:"User",required:[!0,"Giảng vi\xean l\xe0 bắt buộc"]},category:{type:s.Schema.Types.ObjectId,ref:"Category",required:[!0,"Danh mục l\xe0 bắt buộc"]},subcategory:{type:s.Schema.Types.ObjectId,ref:"Category"},language:{type:String,enum:Object.values(a),required:[!0,"Ng\xf4n ngữ kh\xf3a học l\xe0 bắt buộc"]},level:{type:String,enum:Object.values(n),required:[!0,"Cấp độ kh\xf3a học l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(i),default:"draft"},thumbnail:String,previewVideo:String,tags:[String],pricing:{basePrice:{type:Number,required:[!0,"Gi\xe1 kh\xf3a học l\xe0 bắt buộc"],min:[0,"Gi\xe1 kh\xf4ng được \xe2m"]},currency:{type:String,default:"VND"},discountPrice:{type:Number,min:[0,"Gi\xe1 giảm kh\xf4ng được \xe2m"]},discountValidUntil:Date,installmentOptions:{enabled:{type:Boolean,default:!1},plans:[{months:{type:Number,required:!0},monthlyAmount:{type:Number,required:!0}}]}},settings:{maxStudents:{type:Number,min:[1,"Số học vi\xean tối đa phải \xedt nhất l\xe0 1"]},allowComments:{type:Boolean,default:!0},allowRatings:{type:Boolean,default:!0},certificateEnabled:{type:Boolean,default:!0},downloadableResources:{type:Boolean,default:!0},mobileAccess:{type:Boolean,default:!0},lifetimeAccess:{type:Boolean,default:!0},accessDuration:{type:Number,min:[1,"Thời gian truy cập phải \xedt nhất 1 ng\xe0y"]}},stats:{totalStudents:{type:Number,default:0},totalLessons:{type:Number,default:0},totalDuration:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRatings:{type:Number,default:0},completionRate:{type:Number,default:0,min:0,max:100}},seo:{metaTitle:{type:String,maxlength:[60,"Meta title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},metaDescription:{type:String,maxlength:[160,"Meta description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},publishedAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});u.index({slug:1}),u.index({instructor:1}),u.index({category:1,subcategory:1}),u.index({language:1,level:1}),u.index({status:1,publishedAt:-1}),u.index({"stats.averageRating":-1}),u.index({"stats.totalStudents":-1}),u.index({tags:1}),u.index({"pricing.basePrice":1}),u.virtual("isOnSale").get(function(){return!!(this.pricing.discountPrice&&this.pricing.discountValidUntil&&this.pricing.discountValidUntil>new Date)}),u.virtual("currentPrice").get(function(){return this.isOnSale?this.pricing.discountPrice:this.pricing.basePrice}),u.pre("save",function(e){this.isModified("title")&&!this.slug&&(this.slug=this.generateSlug()),e()}),u.methods.generateSlug=function(){return this.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},u.methods.updateStats=async function(){console.log("Updating course stats...")},u.methods.isPublished=function(){return"published"===this.status&&!!this.publishedAt},u.methods.canEnroll=function(){return!!this.isPublished()&&(!this.settings.maxStudents||!(this.stats.totalStudents>=this.settings.maxStudents))};let l=o().models.Course||o().model("Course",u)},93330:(e,t,r)=>{r.d(t,{J0:()=>n,ZP:()=>p,i4:()=>i});var i,n,a,s=r(11185),o=r.n(s),u=r(42023),l=r.n(u);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(i||(i={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(n||(n={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(a||(a={}));let c=new s.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(i),default:"student"},status:{type:String,enum:Object.values(n),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(a)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});c.index({"profile.firstName":1,"profile.lastName":1}),c.index({role:1,status:1}),c.index({createdAt:-1}),c.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),c.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),c.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await l().genSalt(12);this.password=await l().hash(this.password,t),e()}catch(t){e(t)}}),c.methods.comparePassword=async function(e){return l().compare(e,this.password)},c.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},c.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},c.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},c.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let p=o().models.User||o().model("User",c)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[276,242,70,799,133],()=>r(29648));module.exports=i})();