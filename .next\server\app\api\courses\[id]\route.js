"use strict";(()=>{var e={};e.id=279,e.ids=[279,544],e.modules={38013:e=>{e.exports=require("mongodb")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},74519:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>q,patchFetch:()=>P,requestAsyncStorage:()=>A,routeModule:()=>I,serverHooks:()=>k,staticGenerationAsyncStorage:()=>T});var i,r,n,a={};s.r(a),s.d(a,{DELETE:()=>x,GET:()=>N,PUT:()=>D});var o=s(49303),u=s(88716),l=s(60670),c=s(87070),d=s(75571),p=s(95456),m=s(14184),g=s(89332),h=s(11185),f=s.n(h);(function(e){e.VIDEO="video",e.AUDIO="audio",e.TEXT="text",e.QUIZ="quiz",e.ASSIGNMENT="assignment",e.LIVE_SESSION="live_session",e.DOCUMENT="document"})(i||(i={})),function(e){e.DRAFT="draft",e.PUBLISHED="published",e.ARCHIVED="archived",e.SCHEDULED="scheduled"}(r||(r={})),function(e){e.BEGINNER="beginner",e.INTERMEDIATE="intermediate",e.ADVANCED="advanced"}(n||(n={}));let y=new h.Schema({title:{type:String,required:[!0,"Ti\xeau đề b\xe0i học l\xe0 bắt buộc"],trim:!0,maxlength:[200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"]},slug:{type:String,required:!0,lowercase:!0,trim:!0},description:{type:String,required:[!0,"M\xf4 tả b\xe0i học l\xe0 bắt buộc"],maxlength:[1e3,"M\xf4 tả kh\xf4ng được vượt qu\xe1 1000 k\xfd tự"]},courseId:{type:h.Schema.Types.ObjectId,ref:"Course",required:[!0,"ID kh\xf3a học l\xe0 bắt buộc"]},chapterId:{type:h.Schema.Types.ObjectId,ref:"Chapter"},order:{type:Number,required:!0,min:[1,"Thứ tự b\xe0i học phải từ 1 trở l\xean"]},type:{type:String,enum:Object.values(i),required:[!0,"Loại b\xe0i học l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"draft"},difficulty:{type:String,enum:Object.values(n),default:"beginner"},content:{type:{type:String,enum:Object.values(i),required:!0},data:{mediaUrl:String,duration:{type:Number,min:0},transcript:String,subtitles:[{language:{type:String,required:!0},url:{type:String,required:!0}}],content:String,readingTime:{type:Number,min:0},questions:[{id:{type:String,required:!0},type:{type:String,enum:["multiple_choice","true_false","fill_blank","essay"],required:!0},question:{type:String,required:!0},options:[String],correctAnswer:h.Schema.Types.Mixed,points:{type:Number,required:!0,min:0},explanation:String}],documentUrl:String,documentType:String,fileSize:{type:Number,min:0},meetingUrl:String,scheduledAt:Date,timezone:String}},settings:{isPreview:{type:Boolean,default:!1},allowDownload:{type:Boolean,default:!1},allowComments:{type:Boolean,default:!0},autoPlay:{type:Boolean,default:!1},showTranscript:{type:Boolean,default:!0},completionCriteria:{type:{type:String,enum:["time_based","interaction_based","quiz_based"],default:"time_based"},value:{type:Number,required:!0,min:0,max:100}}},stats:{totalViews:{type:Number,default:0},totalCompletions:{type:Number,default:0},averageWatchTime:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRatings:{type:Number,default:0},completionRate:{type:Number,default:0,min:0,max:100},lastUpdated:{type:Date,default:Date.now}},seo:{metaTitle:{type:String,maxlength:[60,"Meta title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},metaDescription:{type:String,maxlength:[160,"Meta description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},publishedAt:Date,scheduledAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});y.index({courseId:1,order:1}),y.index({slug:1}),y.index({status:1,publishedAt:-1}),y.index({type:1}),y.index({difficulty:1}),y.index({"stats.totalViews":-1}),y.index({"stats.averageRating":-1}),y.index({courseId:1,chapterId:1,order:1}),y.virtual("estimatedDuration").get(function(){return this.content.data.duration?this.content.data.duration:this.content.data.readingTime?60*this.content.data.readingTime:0}),y.virtual("isQuiz").get(function(){return"quiz"===this.type||"assignment"===this.type}),y.pre("save",function(e){this.isModified("title")&&!this.slug&&(this.slug=this.generateSlug()),this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date),e()}),y.methods.generateSlug=function(){return this.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},y.methods.updateStats=async function(){console.log("Updating lesson stats...")},y.methods.isPublished=function(){return"published"===this.status&&!!this.publishedAt},y.methods.canAccess=async function(e){let t=f().model("Enrollment");return!!await t.findOne({userId:e,courseId:this.courseId,status:"active"})||this.settings.isPreview},y.methods.markAsCompleted=async function(e){console.log(`Marking lesson ${this._id} as completed for user ${e}`)};let b=f().models.Lesson||f().model("Lesson",y);var S=s(66820),E=s(93330),v=s(9133);let w=v.z.object({title:v.z.string().min(1).max(200).optional(),shortDescription:v.z.string().min(1).max(500).optional(),content:v.z.object({description:v.z.string().min(1).optional(),objectives:v.z.array(v.z.string()).min(1).optional(),prerequisites:v.z.array(v.z.string()).optional(),syllabus:v.z.array(v.z.object({week:v.z.number().min(1),title:v.z.string().min(1),topics:v.z.array(v.z.string()),duration:v.z.number().min(0)})).optional()}).optional(),category:v.z.string().optional(),language:v.z.string().optional(),level:v.z.string().optional(),pricing:v.z.object({basePrice:v.z.number().min(0),currency:v.z.string().default("VND")}).optional(),thumbnail:v.z.string().url().optional(),tags:v.z.array(v.z.string()).optional(),status:v.z.enum(Object.values(g.D4)).optional(),metadata:v.z.object({isFeatured:v.z.boolean().optional(),difficulty:v.z.number().min(1).max(5).optional(),estimatedDuration:v.z.number().min(0).optional()}).optional()});async function N(e,{params:t}){try{let e;await (0,m.ZP)();let{id:s}=t;e=f().Types.ObjectId.isValid(s)?{_id:s}:{slug:s};let i=await (0,d.getServerSession)(p.L),r={...e};if(i?.user?.id){let t=await E.ZP.findById(i.user.id);t?.role!==E.i4.INSTRUCTOR&&t?.role!==E.i4.ADMIN&&(r.status=g.D4.PUBLISHED),t?.role===E.i4.INSTRUCTOR&&(r.$or=[{...e,status:g.D4.PUBLISHED},{...e,instructor:i.user.id}])}else r.status=g.D4.PUBLISHED;let n=await g.ZP.findOne(r).populate("instructor","profile.firstName profile.lastName profile.avatar profile.bio").populate("category","name slug metadata").lean();if(!n)return c.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy kh\xf3a học"},{status:404});let a=await b.find({courseId:n._id,status:"published"}).select("title slug description order type difficulty settings.isPreview").sort({order:1}).lean(),o=null;i?.user?.id&&(o=await S.default.findOne({userId:i.user.id,courseId:n._id,status:"active"}).lean());let u=await S.default.getCourseStats(n._id.toString()),l={...n,lessons:a.map(e=>({...e,canAccess:o||e.settings?.isPreview||!1})),enrollment:o,stats:u,canEnroll:!o&&n.status===g.D4.PUBLISHED,canEdit:i?.user?.id===n.instructor._id||i?.user&&await E.ZP.findById(i.user.id).then(e=>e?.role===E.i4.ADMIN)};return c.NextResponse.json({success:!0,data:l})}catch(e){return console.error("Error fetching course:",e),c.NextResponse.json({success:!1,error:"Lỗi server khi lấy th\xf4ng tin kh\xf3a học"},{status:500})}}async function D(e,{params:t}){try{await (0,m.ZP)();let{id:s}=t;if(!f().Types.ObjectId.isValid(s))return c.NextResponse.json({success:!1,error:"ID kh\xf3a học kh\xf4ng hợp lệ"},{status:400});let i=await (0,d.getServerSession)(p.L);if(!i?.user?.id)return c.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});let r=await g.ZP.findById(s);if(!r)return c.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy kh\xf3a học"},{status:404});let n=await E.ZP.findById(i.user.id),a=r.instructor.toString()===i.user.id,o=n?.role===E.i4.ADMIN;if(!a&&!o)return c.NextResponse.json({success:!1,error:"Bạn kh\xf4ng c\xf3 quyền chỉnh sửa kh\xf3a học n\xe0y"},{status:403});let u=await e.json(),l=w.parse(u);if(l.title&&l.title!==r.title){let e=l.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-");if(await g.ZP.findOne({slug:e,_id:{$ne:s}}))return c.NextResponse.json({success:!1,error:"Ti\xeau đề kh\xf3a học đ\xe3 tồn tại, vui l\xf2ng chọn ti\xeau đề kh\xe1c"},{status:400});l.slug=e}l.status&&l.status===g.D4.PUBLISHED&&!r.publishedAt&&(l.publishedAt=new Date);let h=await g.ZP.findByIdAndUpdate(s,{...l,updatedAt:new Date},{new:!0,runValidators:!0}).populate("instructor","profile.firstName profile.lastName profile.avatar").populate("category","name slug");return c.NextResponse.json({success:!0,data:h,message:"Kh\xf3a học đ\xe3 được cập nhật th\xe0nh c\xf4ng"})}catch(e){if(console.error("Error updating course:",e),e instanceof v.z.ZodError)return c.NextResponse.json({success:!1,error:"Dữ liệu kh\xf4ng hợp lệ",details:e.errors},{status:400});return c.NextResponse.json({success:!1,error:"Lỗi server khi cập nhật kh\xf3a học"},{status:500})}}async function x(e,{params:t}){try{await (0,m.ZP)();let{id:e}=t;if(!f().Types.ObjectId.isValid(e))return c.NextResponse.json({success:!1,error:"ID kh\xf3a học kh\xf4ng hợp lệ"},{status:400});let s=await (0,d.getServerSession)(p.L);if(!s?.user?.id)return c.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});let i=await g.ZP.findById(e);if(!i)return c.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy kh\xf3a học"},{status:404});let r=await E.ZP.findById(s.user.id),n=i.instructor.toString()===s.user.id,a=r?.role===E.i4.ADMIN;if(!n&&!a)return c.NextResponse.json({success:!1,error:"Bạn kh\xf4ng c\xf3 quyền x\xf3a kh\xf3a học n\xe0y"},{status:403});if(await S.default.countDocuments({courseId:e,status:"active"})>0)return c.NextResponse.json({success:!1,error:"Kh\xf4ng thể x\xf3a kh\xf3a học c\xf3 học vi\xean đang học"},{status:400});return await Promise.all([b.deleteMany({courseId:e}),S.default.deleteMany({courseId:e}),g.ZP.findByIdAndDelete(e)]),c.NextResponse.json({success:!0,message:"Kh\xf3a học đ\xe3 được x\xf3a th\xe0nh c\xf4ng"})}catch(e){return console.error("Error deleting course:",e),c.NextResponse.json({success:!1,error:"Lỗi server khi x\xf3a kh\xf3a học"},{status:500})}}let I=new o.AppRouteRouteModule({definition:{kind:u.x.APP_ROUTE,page:"/api/courses/[id]/route",pathname:"/api/courses/[id]",filename:"route",bundlePath:"app/api/courses/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\courses\\[id]\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:A,staticGenerationAsyncStorage:T,serverHooks:k}=I,q="/api/courses/[id]/route";function P(){return(0,l.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:T})}},95456:(e,t,s)=>{s.d(t,{L:()=>c});var i=s(53797),r=s(77234),n=s(41017),a=s(38013),o=s(14184),u=s(93330);let l=new a.MongoClient(process.env.MONGODB_URI).connect(),c={adapter:(0,n.dJ)(l),secret:process.env.NEXTAUTH_SECRET,providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email v\xe0 mật khẩu l\xe0 bắt buộc");try{await (0,o.ZP)();let t=await u.ZP.findOne({email:e.email.toLowerCase()}).select("+password");if(!t)throw Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");if(t.isLocked())throw Error("T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần");if(t.status!==u.J0.ACTIVE)throw Error("T\xe0i khoản chưa được k\xedch hoạt");if(!await t.comparePassword(e.password))throw await t.incrementLoginAttempts(),Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");return t.loginAttempts>0&&await t.updateOne({$unset:{loginAttempts:1,lockUntil:1}}),await t.updateOne({lastLogin:new Date}),{id:t._id.toString(),email:t.email,name:t.profile.fullName,role:t.role,status:t.status,emailVerified:t.emailVerified,image:t.profile.avatar}}catch(e){throw Error(e.message||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập")}}}),(0,r.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:e,user:t,account:s})=>(s&&t&&(e.role=t.role,e.status=t.status,e.emailVerified=t.emailVerified),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.status=t.status,e.user.emailVerified=t.emailVerified),e),signIn:async({user:e,account:t,profile:s})=>t?.provider!=="credentials"||!0===e.emailVerified,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:s,isNewUser:i}){console.log(`User ${e.email} signed in with ${t?.provider}`)},async signOut({session:e,token:t}){console.log("User signed out")},async createUser({user:e}){console.log(`New user created: ${e.email}`)}},debug:!1}},14184:(e,t,s)=>{s.d(t,{ZP:()=>o});var i=s(11185),r=s.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=r().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},89332:(e,t,s)=>{s.d(t,{D4:()=>i,ZP:()=>l,f:()=>r,ij:()=>n});var i,r,n,a=s(11185),o=s.n(a);(function(e){e.DRAFT="draft",e.PUBLISHED="published",e.ARCHIVED="archived",e.SUSPENDED="suspended"})(i||(i={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(r||(r={})),function(e){e.ENGLISH="english",e.VIETNAMESE="vietnamese",e.CHINESE="chinese",e.JAPANESE="japanese",e.KOREAN="korean",e.FRENCH="french",e.GERMAN="german",e.SPANISH="spanish"}(n||(n={}));let u=new a.Schema({title:{type:String,required:[!0,"Ti\xeau đề kh\xf3a học l\xe0 bắt buộc"],trim:!0,maxlength:[200,"Ti\xeau đề kh\xf4ng được vượt qu\xe1 200 k\xfd tự"]},slug:{type:String,required:!0,unique:!0,lowercase:!0,trim:!0},shortDescription:{type:String,required:[!0,"M\xf4 tả ngắn l\xe0 bắt buộc"],maxlength:[500,"M\xf4 tả ngắn kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},content:{description:{type:String,required:[!0,"M\xf4 tả chi tiết l\xe0 bắt buộc"]},objectives:[{type:String,required:!0}],prerequisites:[String],syllabus:[{week:{type:Number,required:!0},title:{type:String,required:!0},topics:[String],duration:{type:Number,required:!0}}]},instructor:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"Giảng vi\xean l\xe0 bắt buộc"]},category:{type:a.Schema.Types.ObjectId,ref:"Category",required:[!0,"Danh mục l\xe0 bắt buộc"]},subcategory:{type:a.Schema.Types.ObjectId,ref:"Category"},language:{type:String,enum:Object.values(n),required:[!0,"Ng\xf4n ngữ kh\xf3a học l\xe0 bắt buộc"]},level:{type:String,enum:Object.values(r),required:[!0,"Cấp độ kh\xf3a học l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(i),default:"draft"},thumbnail:String,previewVideo:String,tags:[String],pricing:{basePrice:{type:Number,required:[!0,"Gi\xe1 kh\xf3a học l\xe0 bắt buộc"],min:[0,"Gi\xe1 kh\xf4ng được \xe2m"]},currency:{type:String,default:"VND"},discountPrice:{type:Number,min:[0,"Gi\xe1 giảm kh\xf4ng được \xe2m"]},discountValidUntil:Date,installmentOptions:{enabled:{type:Boolean,default:!1},plans:[{months:{type:Number,required:!0},monthlyAmount:{type:Number,required:!0}}]}},settings:{maxStudents:{type:Number,min:[1,"Số học vi\xean tối đa phải \xedt nhất l\xe0 1"]},allowComments:{type:Boolean,default:!0},allowRatings:{type:Boolean,default:!0},certificateEnabled:{type:Boolean,default:!0},downloadableResources:{type:Boolean,default:!0},mobileAccess:{type:Boolean,default:!0},lifetimeAccess:{type:Boolean,default:!0},accessDuration:{type:Number,min:[1,"Thời gian truy cập phải \xedt nhất 1 ng\xe0y"]}},stats:{totalStudents:{type:Number,default:0},totalLessons:{type:Number,default:0},totalDuration:{type:Number,default:0},averageRating:{type:Number,default:0,min:0,max:5},totalRatings:{type:Number,default:0},completionRate:{type:Number,default:0,min:0,max:100}},seo:{metaTitle:{type:String,maxlength:[60,"Meta title kh\xf4ng được vượt qu\xe1 60 k\xfd tự"]},metaDescription:{type:String,maxlength:[160,"Meta description kh\xf4ng được vượt qu\xe1 160 k\xfd tự"]},keywords:[String]},publishedAt:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});u.index({slug:1}),u.index({instructor:1}),u.index({category:1,subcategory:1}),u.index({language:1,level:1}),u.index({status:1,publishedAt:-1}),u.index({"stats.averageRating":-1}),u.index({"stats.totalStudents":-1}),u.index({tags:1}),u.index({"pricing.basePrice":1}),u.virtual("isOnSale").get(function(){return!!(this.pricing.discountPrice&&this.pricing.discountValidUntil&&this.pricing.discountValidUntil>new Date)}),u.virtual("currentPrice").get(function(){return this.isOnSale?this.pricing.discountPrice:this.pricing.basePrice}),u.pre("save",function(e){this.isModified("title")&&!this.slug&&(this.slug=this.generateSlug()),e()}),u.methods.generateSlug=function(){return this.title.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").trim().replace(/\s+/g,"-").replace(/-+/g,"-")},u.methods.updateStats=async function(){console.log("Updating course stats...")},u.methods.isPublished=function(){return"published"===this.status&&!!this.publishedAt},u.methods.canEnroll=function(){return!!this.isPublished()&&(!this.settings.maxStudents||!(this.stats.totalStudents>=this.settings.maxStudents))};let l=o().models.Course||o().model("Course",u)},66820:(e,t,s)=>{s.d(t,{default:()=>c,mh:()=>i,xM:()=>r});var i,r,n,a=s(11185),o=s.n(a);(function(e){e.ACTIVE="active",e.COMPLETED="completed",e.SUSPENDED="suspended",e.EXPIRED="expired",e.REFUNDED="refunded"})(i||(i={})),function(e){e.PAID="paid",e.FREE="free",e.ACTIVATION_CODE="activation_code",e.ADMIN_GRANTED="admin_granted"}(r||(r={})),function(e){e.NOT_STARTED="not_started",e.IN_PROGRESS="in_progress",e.COMPLETED="completed"}(n||(n={}));let u=new a.Schema({lessonId:{type:a.Schema.Types.ObjectId,ref:"Lesson",required:!0},status:{type:String,enum:Object.values(n),default:"not_started"},startedAt:Date,completedAt:Date,watchTime:{type:Number,default:0,min:0},lastPosition:{type:Number,default:0,min:0},attempts:{type:Number,default:0,min:0},score:{type:Number,min:0,max:100},notes:String},{_id:!1}),l=new a.Schema({userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID l\xe0 bắt buộc"]},courseId:{type:a.Schema.Types.ObjectId,ref:"Course",required:[!0,"Course ID l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(i),default:"active"},type:{type:String,enum:Object.values(r),required:[!0,"Loại đăng k\xfd l\xe0 bắt buộc"]},progress:{status:{type:String,enum:Object.values(n),default:"not_started"},completedLessons:{type:Number,default:0,min:0},totalLessons:{type:Number,default:0,min:0},completionPercentage:{type:Number,default:0,min:0,max:100},totalWatchTime:{type:Number,default:0,min:0},lastAccessedAt:{type:Date,default:Date.now},estimatedCompletionDate:Date,lessons:[u]},payment:{amount:{type:Number,required:!0,min:0},currency:{type:String,default:"VND"},method:{type:String,enum:["stripe","paypal","activation_code","free"],required:!0},transactionId:String,activationCode:String,paidAt:Date,refundedAt:Date,refundAmount:{type:Number,min:0}},certificate:{issued:{type:Boolean,default:!1},issuedAt:Date,certificateId:String,downloadUrl:String,validUntil:Date},accessGrantedAt:{type:Date,default:Date.now},accessExpiresAt:Date,lastAccessedAt:{type:Date,default:Date.now},enrolledBy:{type:a.Schema.Types.ObjectId,ref:"User"},notes:String},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});l.index({userId:1,courseId:1},{unique:!0}),l.index({userId:1,status:1}),l.index({courseId:1,status:1}),l.index({status:1,accessExpiresAt:1}),l.index({"payment.method":1,"payment.paidAt":-1}),l.index({"progress.status":1}),l.index({"progress.completionPercentage":-1}),l.index({lastAccessedAt:-1}),l.virtual("isActive").get(function(){return"active"===this.status&&this.canAccess()}),l.virtual("daysRemaining").get(function(){if(!this.accessExpiresAt)return null;let e=new Date;return Math.ceil((this.accessExpiresAt.getTime()-e.getTime())/864e5)}),l.pre("save",async function(e){this.isModified("progress.lessons")&&(this.progress.completedLessons=this.progress.lessons.filter(e=>"completed"===e.status).length,this.progress.completionPercentage=this.calculateCompletionPercentage(),0===this.progress.completionPercentage?this.progress.status="not_started":100===this.progress.completionPercentage?(this.progress.status="completed",this.status="completed"):this.progress.status="in_progress"),e()}),l.methods.updateProgress=async function(){let e=o().model("Lesson"),t=await e.find({courseId:this.courseId,status:"published"}).sort({order:1});for(let e of(this.progress.totalLessons=t.length,t))this.progress.lessons.find(t=>t.lessonId.toString()===e._id.toString())||this.progress.lessons.push({lessonId:e._id,status:"not_started",watchTime:0,lastPosition:0,attempts:0});this.progress.totalWatchTime=this.progress.lessons.reduce((e,t)=>e+t.watchTime,0),await this.save()},l.methods.calculateCompletionPercentage=function(){return 0===this.progress.totalLessons?0:Math.round(this.progress.completedLessons/this.progress.totalLessons*100)},l.methods.isExpired=function(){return!!this.accessExpiresAt&&new Date>this.accessExpiresAt},l.methods.canAccess=function(){return!("active"!==this.status||this.isExpired())},l.methods.generateCertificate=async function(){if("completed"!==this.progress.status)throw Error("Kh\xf3a học chưa ho\xe0n th\xe0nh");if(this.certificate.issued)throw Error("Chứng chỉ đ\xe3 được cấp");let e=`CERT-${this.courseId}-${this.userId}-${Date.now()}`;this.certificate={issued:!0,issuedAt:new Date,certificateId:e,downloadUrl:`/api/certificates/${e}`,validUntil:new Date(Date.now()+31536e6)},await this.save()},l.methods.extendAccess=async function(e){this.accessExpiresAt?this.accessExpiresAt=new Date(this.accessExpiresAt.getTime()+864e5*e):this.accessExpiresAt=new Date(Date.now()+864e5*e),await this.save()},l.statics.getActiveEnrollments=function(e){return this.find({userId:e,status:"active",$or:[{accessExpiresAt:{$exists:!1}},{accessExpiresAt:{$gt:new Date}}]}).populate("courseId")},l.statics.getCourseStats=async function(e){return(await this.aggregate([{$match:{courseId:new(o()).Types.ObjectId(e)}},{$group:{_id:null,totalEnrollments:{$sum:1},activeEnrollments:{$sum:{$cond:[{$eq:["$status","active"]},1,0]}},completedEnrollments:{$sum:{$cond:[{$eq:["$status","completed"]},1,0]}},averageProgress:{$avg:"$progress.completionPercentage"},totalRevenue:{$sum:"$payment.amount"}}}]))[0]||{totalEnrollments:0,activeEnrollments:0,completedEnrollments:0,averageProgress:0,totalRevenue:0}};let c=o().models.Enrollment||o().model("Enrollment",l)},93330:(e,t,s)=>{s.d(t,{J0:()=>r,ZP:()=>d,i4:()=>i});var i,r,n,a=s(11185),o=s.n(a),u=s(42023),l=s.n(u);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(i||(i={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(r||(r={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(n||(n={}));let c=new a.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(i),default:"student"},status:{type:String,enum:Object.values(r),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(n)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});c.index({"profile.firstName":1,"profile.lastName":1}),c.index({role:1,status:1}),c.index({createdAt:-1}),c.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),c.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),c.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await l().genSalt(12);this.password=await l().hash(this.password,t),e()}catch(t){e(t)}}),c.methods.comparePassword=async function(e){return l().compare(e,this.password)},c.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},c.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},c.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},c.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let d=o().models.User||o().model("User",c)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[276,242,70,799,133],()=>s(74519));module.exports=i})();