# WebTA LMS - <PERSON><PERSON> thống Quản lý <PERSON>c tập

<PERSON>ệ thống LMS Next.js fullstack cho việc bán và quản lý khóa học ngoại ngữ với tính năng đánh giá tự động bằng AI cho 4 kỹ năng: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>.

## 🚀 Tính năng chính

- **Quản lý khóa học**: <PERSON><PERSON><PERSON>, chỉnh sửa và quản lý khóa học ngoại ngữ
- **Đ<PERSON>h giá AI**: Tự động đánh giá 4 kỹ năng ngôn ngữ bằng AI
- **Hệ thống thanh toán**: <PERSON><PERSON><PERSON> hợ<PERSON> thanh toán trực tuyến
- **Quản lý người dùng**: <PERSON>ân quyền Student, Instructor, Admin
- **Responsive Design**: Tối ưu cho mọi thiết bị

## 🛠️ Technology Stack

- **Frontend**: Next.js 14+, React 18, TypeScript
- **Styling**: <PERSON><PERSON><PERSON> CSS, <PERSON>hadcn/ui
- **Backend**: Next.js API Routes
- **Database**: MongoDB với Mongoose ODM
- **Authentication**: NextAuth.js
- **Testing**: Jest, React Testing Library
- **Code Quality**: ESLint, Prettier

## 📋 Yêu cầu hệ thống

- Node.js >= 18.0.0
- npm >= 9.0.0
- MongoDB >= 5.0

## 🚀 Cài đặt và chạy dự án

### 1. Clone repository

```bash
git clone <repository-url>
cd webta-lms
```

### 2. Cài đặt dependencies

```bash
npm install
```

### 3. Cấu hình environment variables

Sao chép file `.env.example` thành `.env.local` và cập nhật các giá trị:

```bash
cp .env.example .env.local
```

Cập nhật các biến môi trường trong `.env.local`:

```env
MONGODB_URI=mongodb://localhost:27017/webta-lms
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
# ... các biến khác
```

### 4. Khởi động MongoDB

Đảm bảo MongoDB đang chạy trên hệ thống của bạn.

### 5. Chạy ứng dụng

```bash
# Development mode
npm run dev

# Production build
npm run build
npm start
```

Ứng dụng sẽ chạy tại `http://localhost:3000`

## 🧪 Testing

```bash
# Chạy tests
npm test

# Chạy tests với watch mode
npm run test:watch

# Chạy tests với coverage
npm run test:coverage
```

## 📝 Scripts có sẵn

- `npm run dev` - Chạy development server
- `npm run build` - Build production
- `npm start` - Chạy production server
- `npm run lint` - Chạy ESLint
- `npm run lint:fix` - Tự động fix ESLint errors
- `npm run type-check` - Kiểm tra TypeScript types
- `npm test` - Chạy tests
- `npm run test:watch` - Chạy tests với watch mode
- `npm run test:coverage` - Chạy tests với coverage report

## 📁 Cấu trúc dự án

```
webta-lms/
├── docs/                     # Tài liệu dự án
├── src/
│   ├── app/                  # Next.js App Router
│   │   ├── api/             # API routes
│   │   ├── auth/            # Authentication pages
│   │   └── globals.css      # Global styles
│   ├── components/          # React components
│   │   └── ui/              # UI components
│   ├── lib/                 # Utility libraries
│   ├── models/              # Database models
│   └── types/               # TypeScript type definitions
├── public/                  # Static assets
├── tests/                   # Test files
└── ...config files
```

## 🔧 Development Guidelines

### Code Style

- Sử dụng TypeScript cho type safety
- Follow ESLint và Prettier rules
- Viết tests cho các components và utilities quan trọng
- Sử dụng conventional commits

### Git Workflow

1. Tạo branch từ `main` cho feature mới
2. Commit với conventional commit format
3. Tạo Pull Request để review
4. Merge sau khi được approve

### Database

- Sử dụng Mongoose schemas với validation
- Tạo indexes cho performance
- Backup database thường xuyên

## 📚 Tài liệu

Xem thêm tài liệu chi tiết trong thư mục `docs/`:

- [Development Implementation Plan](docs/Development-Implementation-Plan.md)
- [LMS Functional Requirements](docs/LMS-Functional-Requirements.md)
- [MongoDB Schema Design](docs/MongoDB-Schema-Design.md)
- [API Design Specification](docs/API-Design-Specification.md)
- [Code Standards Guidelines](docs/Code-Standards-Guidelines.md)

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 👥 Team

- **WebTA Team** - Initial work

## 🆘 Support

Nếu bạn gặp vấn đề hoặc có câu hỏi, vui lòng tạo issue trên GitHub repository.

---

**Phase 0 Status**: ✅ Completed
- [x] Project initialization
- [x] Database setup
- [x] Authentication foundation
- [x] Development tools configuration
- [x] Environment & configuration
- [x] Basic UI components
