#!/usr/bin/env node

/**
 * Layout Consistency Audit Script for WebTA LMS
 * Analyzes layout consistency across all pages and components
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Layout Consistency Audit - WebTA LMS')
console.log('=' .repeat(50))

// Audit results
let totalChecks = 0
let passedChecks = 0
const issues = []
const recommendations = []

function check(name, condition, details = '', recommendation = '') {
  totalChecks++
  const passed = condition
  if (passed) passedChecks++
  
  const status = passed ? '✅ PASS' : '⚠️  ISSUE'
  console.log(`${status} ${name}`)
  if (details) console.log(`   ${details}`)
  
  if (!passed) {
    issues.push({ name, details, recommendation })
    if (recommendation) {
      recommendations.push(recommendation)
    }
  }
}

// 1. Layout Structure Consistency
console.log('\n🏗️ 1. Layout Structure Consistency')
console.log('-'.repeat(40))

// Check all page files for consistent structure
const pageFiles = [
  'src/app/page.tsx',
  'src/app/courses/page.tsx',
  'src/app/courses/[slug]/page.tsx',
  'src/app/dashboard/student/page.tsx',
  'src/app/dashboard/instructor/page.tsx',
  'src/app/test-layout/page.tsx',
  'src/app/test-dashboard/page.tsx'
]

let consistentPageStructure = true
let pagesWithMinHeight = 0
let pagesWithBgGray = 0

pageFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('min-h-screen')) pagesWithMinHeight++
    if (content.includes('bg-gray-50') || content.includes('bg-gray-100')) pagesWithBgGray++
  }
})

check(
  'Consistent page structure',
  pagesWithMinHeight >= pageFiles.length * 0.8,
  `${pagesWithMinHeight}/${pageFiles.length} pages use min-h-screen`,
  'Ensure all pages use consistent min-height classes'
)

check(
  'Consistent background colors',
  pagesWithBgGray >= pageFiles.length * 0.7,
  `${pagesWithBgGray}/${pageFiles.length} pages use consistent background`,
  'Standardize background colors across all pages'
)

// 2. Component Consistency
console.log('\n🧩 2. Component Consistency')
console.log('-'.repeat(40))

// Check UI components for consistent patterns
const componentFiles = [
  'src/components/ui/Button.tsx',
  'src/components/ui/Card.tsx',
  'src/components/ui/Modal.tsx',
  'src/components/ui/Loading.tsx',
  'src/components/ui/Alert.tsx'
]

let componentsWithVariants = 0
let componentsWithSizes = 0
let componentsWithClassName = 0

componentFiles.forEach(filePath => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    if (content.includes('variant') && content.includes('VariantProps')) componentsWithVariants++
    if (content.includes('size') && (content.includes('sm') || content.includes('lg'))) componentsWithSizes++
    if (content.includes('className') && content.includes('cn(')) componentsWithClassName++
  }
})

check(
  'Component variant consistency',
  componentsWithVariants >= componentFiles.length * 0.8,
  `${componentsWithVariants}/${componentFiles.length} components have variant props`,
  'Implement consistent variant patterns across all UI components'
)

check(
  'Component size consistency',
  componentsWithSizes >= componentFiles.length * 0.6,
  `${componentsWithSizes}/${componentFiles.length} components have size props`,
  'Add size variants to components where appropriate'
)

check(
  'Component className handling',
  componentsWithClassName >= componentFiles.length * 0.9,
  `${componentsWithClassName}/${componentFiles.length} components handle className properly`,
  'Ensure all components use cn() utility for className merging'
)

// 3. Typography Consistency
console.log('\n📝 3. Typography Consistency')
console.log('-'.repeat(40))

// Check for consistent typography usage
let filesWithConsistentHeadings = 0
let filesWithConsistentText = 0
let totalFilesChecked = 0

const checkTypography = (filePath) => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    totalFilesChecked++
    
    // Check for consistent heading classes
    const hasConsistentHeadings = content.includes('text-3xl font-bold') || 
                                  content.includes('text-2xl font-semibold') ||
                                  content.includes('text-xl font-semibold')
    if (hasConsistentHeadings) filesWithConsistentHeadings++
    
    // Check for consistent text classes
    const hasConsistentText = content.includes('text-gray-600') || 
                             content.includes('text-gray-700') ||
                             content.includes('text-sm')
    if (hasConsistentText) filesWithConsistentText++
  }
}

pageFiles.forEach(checkTypography)

check(
  'Consistent heading typography',
  filesWithConsistentHeadings >= totalFilesChecked * 0.8,
  `${filesWithConsistentHeadings}/${totalFilesChecked} files use consistent heading classes`,
  'Standardize heading typography classes (text-3xl font-bold, text-2xl font-semibold, etc.)'
)

check(
  'Consistent text typography',
  filesWithConsistentText >= totalFilesChecked * 0.8,
  `${filesWithConsistentText}/${totalFilesChecked} files use consistent text classes`,
  'Standardize text color and size classes across all components'
)

// 4. Spacing Consistency
console.log('\n📏 4. Spacing Consistency')
console.log('-'.repeat(40))

// Check for consistent spacing patterns
let filesWithConsistentPadding = 0
let filesWithConsistentMargin = 0
let filesWithConsistentGap = 0

const checkSpacing = (filePath) => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for consistent padding
    if (content.includes('p-4') || content.includes('p-6') || content.includes('p-8') ||
        content.includes('px-4') || content.includes('py-4')) {
      filesWithConsistentPadding++
    }
    
    // Check for consistent margin
    if (content.includes('m-4') || content.includes('mb-4') || content.includes('mt-4') ||
        content.includes('mx-auto')) {
      filesWithConsistentMargin++
    }
    
    // Check for consistent gap
    if (content.includes('gap-4') || content.includes('gap-6') || content.includes('space-y-4')) {
      filesWithConsistentGap++
    }
  }
}

pageFiles.forEach(checkSpacing)

check(
  'Consistent padding usage',
  filesWithConsistentPadding >= totalFilesChecked * 0.8,
  `${filesWithConsistentPadding}/${totalFilesChecked} files use consistent padding`,
  'Use consistent padding scale (p-4, p-6, p-8) across all components'
)

check(
  'Consistent margin usage',
  filesWithConsistentMargin >= totalFilesChecked * 0.7,
  `${filesWithConsistentMargin}/${totalFilesChecked} files use consistent margin`,
  'Standardize margin usage with consistent scale'
)

check(
  'Consistent gap/spacing usage',
  filesWithConsistentGap >= totalFilesChecked * 0.7,
  `${filesWithConsistentGap}/${totalFilesChecked} files use consistent gap classes`,
  'Use consistent gap and space-y classes for layout spacing'
)

// 5. Color Scheme Consistency
console.log('\n🎨 5. Color Scheme Consistency')
console.log('-'.repeat(40))

// Check Tailwind config for consistent color scheme
const tailwindConfigExists = fs.existsSync(path.join(__dirname, '../tailwind.config.ts')) ||
                             fs.existsSync(path.join(__dirname, '../tailwind.config.js'))

check(
  'Tailwind configuration exists',
  tailwindConfigExists,
  'Tailwind config file found',
  'Ensure Tailwind configuration is properly set up'
)

if (tailwindConfigExists) {
  const configPath = fs.existsSync(path.join(__dirname, '../tailwind.config.ts')) 
    ? '../tailwind.config.ts' 
    : '../tailwind.config.js'
  
  const configContent = fs.readFileSync(path.join(__dirname, configPath), 'utf8')
  
  check(
    'Custom color scheme defined',
    configContent.includes('primary') && configContent.includes('secondary'),
    'Primary and secondary colors are defined in Tailwind config',
    'Define comprehensive color scheme in Tailwind configuration'
  )
  
  check(
    'Extended color palette',
    configContent.includes('extend') && configContent.includes('colors'),
    'Extended color palette is configured',
    'Extend Tailwind color palette with brand colors'
  )
}

// 6. Responsive Design Patterns
console.log('\n📱 6. Responsive Design Patterns')
console.log('-'.repeat(40))

let filesWithResponsiveClasses = 0
let filesWithMobileFirst = 0

const checkResponsive = (filePath) => {
  if (fs.existsSync(path.join(__dirname, '..', filePath))) {
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8')
    
    // Check for responsive classes
    if (content.includes('sm:') || content.includes('md:') || content.includes('lg:')) {
      filesWithResponsiveClasses++
    }
    
    // Check for mobile-first approach
    if (content.includes('grid-cols-1') && content.includes('md:grid-cols-2')) {
      filesWithMobileFirst++
    }
  }
}

pageFiles.forEach(checkResponsive)

check(
  'Responsive design implementation',
  filesWithResponsiveClasses >= totalFilesChecked * 0.8,
  `${filesWithResponsiveClasses}/${totalFilesChecked} files use responsive classes`,
  'Implement responsive design patterns across all pages'
)

check(
  'Mobile-first approach',
  filesWithMobileFirst >= totalFilesChecked * 0.5,
  `${filesWithMobileFirst}/${totalFilesChecked} files follow mobile-first patterns`,
  'Adopt mobile-first responsive design approach'
)

// Summary
console.log('\n' + '='.repeat(50))
console.log('🔍 LAYOUT CONSISTENCY AUDIT SUMMARY')
console.log('='.repeat(50))

const passRate = Math.round((passedChecks / totalChecks) * 100)
console.log(`Total Checks: ${totalChecks}`)
console.log(`Passed: ${passedChecks}`)
console.log(`Issues Found: ${totalChecks - passedChecks}`)
console.log(`Consistency Score: ${passRate}%`)

if (passRate >= 90) {
  console.log('\n🎉 EXCELLENT! Layout consistency is very high!')
} else if (passRate >= 80) {
  console.log('\n✅ GOOD! Layout consistency is mostly maintained!')
} else if (passRate >= 70) {
  console.log('\n⚠️  FAIR! Some layout consistency improvements needed!')
} else {
  console.log('\n❌ POOR! Significant layout consistency issues found!')
}

// Issues and Recommendations
if (issues.length > 0) {
  console.log('\n⚠️  Issues Found:')
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.name}`)
    if (issue.details) console.log(`   ${issue.details}`)
    if (issue.recommendation) console.log(`   💡 ${issue.recommendation}`)
  })
}

if (recommendations.length > 0) {
  console.log('\n💡 Key Recommendations:')
  const uniqueRecommendations = [...new Set(recommendations)]
  uniqueRecommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`)
  })
}

console.log('\n🚀 Layout Consistency Audit Complete!')

// Exit with appropriate code
process.exit(issues.length > 0 ? 1 : 0)
