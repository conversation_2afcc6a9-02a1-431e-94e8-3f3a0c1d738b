(()=>{var e={};e.id=298,e.ids=[298],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},92110:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),a(73375),a(39285),a(35866);var t=a(23191),r=a(88716),l=a(37922),n=a.n(l),i=a(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let c=["",{children:["test-layout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,73375)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\test-layout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\test-layout\\page.tsx"],x="/test-layout/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/test-layout/page",pathname:"/test-layout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9483:(e,s,a)=>{Promise.resolve().then(a.bind(a,17805))},17805:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});var t=a(10326),r=a(90434),l=a(47375),n=a(99837);function i(){return t.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:t.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)(l.Zb,{className:"p-8",children:[t.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Layout Testing Page"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Layout Structure Test"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Trang n\xe0y để test layout structure v\xe0 đảm bảo kh\xf4ng c\xf3 duplicate headers/footers."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(l.Zb,{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Main App Routes"}),t.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c routes n\xe0y sẽ c\xf3 Header v\xe0 Footer"}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(r.default,{href:"/",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83C\uDFE0 Home Page"})}),t.jsx(r.default,{href:"/courses",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDCDA Courses Page"})}),t.jsx(r.default,{href:"/about",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"ℹ️ About Page"})}),t.jsx(r.default,{href:"/contact",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDCDE Contact Page"})})]})]}),(0,t.jsxs)(l.Zb,{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Dashboard Routes"}),t.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c routes n\xe0y c\xf3 layout ri\xeang, kh\xf4ng c\xf3 Header/Footer ch\xednh"}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(r.default,{href:"/dashboard",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDCCA Dashboard (Auto-redirect)"})}),t.jsx(r.default,{href:"/dashboard/student",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83C\uDF93 Student Dashboard"})}),t.jsx(r.default,{href:"/dashboard/instructor",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDC68‍\uD83C\uDFEB Instructor Dashboard"})}),t.jsx(r.default,{href:"/test-dashboard",className:"block",children:t.jsx(n.z,{variant:"outline",className:"w-full justify-start",children:"\uD83E\uDDEA Dashboard API Test"})})]})]})]}),(0,t.jsxs)(l.Zb,{className:"p-6 bg-blue-50 border-blue-200",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4 text-blue-900",children:"Layout Verification Checklist"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-green-600",children:"✅"}),t.jsx("span",{children:"Main app routes c\xf3 Header v\xe0 Footer"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-green-600",children:"✅"}),t.jsx("span",{children:"Dashboard routes c\xf3 navigation ri\xeang"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-green-600",children:"✅"}),t.jsx("span",{children:"Kh\xf4ng c\xf3 duplicate headers"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-green-600",children:"✅"}),t.jsx("span",{children:"Layout transitions mượt m\xe0"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-green-600",children:"✅"}),t.jsx("span",{children:"Responsive design hoạt động đ\xfang"})]})]})]}),(0,t.jsxs)(l.Zb,{className:"p-6 bg-gray-50",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Current Route Info"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Current Path:"})," /test-layout"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Layout Type:"})," Main App Layout (with Header/Footer)"]}),(0,t.jsxs)("div",{children:[t.jsx("strong",{children:"Expected Behavior:"})," Should show main navigation header and footer"]})]})]}),(0,t.jsxs)(l.Zb,{className:"p-6 bg-yellow-50 border-yellow-200",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4 text-yellow-900",children:"Testing Instructions"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm text-yellow-800",children:[t.jsx("li",{children:"Kiểm tra trang n\xe0y c\xf3 Header v\xe0 Footer kh\xf4ng"}),t.jsx("li",{children:"Click v\xe0o c\xe1c main app routes v\xe0 verify c\xf3 Header/Footer"}),t.jsx("li",{children:"Click v\xe0o dashboard routes v\xe0 verify chỉ c\xf3 dashboard navigation"}),t.jsx("li",{children:"Kiểm tra kh\xf4ng c\xf3 duplicate navigation bars"}),t.jsx("li",{children:"Test responsive behavior tr\xean mobile v\xe0 desktop"}),t.jsx("li",{children:"Verify layout transitions mượt m\xe0"})]})]}),t.jsx("div",{className:"text-center pt-6",children:t.jsx(r.default,{href:"/",children:t.jsx(n.z,{children:"← Về trang chủ"})})})]})]})})})}},47375:(e,s,a)=>{"use strict";a.d(s,{Zb:()=>d});var t=a(10326),r=a(17577),l=a(79360),n=a(51223);let i=(0,l.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),d=r.forwardRef(({className:e,variant:s,size:a,...r},l)=>t.jsx("div",{ref:l,className:(0,n.cn)(i({variant:s,size:a}),e),...r}));d.displayName="Card",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s})).displayName="CardHeader",r.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s})).displayName="CardTitle",r.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,n.cn)("p-6 pt-0",e),...s})).displayName="CardContent",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},73375:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\test-layout\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[276,105,826],()=>a(92110));module.exports=t})();