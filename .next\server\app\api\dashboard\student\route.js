"use strict";(()=>{var e={};e.id=725,e.ids=[725,544],e.modules={38013:e=>{e.exports=require("mongodb")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},69434:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>D,patchFetch:()=>E,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>y,staticGenerationAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{GET:()=>m});var i=s(49303),n=s(88716),a=s(60670),o=s(87070),c=s(75571),l=s(95456),u=s(14184),d=s(66820),p=s(93330);async function m(e){try{await (0,u.ZP)();let e=await (0,c.getServerSession)(l.L);if(!e?.user?.id)return o.NextResponse.json({success:!1,error:"Vui l\xf2ng đăng nhập"},{status:401});if(!await p.ZP.findById(e.user.id))return o.NextResponse.json({success:!1,error:"Kh\xf4ng t\xecm thấy người d\xf9ng"},{status:404});let t=await d.default.find({userId:e.user.id,status:{$in:[d.mh.ACTIVE,d.mh.COMPLETED]}}).populate({path:"courseId",select:"title slug thumbnail instructor stats",populate:{path:"instructor",select:"profile.firstName profile.lastName profile.avatar"}}).sort({lastAccessedAt:-1}).lean(),s={totalCourses:t.length,completedCourses:t.filter(e=>"completed"===e.progress.status).length,totalWatchTime:t.reduce((e,t)=>e+t.progress.totalWatchTime,0),averageProgress:t.length>0?t.reduce((e,t)=>e+t.progress.completionPercentage,0)/t.length:0},r=new Date(Date.now()-6048e5),i=t.filter(e=>new Date(e.progress.lastAccessedAt)>r).length,n=await h(e.user.id),a=t.filter(e=>e.accessExpiresAt&&new Date(e.accessExpiresAt)>new Date).sort((e,t)=>new Date(e.accessExpiresAt).getTime()-new Date(t.accessExpiresAt).getTime()).slice(0,5);t.map(e=>e.courseId?.category).filter(Boolean);let m=function(e,t){let s=[];t.totalCourses>=1&&s.push({id:"first_course",title:"Kh\xf3a học đầu ti\xean",description:"Đ\xe3 đăng k\xfd kh\xf3a học đầu ti\xean",icon:"\uD83C\uDFAF",unlockedAt:e[0]?.createdAt}),t.completedCourses>=1&&s.push({id:"first_completion",title:"Ho\xe0n th\xe0nh đầu ti\xean",description:"Đ\xe3 ho\xe0n th\xe0nh kh\xf3a học đầu ti\xean",icon:"\uD83C\uDFC6",unlockedAt:e.find(e=>"completed"===e.progress.status)?.updatedAt}),t.completedCourses>=5&&s.push({id:"five_completions",title:"Học vi\xean t\xedch cực",description:"Đ\xe3 ho\xe0n th\xe0nh 5 kh\xf3a học",icon:"\uD83C\uDF1F",unlockedAt:new Date});let r=Math.floor(t.totalWatchTime/3600);return r>=10&&s.push({id:"ten_hours",title:"Người học ki\xean tr\xec",description:"Đ\xe3 học hơn 10 giờ",icon:"⏰",unlockedAt:new Date}),r>=50&&s.push({id:"fifty_hours",title:"Chuy\xean gia học tập",description:"Đ\xe3 học hơn 50 giờ",icon:"\uD83C\uDF93",unlockedAt:new Date}),t.averageProgress>=80&&s.push({id:"high_progress",title:"Người ho\xe0n th\xe0nh xuất sắc",description:"Tiến độ trung b\xecnh tr\xean 80%",icon:"\uD83D\uDCC8",unlockedAt:new Date}),s.sort((e,t)=>new Date(t.unlockedAt).getTime()-new Date(e.unlockedAt).getTime())}(t,s);return o.NextResponse.json({success:!0,data:{enrollments:t,stats:{...s,recentActivity:i,learningStreak:n},upcomingDeadlines:a,achievements:m}})}catch(e){return console.error("Error fetching student dashboard:",e),o.NextResponse.json({success:!1,error:"Lỗi server khi tải dashboard"},{status:500})}}async function h(e){try{let t=await d.default.find({userId:e,status:d.mh.ACTIVE}).select("progress.lastAccessedAt").lean();if(0===t.length)return 0;let s=t.map(e=>new Date(e.progress.lastAccessedAt).toDateString()).filter((e,t,s)=>s.indexOf(e)===t).sort((e,t)=>new Date(t).getTime()-new Date(e).getTime());if(0===s.length)return 0;let r=0,i=new Date().toDateString(),n=new Date(Date.now()-864e5).toDateString();if(s[0]===i||s[0]===n){r=1;for(let e=1;e<s.length;e++){let t=new Date(s[e-1]),i=new Date(s[e]),n=Math.floor((t.getTime()-i.getTime())/864e5);if(1===n)r++;else break}}return r}catch(e){return console.error("Error calculating learning streak:",e),0}}let g=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/dashboard/student/route",pathname:"/api/dashboard/student",filename:"route",bundlePath:"app/api/dashboard/student/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\dashboard\\student\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:w,serverHooks:y}=g,D="/api/dashboard/student/route";function E(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:w})}},95456:(e,t,s)=>{s.d(t,{L:()=>u});var r=s(53797),i=s(77234),n=s(41017),a=s(38013),o=s(14184),c=s(93330);let l=new a.MongoClient(process.env.MONGODB_URI).connect(),u={adapter:(0,n.dJ)(l),secret:process.env.NEXTAUTH_SECRET,providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email v\xe0 mật khẩu l\xe0 bắt buộc");try{await (0,o.ZP)();let t=await c.ZP.findOne({email:e.email.toLowerCase()}).select("+password");if(!t)throw Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");if(t.isLocked())throw Error("T\xe0i khoản đ\xe3 bị kh\xf3a do đăng nhập sai qu\xe1 nhiều lần");if(t.status!==c.J0.ACTIVE)throw Error("T\xe0i khoản chưa được k\xedch hoạt");if(!await t.comparePassword(e.password))throw await t.incrementLoginAttempts(),Error("Email hoặc mật khẩu kh\xf4ng đ\xfang");return t.loginAttempts>0&&await t.updateOne({$unset:{loginAttempts:1,lockUntil:1}}),await t.updateOne({lastLogin:new Date}),{id:t._id.toString(),email:t.email,name:t.profile.fullName,role:t.role,status:t.status,emailVerified:t.emailVerified,image:t.profile.avatar}}catch(e){throw Error(e.message||"Đ\xe3 xảy ra lỗi trong qu\xe1 tr\xecnh đăng nhập")}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error",verifyRequest:"/auth/verify-request",newUser:"/auth/welcome"},callbacks:{jwt:async({token:e,user:t,account:s})=>(s&&t&&(e.role=t.role,e.status=t.status,e.emailVerified=t.emailVerified),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.status=t.status,e.user.emailVerified=t.emailVerified),e),signIn:async({user:e,account:t,profile:s})=>t?.provider!=="credentials"||!0===e.emailVerified,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:s,isNewUser:r}){console.log(`User ${e.email} signed in with ${t?.provider}`)},async signOut({session:e,token:t}){console.log("User signed out")},async createUser({user:e}){console.log(`New user created: ${e.email}`)}},debug:!1}},14184:(e,t,s)=>{s.d(t,{ZP:()=>o});var r=s(11185),i=s.n(r);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=i().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},66820:(e,t,s)=>{s.d(t,{default:()=>u,mh:()=>r,xM:()=>i});var r,i,n,a=s(11185),o=s.n(a);(function(e){e.ACTIVE="active",e.COMPLETED="completed",e.SUSPENDED="suspended",e.EXPIRED="expired",e.REFUNDED="refunded"})(r||(r={})),function(e){e.PAID="paid",e.FREE="free",e.ACTIVATION_CODE="activation_code",e.ADMIN_GRANTED="admin_granted"}(i||(i={})),function(e){e.NOT_STARTED="not_started",e.IN_PROGRESS="in_progress",e.COMPLETED="completed"}(n||(n={}));let c=new a.Schema({lessonId:{type:a.Schema.Types.ObjectId,ref:"Lesson",required:!0},status:{type:String,enum:Object.values(n),default:"not_started"},startedAt:Date,completedAt:Date,watchTime:{type:Number,default:0,min:0},lastPosition:{type:Number,default:0,min:0},attempts:{type:Number,default:0,min:0},score:{type:Number,min:0,max:100},notes:String},{_id:!1}),l=new a.Schema({userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID l\xe0 bắt buộc"]},courseId:{type:a.Schema.Types.ObjectId,ref:"Course",required:[!0,"Course ID l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"active"},type:{type:String,enum:Object.values(i),required:[!0,"Loại đăng k\xfd l\xe0 bắt buộc"]},progress:{status:{type:String,enum:Object.values(n),default:"not_started"},completedLessons:{type:Number,default:0,min:0},totalLessons:{type:Number,default:0,min:0},completionPercentage:{type:Number,default:0,min:0,max:100},totalWatchTime:{type:Number,default:0,min:0},lastAccessedAt:{type:Date,default:Date.now},estimatedCompletionDate:Date,lessons:[c]},payment:{amount:{type:Number,required:!0,min:0},currency:{type:String,default:"VND"},method:{type:String,enum:["stripe","paypal","activation_code","free"],required:!0},transactionId:String,activationCode:String,paidAt:Date,refundedAt:Date,refundAmount:{type:Number,min:0}},certificate:{issued:{type:Boolean,default:!1},issuedAt:Date,certificateId:String,downloadUrl:String,validUntil:Date},accessGrantedAt:{type:Date,default:Date.now},accessExpiresAt:Date,lastAccessedAt:{type:Date,default:Date.now},enrolledBy:{type:a.Schema.Types.ObjectId,ref:"User"},notes:String},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});l.index({userId:1,courseId:1},{unique:!0}),l.index({userId:1,status:1}),l.index({courseId:1,status:1}),l.index({status:1,accessExpiresAt:1}),l.index({"payment.method":1,"payment.paidAt":-1}),l.index({"progress.status":1}),l.index({"progress.completionPercentage":-1}),l.index({lastAccessedAt:-1}),l.virtual("isActive").get(function(){return"active"===this.status&&this.canAccess()}),l.virtual("daysRemaining").get(function(){if(!this.accessExpiresAt)return null;let e=new Date;return Math.ceil((this.accessExpiresAt.getTime()-e.getTime())/864e5)}),l.pre("save",async function(e){this.isModified("progress.lessons")&&(this.progress.completedLessons=this.progress.lessons.filter(e=>"completed"===e.status).length,this.progress.completionPercentage=this.calculateCompletionPercentage(),0===this.progress.completionPercentage?this.progress.status="not_started":100===this.progress.completionPercentage?(this.progress.status="completed",this.status="completed"):this.progress.status="in_progress"),e()}),l.methods.updateProgress=async function(){let e=o().model("Lesson"),t=await e.find({courseId:this.courseId,status:"published"}).sort({order:1});for(let e of(this.progress.totalLessons=t.length,t))this.progress.lessons.find(t=>t.lessonId.toString()===e._id.toString())||this.progress.lessons.push({lessonId:e._id,status:"not_started",watchTime:0,lastPosition:0,attempts:0});this.progress.totalWatchTime=this.progress.lessons.reduce((e,t)=>e+t.watchTime,0),await this.save()},l.methods.calculateCompletionPercentage=function(){return 0===this.progress.totalLessons?0:Math.round(this.progress.completedLessons/this.progress.totalLessons*100)},l.methods.isExpired=function(){return!!this.accessExpiresAt&&new Date>this.accessExpiresAt},l.methods.canAccess=function(){return!("active"!==this.status||this.isExpired())},l.methods.generateCertificate=async function(){if("completed"!==this.progress.status)throw Error("Kh\xf3a học chưa ho\xe0n th\xe0nh");if(this.certificate.issued)throw Error("Chứng chỉ đ\xe3 được cấp");let e=`CERT-${this.courseId}-${this.userId}-${Date.now()}`;this.certificate={issued:!0,issuedAt:new Date,certificateId:e,downloadUrl:`/api/certificates/${e}`,validUntil:new Date(Date.now()+31536e6)},await this.save()},l.methods.extendAccess=async function(e){this.accessExpiresAt?this.accessExpiresAt=new Date(this.accessExpiresAt.getTime()+864e5*e):this.accessExpiresAt=new Date(Date.now()+864e5*e),await this.save()},l.statics.getActiveEnrollments=function(e){return this.find({userId:e,status:"active",$or:[{accessExpiresAt:{$exists:!1}},{accessExpiresAt:{$gt:new Date}}]}).populate("courseId")},l.statics.getCourseStats=async function(e){return(await this.aggregate([{$match:{courseId:new(o()).Types.ObjectId(e)}},{$group:{_id:null,totalEnrollments:{$sum:1},activeEnrollments:{$sum:{$cond:[{$eq:["$status","active"]},1,0]}},completedEnrollments:{$sum:{$cond:[{$eq:["$status","completed"]},1,0]}},averageProgress:{$avg:"$progress.completionPercentage"},totalRevenue:{$sum:"$payment.amount"}}}]))[0]||{totalEnrollments:0,activeEnrollments:0,completedEnrollments:0,averageProgress:0,totalRevenue:0}};let u=o().models.Enrollment||o().model("Enrollment",l)},93330:(e,t,s)=>{s.d(t,{J0:()=>i,ZP:()=>d,i4:()=>r});var r,i,n,a=s(11185),o=s.n(a),c=s(42023),l=s.n(c);(function(e){e.STUDENT="student",e.INSTRUCTOR="instructor",e.ADMIN="admin",e.SUPER_ADMIN="super_admin"})(r||(r={})),function(e){e.ACTIVE="active",e.INACTIVE="inactive",e.SUSPENDED="suspended",e.PENDING_VERIFICATION="pending_verification"}(i||(i={})),function(e){e.BEGINNER="beginner",e.ELEMENTARY="elementary",e.INTERMEDIATE="intermediate",e.UPPER_INTERMEDIATE="upper_intermediate",e.ADVANCED="advanced",e.PROFICIENT="proficient"}(n||(n={}));let u=new a.Schema({email:{type:String,required:[!0,"Email l\xe0 bắt buộc"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Email kh\xf4ng hợp lệ"]},password:{type:String,required:[!0,"Mật khẩu l\xe0 bắt buộc"],minlength:[8,"Mật khẩu phải c\xf3 \xedt nhất 8 k\xfd tự"],select:!1},role:{type:String,enum:Object.values(r),default:"student"},status:{type:String,enum:Object.values(i),default:"pending_verification"},profile:{firstName:{type:String,required:[!0,"T\xean l\xe0 bắt buộc"],trim:!0,maxlength:[50,"T\xean kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},lastName:{type:String,required:[!0,"Họ l\xe0 bắt buộc"],trim:!0,maxlength:[50,"Họ kh\xf4ng được vượt qu\xe1 50 k\xfd tự"]},dateOfBirth:Date,phoneNumber:{type:String,match:[/^[+]?[\d\s\-\(\)]+$/,"Số điện thoại kh\xf4ng hợp lệ"]},address:{street:String,city:String,state:String,country:String,zipCode:String},avatar:String,bio:{type:String,maxlength:[500,"Bio kh\xf4ng được vượt qu\xe1 500 k\xfd tự"]},languagePreferences:{native:[String],learning:[String],currentLevel:{type:String,enum:Object.values(n)}},timezone:String},preferences:{notifications:{email:{type:Boolean,default:!0},push:{type:Boolean,default:!0},sms:{type:Boolean,default:!1}},privacy:{profileVisibility:{type:String,enum:["public","private","friends"],default:"public"},showProgress:{type:Boolean,default:!0},showAchievements:{type:Boolean,default:!0}},learning:{dailyGoal:{type:Number,min:5,max:480},reminderTime:String,preferredDifficulty:{type:String,enum:["easy","medium","hard"],default:"medium"}}},emailVerified:{type:Boolean,default:!1},emailVerificationToken:String,passwordResetToken:String,passwordResetExpires:Date,lastLogin:Date,loginAttempts:{type:Number,default:0},lockUntil:Date},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});u.index({"profile.firstName":1,"profile.lastName":1}),u.index({role:1,status:1}),u.index({createdAt:-1}),u.virtual("profile.fullName").get(function(){return`${this.profile.firstName} ${this.profile.lastName}`}),u.virtual("isAccountLocked").get(function(){return!!(this.lockUntil&&this.lockUntil.getTime()>Date.now())}),u.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await l().genSalt(12);this.password=await l().hash(this.password,t),e()}catch(t){e(t)}}),u.methods.comparePassword=async function(e){return l().compare(e,this.password)},u.methods.generatePasswordResetToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.passwordResetToken=e,this.passwordResetExpires=new Date(Date.now()+6e5),e},u.methods.generateEmailVerificationToken=function(){let e=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return this.emailVerificationToken=e,e},u.methods.isLocked=function(){return!!(this.lockUntil&&this.lockUntil>Date.now())},u.methods.incrementLoginAttempts=async function(){if(this.lockUntil&&this.lockUntil<Date.now())return this.updateOne({$unset:{lockUntil:1},$set:{loginAttempts:1}});let e={$inc:{loginAttempts:1}};return this.loginAttempts+1>=5&&!this.isLocked()&&(e.$set={lockUntil:Date.now()+72e5}),this.updateOne(e)};let d=o().models.User||o().model("User",u)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,242,70,799],()=>s(69434));module.exports=r})();