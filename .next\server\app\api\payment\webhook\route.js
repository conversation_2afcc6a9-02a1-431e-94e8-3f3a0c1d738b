"use strict";(()=>{var e={};e.id=958,e.ids=[958,544],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},21764:e=>{e.exports=require("util")},86704:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>b,patchFetch:()=>I,requestAsyncStorage:()=>v,routeModule:()=>w,serverHooks:()=>x,staticGenerationAsyncStorage:()=>A});var r={};s.r(r),s.d(r,{POST:()=>m});var n=s(49303),a=s(88716),o=s(60670),i=s(87070),c=s(46029),u=s(14184),d=s(60321),p=s(66820),l=s(89332);async function m(e){try{let t;await (0,u.ZP)();let s=await e.text(),r=e.headers.get("stripe-signature");if(!r)return i.NextResponse.json({success:!1,error:"Missing Stripe signature"},{status:400});try{t=(0,c.po)(s,r)}catch(e){return console.error("Webhook signature verification failed:",e),i.NextResponse.json({success:!1,error:"Invalid signature"},{status:400})}switch(console.log("Received webhook event:",t.type),t.type){case"payment_intent.succeeded":await h(t.data.object);break;case"payment_intent.payment_failed":await f(t.data.object);break;case"payment_intent.canceled":await g(t.data.object);break;case"charge.dispute.created":await y(t.data.object);break;default:console.log(`Unhandled event type: ${t.type}`)}return i.NextResponse.json({success:!0,message:"Webhook processed successfully"})}catch(e){if(console.error("Webhook processing error:",e),e.type&&e.type.startsWith("Stripe")){let t=(0,c.ks)(e);return i.NextResponse.json({success:!1,error:t.message},{status:400})}return i.NextResponse.json({success:!1,error:"Webhook processing failed"},{status:500})}}async function h(e){try{console.log("Processing successful payment:",e.id);let t=await d.ZP.findOne({"details.stripePaymentIntentId":e.id});if(!t){console.error("Payment record not found for payment intent:",e.id);return}t.status=d.bG.COMPLETED,t.paidAt=new Date,t.details.stripeChargeId=e.latest_charge,await t.save();let s=await p.default.findOne({userId:t.userId,courseId:t.courseId});s?(s.status=p.mh.ACTIVE,s.type=p.xM.PAID,s.payment={amount:t.amount,currency:t.currency,method:"stripe",transactionId:e.id,paidAt:new Date}):s=new p.default({userId:t.userId,courseId:t.courseId,status:p.mh.ACTIVE,type:p.xM.PAID,payment:{amount:t.amount,currency:t.currency,method:"stripe",transactionId:e.id,paidAt:new Date}}),await s.updateProgress(),await s.save(),await l.ZP.findByIdAndUpdate(t.courseId,{$inc:{"stats.totalStudents":1},"stats.lastUpdated":new Date}),console.log("Payment processed successfully:",{paymentId:t._id,enrollmentId:s._id,userId:t.userId,courseId:t.courseId})}catch(e){throw console.error("Error processing successful payment:",e),e}}async function f(e){try{console.log("Processing failed payment:",e.id);let t=await d.ZP.findOne({"details.stripePaymentIntentId":e.id});if(!t){console.error("Payment record not found for payment intent:",e.id);return}t.status=d.bG.FAILED,t.failedAt=new Date,e.last_payment_error&&(t.metadata.failureReason=e.last_payment_error.message||"Unknown error",t.metadata.failureCode=e.last_payment_error.code||"unknown"),await t.save(),console.log("Failed payment processed:",{paymentId:t._id,reason:t.metadata.failureReason})}catch(e){throw console.error("Error processing failed payment:",e),e}}async function g(e){try{console.log("Processing canceled payment:",e.id);let t=await d.ZP.findOne({"details.stripePaymentIntentId":e.id});if(!t){console.error("Payment record not found for payment intent:",e.id);return}t.status=d.bG.CANCELLED,t.cancelledAt=new Date,await t.save(),console.log("Canceled payment processed:",{paymentId:t._id})}catch(e){throw console.error("Error processing canceled payment:",e),e}}async function y(e){try{console.log("Processing charge dispute:",e.id);let t=await d.ZP.findOne({"details.stripeChargeId":e.charge});if(!t){console.error("Payment record not found for charge:",e.charge);return}t.metadata.disputeId=e.id,t.metadata.disputeReason=e.reason,t.metadata.disputeStatus=e.status,t.metadata.disputeAmount=e.amount.toString(),await t.save(),console.log("Dispute processed:",{paymentId:t._id,disputeId:e.id,reason:e.reason})}catch(e){throw console.error("Error processing dispute:",e),e}}let w=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/payment/webhook/route",pathname:"/api/payment/webhook",filename:"route",bundlePath:"app/api/payment/webhook/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\api\\payment\\webhook\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:v,staticGenerationAsyncStorage:A,serverHooks:x}=w,b="/api/payment/webhook/route";function I(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:A})}},79925:e=>{var t=Object.defineProperty,s=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,a={};function o(e){var t;let s=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),r=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===s.length?r:`${r}; ${s.join("; ")}`}function i(e){let t=new Map;for(let s of e.split(/; */)){if(!s)continue;let e=s.indexOf("=");if(-1===e){t.set(s,"true");continue}let[r,n]=[s.slice(0,e),s.slice(e+1)];try{t.set(r,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function c(e){var t,s;if(!e)return;let[[r,n],...a]=i(e),{domain:o,expires:c,httponly:p,maxage:l,path:m,samesite:h,secure:f,partitioned:g,priority:y}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let s in e)e[s]&&(t[s]=e[s]);return t}({name:r,value:decodeURIComponent(n),domain:o,...c&&{expires:new Date(c)},...p&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:m,...h&&{sameSite:u.includes(t=(t=h).toLowerCase())?t:void 0},...f&&{secure:!0},...y&&{priority:d.includes(s=(s=y).toLowerCase())?s:void 0},...g&&{partitioned:!0}})}((e,s)=>{for(var r in s)t(e,r,{get:s[r],enumerable:!0})})(a,{RequestCookies:()=>p,ResponseCookies:()=>l,parseCookie:()=>i,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,a,o,i)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let o of r(a))n.call(e,o)||void 0===o||t(e,o,{get:()=>a[o],enumerable:!(i=s(a,o))||i.enumerable});return e})(t({},"__esModule",{value:!0}),a);var u=["strict","lax","none"],d=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,s]of i(t))this._parsed.set(e,{name:e,value:s})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let s=Array.from(this._parsed);if(!e.length)return s.map(([e,t])=>t);let r="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return s.filter(([e])=>e===r).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,s]=1===e.length?[e[0].name,e[0].value]:e,r=this._parsed;return r.set(t,{name:t,value:s}),this._headers.set("cookie",Array.from(r).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,s=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),s}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},l=class{constructor(e){var t,s,r;this._parsed=new Map,this._headers=e;let n=null!=(r=null!=(s=null==(t=e.getSetCookie)?void 0:t.call(e))?s:e.get("set-cookie"))?r:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,s,r,n,a,o=[],i=0;function c(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,a=!1;c();)if(","===(s=e.charAt(i))){for(r=i,i+=1,c(),n=i;i<e.length&&"="!==(s=e.charAt(i))&&";"!==s&&","!==s;)i+=1;i<e.length&&"="===e.charAt(i)?(a=!0,i=n,o.push(e.substring(t,r)),t=i):i=r+1}else i+=1;(!a||i>=e.length)&&o.push(e.substring(t,e.length))}return o}(n)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let s=Array.from(this._parsed.values());if(!e.length)return s;let r="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return s.filter(e=>e.name===r)}has(e){return this._parsed.has(e)}set(...e){let[t,s,r]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:s,...r})),function(e,t){for(let[,s]of(t.delete("set-cookie"),e)){let e=o(s);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,s,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:s,domain:r,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},49303:(e,t,s)=>{e.exports=s(30517)},92044:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{RequestCookies:function(){return r.RequestCookies},ResponseCookies:function(){return r.ResponseCookies},stringifyCookie:function(){return r.stringifyCookie}});let r=s(79925)},66820:(e,t,s)=>{s.d(t,{default:()=>d,mh:()=>r,xM:()=>n});var r,n,a,o=s(11185),i=s.n(o);(function(e){e.ACTIVE="active",e.COMPLETED="completed",e.SUSPENDED="suspended",e.EXPIRED="expired",e.REFUNDED="refunded"})(r||(r={})),function(e){e.PAID="paid",e.FREE="free",e.ACTIVATION_CODE="activation_code",e.ADMIN_GRANTED="admin_granted"}(n||(n={})),function(e){e.NOT_STARTED="not_started",e.IN_PROGRESS="in_progress",e.COMPLETED="completed"}(a||(a={}));let c=new o.Schema({lessonId:{type:o.Schema.Types.ObjectId,ref:"Lesson",required:!0},status:{type:String,enum:Object.values(a),default:"not_started"},startedAt:Date,completedAt:Date,watchTime:{type:Number,default:0,min:0},lastPosition:{type:Number,default:0,min:0},attempts:{type:Number,default:0,min:0},score:{type:Number,min:0,max:100},notes:String},{_id:!1}),u=new o.Schema({userId:{type:o.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID l\xe0 bắt buộc"]},courseId:{type:o.Schema.Types.ObjectId,ref:"Course",required:[!0,"Course ID l\xe0 bắt buộc"]},status:{type:String,enum:Object.values(r),default:"active"},type:{type:String,enum:Object.values(n),required:[!0,"Loại đăng k\xfd l\xe0 bắt buộc"]},progress:{status:{type:String,enum:Object.values(a),default:"not_started"},completedLessons:{type:Number,default:0,min:0},totalLessons:{type:Number,default:0,min:0},completionPercentage:{type:Number,default:0,min:0,max:100},totalWatchTime:{type:Number,default:0,min:0},lastAccessedAt:{type:Date,default:Date.now},estimatedCompletionDate:Date,lessons:[c]},payment:{amount:{type:Number,required:!0,min:0},currency:{type:String,default:"VND"},method:{type:String,enum:["stripe","paypal","activation_code","free"],required:!0},transactionId:String,activationCode:String,paidAt:Date,refundedAt:Date,refundAmount:{type:Number,min:0}},certificate:{issued:{type:Boolean,default:!1},issuedAt:Date,certificateId:String,downloadUrl:String,validUntil:Date},accessGrantedAt:{type:Date,default:Date.now},accessExpiresAt:Date,lastAccessedAt:{type:Date,default:Date.now},enrolledBy:{type:o.Schema.Types.ObjectId,ref:"User"},notes:String},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});u.index({userId:1,courseId:1},{unique:!0}),u.index({userId:1,status:1}),u.index({courseId:1,status:1}),u.index({status:1,accessExpiresAt:1}),u.index({"payment.method":1,"payment.paidAt":-1}),u.index({"progress.status":1}),u.index({"progress.completionPercentage":-1}),u.index({lastAccessedAt:-1}),u.virtual("isActive").get(function(){return"active"===this.status&&this.canAccess()}),u.virtual("daysRemaining").get(function(){if(!this.accessExpiresAt)return null;let e=new Date;return Math.ceil((this.accessExpiresAt.getTime()-e.getTime())/864e5)}),u.pre("save",async function(e){this.isModified("progress.lessons")&&(this.progress.completedLessons=this.progress.lessons.filter(e=>"completed"===e.status).length,this.progress.completionPercentage=this.calculateCompletionPercentage(),0===this.progress.completionPercentage?this.progress.status="not_started":100===this.progress.completionPercentage?(this.progress.status="completed",this.status="completed"):this.progress.status="in_progress"),e()}),u.methods.updateProgress=async function(){let e=i().model("Lesson"),t=await e.find({courseId:this.courseId,status:"published"}).sort({order:1});for(let e of(this.progress.totalLessons=t.length,t))this.progress.lessons.find(t=>t.lessonId.toString()===e._id.toString())||this.progress.lessons.push({lessonId:e._id,status:"not_started",watchTime:0,lastPosition:0,attempts:0});this.progress.totalWatchTime=this.progress.lessons.reduce((e,t)=>e+t.watchTime,0),await this.save()},u.methods.calculateCompletionPercentage=function(){return 0===this.progress.totalLessons?0:Math.round(this.progress.completedLessons/this.progress.totalLessons*100)},u.methods.isExpired=function(){return!!this.accessExpiresAt&&new Date>this.accessExpiresAt},u.methods.canAccess=function(){return!("active"!==this.status||this.isExpired())},u.methods.generateCertificate=async function(){if("completed"!==this.progress.status)throw Error("Kh\xf3a học chưa ho\xe0n th\xe0nh");if(this.certificate.issued)throw Error("Chứng chỉ đ\xe3 được cấp");let e=`CERT-${this.courseId}-${this.userId}-${Date.now()}`;this.certificate={issued:!0,issuedAt:new Date,certificateId:e,downloadUrl:`/api/certificates/${e}`,validUntil:new Date(Date.now()+31536e6)},await this.save()},u.methods.extendAccess=async function(e){this.accessExpiresAt?this.accessExpiresAt=new Date(this.accessExpiresAt.getTime()+864e5*e):this.accessExpiresAt=new Date(Date.now()+864e5*e),await this.save()},u.statics.getActiveEnrollments=function(e){return this.find({userId:e,status:"active",$or:[{accessExpiresAt:{$exists:!1}},{accessExpiresAt:{$gt:new Date}}]}).populate("courseId")},u.statics.getCourseStats=async function(e){return(await this.aggregate([{$match:{courseId:new(i()).Types.ObjectId(e)}},{$group:{_id:null,totalEnrollments:{$sum:1},activeEnrollments:{$sum:{$cond:[{$eq:["$status","active"]},1,0]}},completedEnrollments:{$sum:{$cond:[{$eq:["$status","completed"]},1,0]}},averageProgress:{$avg:"$progress.completionPercentage"},totalRevenue:{$sum:"$payment.amount"}}}]))[0]||{totalEnrollments:0,activeEnrollments:0,completedEnrollments:0,averageProgress:0,totalRevenue:0}};let d=i().models.Enrollment||i().model("Enrollment",u)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,70,472,791],()=>s(86704));module.exports=r})();