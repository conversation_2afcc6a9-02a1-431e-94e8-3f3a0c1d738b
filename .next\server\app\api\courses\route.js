"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/courses/route";
exports.ids = ["app/api/courses/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ADMIN_OneDrive_Desktop_WebTA_src_app_api_courses_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/courses/route.ts */ \"(rsc)/./src/app/api/courses/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/courses/route\",\n        pathname: \"/api/courses\",\n        filename: \"route\",\n        bundlePath: \"app/api/courses/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WebTA\\\\src\\\\app\\\\api\\\\courses\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ADMIN_OneDrive_Desktop_WebTA_src_app_api_courses_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/courses/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/courses/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/courses/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Course__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Course */ \"(rsc)/./src/models/Course.ts\");\n/* harmony import */ var _models_Category__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/models/Category */ \"(rsc)/./src/models/Category.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n\n\n\n\n// Cache configuration\nconst revalidate = 300 // 5 minutes\n;\n// Validation schemas\nconst createCourseSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().min(1, \"Ti\\xeau đề l\\xe0 bắt buộc\").max(200, \"Ti\\xeau đề kh\\xf4ng được vượt qu\\xe1 200 k\\xfd tự\"),\n    shortDescription: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().min(1, \"M\\xf4 tả ngắn l\\xe0 bắt buộc\").max(500, \"M\\xf4 tả ngắn kh\\xf4ng được vượt qu\\xe1 500 k\\xfd tự\"),\n    content: zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n        description: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().min(1, \"M\\xf4 tả chi tiết l\\xe0 bắt buộc\"),\n        objectives: zod__WEBPACK_IMPORTED_MODULE_7__.z.array(zod__WEBPACK_IMPORTED_MODULE_7__.z.string()).min(1, \"Phải c\\xf3 \\xedt nhất 1 mục ti\\xeau\"),\n        prerequisites: zod__WEBPACK_IMPORTED_MODULE_7__.z.array(zod__WEBPACK_IMPORTED_MODULE_7__.z.string()),\n        syllabus: zod__WEBPACK_IMPORTED_MODULE_7__.z.array(zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n            week: zod__WEBPACK_IMPORTED_MODULE_7__.z.number().min(1),\n            title: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().min(1),\n            topics: zod__WEBPACK_IMPORTED_MODULE_7__.z.array(zod__WEBPACK_IMPORTED_MODULE_7__.z.string()),\n            duration: zod__WEBPACK_IMPORTED_MODULE_7__.z.number().min(0)\n        }))\n    }),\n    category: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().min(1, \"Danh mục l\\xe0 bắt buộc\"),\n    language: zod__WEBPACK_IMPORTED_MODULE_7__.z[\"enum\"](Object.values(_models_Course__WEBPACK_IMPORTED_MODULE_4__.CourseLanguage)),\n    level: zod__WEBPACK_IMPORTED_MODULE_7__.z[\"enum\"](Object.values(_models_Course__WEBPACK_IMPORTED_MODULE_4__.CourseLevel)),\n    pricing: zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n        basePrice: zod__WEBPACK_IMPORTED_MODULE_7__.z.number().min(0, \"Gi\\xe1 kh\\xf4ng được \\xe2m\"),\n        currency: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().default(\"VND\")\n    }),\n    thumbnail: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().url().optional(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_7__.z.array(zod__WEBPACK_IMPORTED_MODULE_7__.z.string()).optional()\n});\nconst querySchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().transform((val)=>val ? parseInt(val) : 1),\n    limit: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().transform((val)=>val ? parseInt(val) : 10),\n    search: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional(),\n    category: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional(),\n    level: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional(),\n    language: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional(),\n    instructor: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().default(\"createdAt\"),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_7__.z[\"enum\"]([\n        \"asc\",\n        \"desc\"\n    ]).optional().default(\"desc\"),\n    featured: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().transform((val)=>val === \"true\"),\n    minPrice: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().transform((val)=>val ? parseFloat(val) : undefined),\n    maxPrice: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().optional().transform((val)=>val ? parseFloat(val) : undefined)\n});\n// GET /api/courses - Lấy danh sách courses với filtering và pagination\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const query = querySchema.parse(Object.fromEntries(searchParams));\n        // Build filter object\n        const filter = {};\n        // Text search\n        if (query.search) {\n            filter.$or = [\n                {\n                    title: {\n                        $regex: query.search,\n                        $options: \"i\"\n                    }\n                },\n                {\n                    shortDescription: {\n                        $regex: query.search,\n                        $options: \"i\"\n                    }\n                },\n                {\n                    \"content.description\": {\n                        $regex: query.search,\n                        $options: \"i\"\n                    }\n                },\n                {\n                    tags: {\n                        $in: [\n                            new RegExp(query.search, \"i\")\n                        ]\n                    }\n                }\n            ];\n        }\n        // Category filter\n        if (query.category) {\n            filter.category = query.category;\n        }\n        // Level filter\n        if (query.level) {\n            filter.level = query.level;\n        }\n        // Language filter\n        if (query.language) {\n            filter.language = query.language;\n        }\n        // Status filter\n        if (query.status) {\n            filter.status = query.status;\n        } else {\n            // Default to published courses for public access\n            filter.status = _models_Course__WEBPACK_IMPORTED_MODULE_4__.CourseStatus.PUBLISHED;\n        }\n        // Instructor filter\n        if (query.instructor) {\n            filter.instructor = query.instructor;\n        }\n        // Featured filter\n        if (query.featured) {\n            filter[\"metadata.isFeatured\"] = true;\n        }\n        // Price range filter\n        if (query.minPrice !== undefined || query.maxPrice !== undefined) {\n            filter[\"pricing.basePrice\"] = {};\n            if (query.minPrice !== undefined) {\n                filter[\"pricing.basePrice\"].$gte = query.minPrice;\n            }\n            if (query.maxPrice !== undefined) {\n                filter[\"pricing.basePrice\"].$lte = query.maxPrice;\n            }\n        }\n        // Build sort object\n        const sort = {};\n        sort[query.sortBy] = query.sortOrder === \"asc\" ? 1 : -1;\n        // Calculate pagination\n        const skip = (query.page - 1) * query.limit;\n        // Execute query with aggregation for better performance\n        const [courses, totalCount] = await Promise.all([\n            _models_Course__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(filter).populate(\"instructor\", \"profile.firstName profile.lastName profile.avatar\").populate(\"category\", \"name slug\").sort(sort).skip(skip).limit(query.limit).lean(),\n            _models_Course__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments(filter)\n        ]);\n        // Calculate pagination info\n        const totalPages = Math.ceil(totalCount / query.limit);\n        const hasNextPage = query.page < totalPages;\n        const hasPrevPage = query.page > 1;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                courses,\n                pagination: {\n                    currentPage: query.page,\n                    totalPages,\n                    totalCount,\n                    limit: query.limit,\n                    hasNextPage,\n                    hasPrevPage\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching courses:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_7__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Dữ liệu kh\\xf4ng hợp lệ\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Lỗi server khi lấy danh s\\xe1ch kh\\xf3a học\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/courses - Tạo course mới (chỉ instructor và admin)\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Vui l\\xf2ng đăng nhập\"\n            }, {\n                status: 401\n            });\n        }\n        // Check user role\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_6__[\"default\"].findById(session.user.id);\n        if (!user || user.role !== _models_User__WEBPACK_IMPORTED_MODULE_6__.UserRole.INSTRUCTOR && user.role !== _models_User__WEBPACK_IMPORTED_MODULE_6__.UserRole.ADMIN) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Bạn kh\\xf4ng c\\xf3 quyền tạo kh\\xf3a học\"\n            }, {\n                status: 403\n            });\n        }\n        // Parse and validate request body\n        const body = await request.json();\n        const validatedData = createCourseSchema.parse(body);\n        // Verify category exists\n        const category = await _models_Category__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findById(validatedData.category);\n        if (!category) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Danh mục kh\\xf4ng tồn tại\"\n            }, {\n                status: 400\n            });\n        }\n        // Create course\n        const course = new _models_Course__WEBPACK_IMPORTED_MODULE_4__[\"default\"]({\n            ...validatedData,\n            instructor: session.user.id,\n            slug: validatedData.title.toLowerCase().normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g, \"\").replace(/[^a-z0-9\\s-]/g, \"\").trim().replace(/\\s+/g, \"-\").replace(/-+/g, \"-\"),\n            status: _models_Course__WEBPACK_IMPORTED_MODULE_4__.CourseStatus.DRAFT,\n            stats: {\n                totalStudents: 0,\n                totalLessons: 0,\n                totalDuration: 0,\n                averageRating: 0,\n                totalRatings: 0,\n                completionRate: 0,\n                lastUpdated: new Date()\n            }\n        });\n        await course.save();\n        // Populate instructor and category info\n        await course.populate(\"instructor\", \"profile.firstName profile.lastName profile.avatar\");\n        await course.populate(\"category\", \"name slug\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: course,\n            message: \"Kh\\xf3a học đ\\xe3 được tạo th\\xe0nh c\\xf4ng\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating course:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_7__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Dữ liệu kh\\xf4ng hợp lệ\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        // Handle duplicate slug error\n        if (error.code === 11000 && error.keyPattern?.slug) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Ti\\xeau đề kh\\xf3a học đ\\xe3 tồn tại, vui l\\xf2ng chọn ti\\xeau đề kh\\xe1c\"\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Lỗi server khi tạo kh\\xf3a học\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/courses/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _next_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @next-auth/mongodb-adapter */ \"(rsc)/./node_modules/@next-auth/mongodb-adapter/dist/index.js\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n\n\n\n\n\n\n// MongoDB client for NextAuth adapter\nconst client = new mongodb__WEBPACK_IMPORTED_MODULE_3__.MongoClient(process.env.MONGODB_URI);\nconst clientPromise = client.connect();\nconst authOptions = {\n    adapter: (0,_next_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_2__.MongoDBAdapter)(clientPromise),\n    secret: process.env.NEXTAUTH_SECRET,\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Email v\\xe0 mật khẩu l\\xe0 bắt buộc\");\n                }\n                try {\n                    await (0,_mongodb__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n                    // Find user with password field\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_5__[\"default\"].findOne({\n                        email: credentials.email.toLowerCase()\n                    }).select(\"+password\");\n                    if (!user) {\n                        throw new Error(\"Email hoặc mật khẩu kh\\xf4ng đ\\xfang\");\n                    }\n                    // Check if account is locked\n                    if (user.isLocked()) {\n                        throw new Error(\"T\\xe0i khoản đ\\xe3 bị kh\\xf3a do đăng nhập sai qu\\xe1 nhiều lần\");\n                    }\n                    // Check if account is active\n                    if (user.status !== _models_User__WEBPACK_IMPORTED_MODULE_5__.UserStatus.ACTIVE) {\n                        throw new Error(\"T\\xe0i khoản chưa được k\\xedch hoạt\");\n                    }\n                    // Verify password\n                    const isPasswordValid = await user.comparePassword(credentials.password);\n                    if (!isPasswordValid) {\n                        // Increment login attempts\n                        await user.incrementLoginAttempts();\n                        throw new Error(\"Email hoặc mật khẩu kh\\xf4ng đ\\xfang\");\n                    }\n                    // Reset login attempts on successful login\n                    if (user.loginAttempts > 0) {\n                        await user.updateOne({\n                            $unset: {\n                                loginAttempts: 1,\n                                lockUntil: 1\n                            }\n                        });\n                    }\n                    // Update last login\n                    await user.updateOne({\n                        lastLogin: new Date()\n                    });\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.profile.fullName,\n                        role: user.role,\n                        status: user.status,\n                        emailVerified: user.emailVerified,\n                        image: user.profile.avatar\n                    };\n                } catch (error) {\n                    throw new Error(error.message || \"Đ\\xe3 xảy ra lỗi trong qu\\xe1 tr\\xecnh đăng nhập\");\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\",\n        verifyRequest: \"/auth/verify-request\",\n        newUser: \"/auth/welcome\"\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // Initial sign in\n            if (account && user) {\n                token.role = user.role;\n                token.status = user.status;\n                token.emailVerified = user.emailVerified;\n            }\n            // Return previous token if the access token has not expired yet\n            return token;\n        },\n        async session ({ session, token }) {\n            // Send properties to the client\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.emailVerified = token.emailVerified;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow OAuth without email verification\n            if (account?.provider !== \"credentials\") {\n                return true;\n            }\n            // For credentials, check email verification\n            return user.emailVerified === true;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            console.log(`User ${user.email} signed in with ${account?.provider}`);\n        },\n        async signOut ({ session, token }) {\n            console.log(`User signed out`);\n        },\n        async createUser ({ user }) {\n            console.log(`New user created: ${user.email}`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONNECTION_STATES: () => (/* binding */ CONNECTION_STATES),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   disconnectDB: () => (/* binding */ disconnectDB),\n/* harmony export */   getConnectionStatus: () => (/* binding */ getConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env.local\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n/**\n * Utility function để disconnect từ database\n * Chủ yếu sử dụng trong testing\n */ async function disconnectDB() {\n    if (cached.conn) {\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().disconnect();\n        cached.conn = null;\n        cached.promise = null;\n    }\n}\n/**\n * Check database connection status\n */ function getConnectionStatus() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState;\n}\n/**\n * Connection states:\n * 0 = disconnected\n * 1 = connected\n * 2 = connecting\n * 3 = disconnecting\n */ const CONNECTION_STATES = {\n    DISCONNECTED: 0,\n    CONNECTED: 1,\n    CONNECTING: 2,\n    DISCONNECTING: 3\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Category.ts":
/*!********************************!*\
  !*** ./src/models/Category.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryStatus: () => (/* binding */ CategoryStatus),\n/* harmony export */   CategoryType: () => (/* binding */ CategoryType),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nvar CategoryStatus;\n(function(CategoryStatus) {\n    CategoryStatus[\"ACTIVE\"] = \"active\";\n    CategoryStatus[\"INACTIVE\"] = \"inactive\";\n    CategoryStatus[\"ARCHIVED\"] = \"archived\";\n})(CategoryStatus || (CategoryStatus = {}));\nvar CategoryType;\n(function(CategoryType) {\n    CategoryType[\"LANGUAGE\"] = \"language\";\n    CategoryType[\"SKILL_LEVEL\"] = \"skill_level\";\n    CategoryType[\"SUBJECT\"] = \"subject\";\n    CategoryType[\"FORMAT\"] = \"format\";\n})(CategoryType || (CategoryType = {}));\nconst CategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"T\\xean danh mục l\\xe0 bắt buộc\"\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            \"T\\xean danh mục kh\\xf4ng được vượt qu\\xe1 100 k\\xfd tự\"\n        ]\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    type: {\n        type: String,\n        enum: Object.values(CategoryType),\n        required: [\n            true,\n            \"Loại danh mục l\\xe0 bắt buộc\"\n        ]\n    },\n    status: {\n        type: String,\n        enum: Object.values(CategoryStatus),\n        default: \"active\"\n    },\n    parent: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\",\n        default: null\n    },\n    metadata: {\n        description: {\n            type: String,\n            maxlength: [\n                500,\n                \"M\\xf4 tả kh\\xf4ng được vượt qu\\xe1 500 k\\xfd tự\"\n            ]\n        },\n        icon: String,\n        color: {\n            type: String,\n            match: [\n                /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,\n                \"M\\xe0u sắc phải l\\xe0 m\\xe3 hex hợp lệ\"\n            ]\n        },\n        order: {\n            type: Number,\n            default: 0,\n            min: [\n                0,\n                \"Thứ tự kh\\xf4ng được \\xe2m\"\n            ]\n        },\n        isFeature: {\n            type: Boolean,\n            default: false\n        },\n        seoTitle: {\n            type: String,\n            maxlength: [\n                60,\n                \"SEO title kh\\xf4ng được vượt qu\\xe1 60 k\\xfd tự\"\n            ]\n        },\n        seoDescription: {\n            type: String,\n            maxlength: [\n                160,\n                \"SEO description kh\\xf4ng được vượt qu\\xe1 160 k\\xfd tự\"\n            ]\n        },\n        keywords: [\n            String\n        ]\n    },\n    stats: {\n        totalCourses: {\n            type: Number,\n            default: 0\n        },\n        totalStudents: {\n            type: Number,\n            default: 0\n        },\n        averageRating: {\n            type: Number,\n            default: 0,\n            min: 0,\n            max: 5\n        },\n        totalRevenue: {\n            type: Number,\n            default: 0\n        },\n        lastUpdated: {\n            type: Date,\n            default: Date.now\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\nCategorySchema.index({\n    slug: 1\n});\nCategorySchema.index({\n    type: 1,\n    status: 1\n});\nCategorySchema.index({\n    parent: 1\n});\nCategorySchema.index({\n    \"metadata.order\": 1\n});\nCategorySchema.index({\n    \"stats.totalCourses\": -1\n});\nCategorySchema.index({\n    \"stats.averageRating\": -1\n});\n// Virtual fields\nCategorySchema.virtual(\"isParentCategory\").get(function() {\n    return !this.parent;\n});\nCategorySchema.virtual(\"fullPath\").get(async function() {\n    return await this.getFullPath();\n});\n// Pre-save middleware\nCategorySchema.pre(\"save\", function(next) {\n    if (this.isModified(\"name\") && !this.slug) {\n        this.slug = this.generateSlug();\n    }\n    next();\n});\n// Instance methods\nCategorySchema.methods.generateSlug = function() {\n    return this.name.toLowerCase().normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g, \"\") // Remove diacritics\n    .replace(/[^a-z0-9\\s-]/g, \"\") // Remove special characters\n    .trim().replace(/\\s+/g, \"-\") // Replace spaces with hyphens\n    .replace(/-+/g, \"-\") // Replace multiple hyphens with single\n    ;\n};\nCategorySchema.methods.updateStats = async function() {\n    const Course = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Course\");\n    const Enrollment = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enrollment\");\n    try {\n        // Count courses in this category\n        const totalCourses = await Course.countDocuments({\n            category: this._id,\n            status: \"published\"\n        });\n        // Count total students enrolled\n        const enrollmentStats = await Enrollment.aggregate([\n            {\n                $lookup: {\n                    from: \"courses\",\n                    localField: \"courseId\",\n                    foreignField: \"_id\",\n                    as: \"course\"\n                }\n            },\n            {\n                $match: {\n                    \"course.category\": this._id\n                }\n            },\n            {\n                $group: {\n                    _id: null,\n                    totalStudents: {\n                        $sum: 1\n                    },\n                    totalRevenue: {\n                        $sum: \"$payment.amount\"\n                    }\n                }\n            }\n        ]);\n        // Calculate average rating\n        const ratingStats = await Course.aggregate([\n            {\n                $match: {\n                    category: this._id,\n                    status: \"published\"\n                }\n            },\n            {\n                $group: {\n                    _id: null,\n                    averageRating: {\n                        $avg: \"$stats.averageRating\"\n                    }\n                }\n            }\n        ]);\n        this.stats = {\n            totalCourses,\n            totalStudents: enrollmentStats[0]?.totalStudents || 0,\n            averageRating: ratingStats[0]?.averageRating || 0,\n            totalRevenue: enrollmentStats[0]?.totalRevenue || 0,\n            lastUpdated: new Date()\n        };\n        await this.save();\n    } catch (error) {\n        console.error(\"Error updating category stats:\", error);\n    }\n};\nCategorySchema.methods.getChildren = async function() {\n    return await mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\").find({\n        parent: this._id,\n        status: \"active\"\n    }).sort({\n        \"metadata.order\": 1\n    });\n};\nCategorySchema.methods.getFullPath = async function() {\n    if (!this.parent) {\n        return this.name;\n    }\n    const parent = await mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\").findById(this.parent);\n    if (!parent) {\n        return this.name;\n    }\n    const parentPath = await parent.getFullPath();\n    return `${parentPath} > ${this.name}`;\n};\nCategorySchema.methods.isParent = function() {\n    return !this.parent;\n};\n// Static methods\nCategorySchema.statics.getHierarchy = async function() {\n    const categories = await this.find({\n        status: \"active\"\n    }).sort({\n        \"metadata.order\": 1\n    }).lean();\n    const buildTree = (parentId = null)=>{\n        return categories.filter((cat)=>String(cat.parent || null) === String(parentId)).map((cat)=>({\n                ...cat,\n                children: buildTree(cat._id)\n            }));\n    };\n    return buildTree();\n};\nCategorySchema.statics.getFeatured = async function() {\n    return await this.find({\n        status: \"active\",\n        \"metadata.isFeature\": true\n    }).sort({\n        \"metadata.order\": 1\n    }).limit(6);\n};\n// Export model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Category || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\", CategorySchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Category.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Course.ts":
/*!******************************!*\
  !*** ./src/models/Course.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CourseLanguage: () => (/* binding */ CourseLanguage),\n/* harmony export */   CourseLevel: () => (/* binding */ CourseLevel),\n/* harmony export */   CourseStatus: () => (/* binding */ CourseStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nvar CourseStatus;\n(function(CourseStatus) {\n    CourseStatus[\"DRAFT\"] = \"draft\";\n    CourseStatus[\"PUBLISHED\"] = \"published\";\n    CourseStatus[\"ARCHIVED\"] = \"archived\";\n    CourseStatus[\"SUSPENDED\"] = \"suspended\";\n})(CourseStatus || (CourseStatus = {}));\nvar CourseLevel;\n(function(CourseLevel) {\n    CourseLevel[\"BEGINNER\"] = \"beginner\";\n    CourseLevel[\"ELEMENTARY\"] = \"elementary\";\n    CourseLevel[\"INTERMEDIATE\"] = \"intermediate\";\n    CourseLevel[\"UPPER_INTERMEDIATE\"] = \"upper_intermediate\";\n    CourseLevel[\"ADVANCED\"] = \"advanced\";\n    CourseLevel[\"PROFICIENT\"] = \"proficient\";\n})(CourseLevel || (CourseLevel = {}));\nvar CourseLanguage;\n(function(CourseLanguage) {\n    CourseLanguage[\"ENGLISH\"] = \"english\";\n    CourseLanguage[\"VIETNAMESE\"] = \"vietnamese\";\n    CourseLanguage[\"CHINESE\"] = \"chinese\";\n    CourseLanguage[\"JAPANESE\"] = \"japanese\";\n    CourseLanguage[\"KOREAN\"] = \"korean\";\n    CourseLanguage[\"FRENCH\"] = \"french\";\n    CourseLanguage[\"GERMAN\"] = \"german\";\n    CourseLanguage[\"SPANISH\"] = \"spanish\";\n})(CourseLanguage || (CourseLanguage = {}));\nconst CourseSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Ti\\xeau đề kh\\xf3a học l\\xe0 bắt buộc\"\n        ],\n        trim: true,\n        maxlength: [\n            200,\n            \"Ti\\xeau đề kh\\xf4ng được vượt qu\\xe1 200 k\\xfd tự\"\n        ]\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    shortDescription: {\n        type: String,\n        required: [\n            true,\n            \"M\\xf4 tả ngắn l\\xe0 bắt buộc\"\n        ],\n        maxlength: [\n            500,\n            \"M\\xf4 tả ngắn kh\\xf4ng được vượt qu\\xe1 500 k\\xfd tự\"\n        ]\n    },\n    content: {\n        description: {\n            type: String,\n            required: [\n                true,\n                \"M\\xf4 tả chi tiết l\\xe0 bắt buộc\"\n            ]\n        },\n        objectives: [\n            {\n                type: String,\n                required: true\n            }\n        ],\n        prerequisites: [\n            String\n        ],\n        syllabus: [\n            {\n                week: {\n                    type: Number,\n                    required: true\n                },\n                title: {\n                    type: String,\n                    required: true\n                },\n                topics: [\n                    String\n                ],\n                duration: {\n                    type: Number,\n                    required: true\n                }\n            }\n        ]\n    },\n    instructor: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Giảng vi\\xean l\\xe0 bắt buộc\"\n        ]\n    },\n    category: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\",\n        required: [\n            true,\n            \"Danh mục l\\xe0 bắt buộc\"\n        ]\n    },\n    subcategory: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    language: {\n        type: String,\n        enum: Object.values(CourseLanguage),\n        required: [\n            true,\n            \"Ng\\xf4n ngữ kh\\xf3a học l\\xe0 bắt buộc\"\n        ]\n    },\n    level: {\n        type: String,\n        enum: Object.values(CourseLevel),\n        required: [\n            true,\n            \"Cấp độ kh\\xf3a học l\\xe0 bắt buộc\"\n        ]\n    },\n    status: {\n        type: String,\n        enum: Object.values(CourseStatus),\n        default: \"draft\"\n    },\n    thumbnail: String,\n    previewVideo: String,\n    tags: [\n        String\n    ],\n    pricing: {\n        basePrice: {\n            type: Number,\n            required: [\n                true,\n                \"Gi\\xe1 kh\\xf3a học l\\xe0 bắt buộc\"\n            ],\n            min: [\n                0,\n                \"Gi\\xe1 kh\\xf4ng được \\xe2m\"\n            ]\n        },\n        currency: {\n            type: String,\n            default: \"VND\"\n        },\n        discountPrice: {\n            type: Number,\n            min: [\n                0,\n                \"Gi\\xe1 giảm kh\\xf4ng được \\xe2m\"\n            ]\n        },\n        discountValidUntil: Date,\n        installmentOptions: {\n            enabled: {\n                type: Boolean,\n                default: false\n            },\n            plans: [\n                {\n                    months: {\n                        type: Number,\n                        required: true\n                    },\n                    monthlyAmount: {\n                        type: Number,\n                        required: true\n                    }\n                }\n            ]\n        }\n    },\n    settings: {\n        maxStudents: {\n            type: Number,\n            min: [\n                1,\n                \"Số học vi\\xean tối đa phải \\xedt nhất l\\xe0 1\"\n            ]\n        },\n        allowComments: {\n            type: Boolean,\n            default: true\n        },\n        allowRatings: {\n            type: Boolean,\n            default: true\n        },\n        certificateEnabled: {\n            type: Boolean,\n            default: true\n        },\n        downloadableResources: {\n            type: Boolean,\n            default: true\n        },\n        mobileAccess: {\n            type: Boolean,\n            default: true\n        },\n        lifetimeAccess: {\n            type: Boolean,\n            default: true\n        },\n        accessDuration: {\n            type: Number,\n            min: [\n                1,\n                \"Thời gian truy cập phải \\xedt nhất 1 ng\\xe0y\"\n            ]\n        }\n    },\n    stats: {\n        totalStudents: {\n            type: Number,\n            default: 0\n        },\n        totalLessons: {\n            type: Number,\n            default: 0\n        },\n        totalDuration: {\n            type: Number,\n            default: 0\n        },\n        averageRating: {\n            type: Number,\n            default: 0,\n            min: 0,\n            max: 5\n        },\n        totalRatings: {\n            type: Number,\n            default: 0\n        },\n        completionRate: {\n            type: Number,\n            default: 0,\n            min: 0,\n            max: 100\n        }\n    },\n    seo: {\n        metaTitle: {\n            type: String,\n            maxlength: [\n                60,\n                \"Meta title kh\\xf4ng được vượt qu\\xe1 60 k\\xfd tự\"\n            ]\n        },\n        metaDescription: {\n            type: String,\n            maxlength: [\n                160,\n                \"Meta description kh\\xf4ng được vượt qu\\xe1 160 k\\xfd tự\"\n            ]\n        },\n        keywords: [\n            String\n        ]\n    },\n    publishedAt: Date\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\nCourseSchema.index({\n    slug: 1\n});\nCourseSchema.index({\n    instructor: 1\n});\nCourseSchema.index({\n    category: 1,\n    subcategory: 1\n});\nCourseSchema.index({\n    language: 1,\n    level: 1\n});\nCourseSchema.index({\n    status: 1,\n    publishedAt: -1\n});\nCourseSchema.index({\n    \"stats.averageRating\": -1\n});\nCourseSchema.index({\n    \"stats.totalStudents\": -1\n});\nCourseSchema.index({\n    tags: 1\n});\nCourseSchema.index({\n    \"pricing.basePrice\": 1\n});\n// Virtual fields\nCourseSchema.virtual(\"isOnSale\").get(function() {\n    return !!(this.pricing.discountPrice && this.pricing.discountValidUntil && this.pricing.discountValidUntil > new Date());\n});\nCourseSchema.virtual(\"currentPrice\").get(function() {\n    if (this.isOnSale) {\n        return this.pricing.discountPrice;\n    }\n    return this.pricing.basePrice;\n});\n// Pre-save middleware\nCourseSchema.pre(\"save\", function(next) {\n    if (this.isModified(\"title\") && !this.slug) {\n        this.slug = this.generateSlug();\n    }\n    next();\n});\n// Instance methods\nCourseSchema.methods.generateSlug = function() {\n    return this.title.toLowerCase().normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g, \"\") // Remove diacritics\n    .replace(/[^a-z0-9\\s-]/g, \"\") // Remove special characters\n    .trim().replace(/\\s+/g, \"-\") // Replace spaces with hyphens\n    .replace(/-+/g, \"-\") // Replace multiple hyphens with single\n    ;\n};\nCourseSchema.methods.updateStats = async function() {\n    // This will be implemented when we have Enrollment and Lesson models\n    // For now, just a placeholder\n    console.log(\"Updating course stats...\");\n};\nCourseSchema.methods.isPublished = function() {\n    return this.status === \"published\" && !!this.publishedAt;\n};\nCourseSchema.methods.canEnroll = function() {\n    if (!this.isPublished()) return false;\n    if (this.settings.maxStudents && this.stats.totalStudents >= this.settings.maxStudents) {\n        return false;\n    }\n    return true;\n};\n// Export model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Course || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Course\", CourseSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Course.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageLevel: () => (/* binding */ LanguageLevel),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   UserStatus: () => (/* binding */ UserStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"STUDENT\"] = \"student\";\n    UserRole[\"INSTRUCTOR\"] = \"instructor\";\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n})(UserRole || (UserRole = {}));\nvar UserStatus;\n(function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n    UserStatus[\"PENDING_VERIFICATION\"] = \"pending_verification\";\n})(UserStatus || (UserStatus = {}));\nvar LanguageLevel;\n(function(LanguageLevel) {\n    LanguageLevel[\"BEGINNER\"] = \"beginner\";\n    LanguageLevel[\"ELEMENTARY\"] = \"elementary\";\n    LanguageLevel[\"INTERMEDIATE\"] = \"intermediate\";\n    LanguageLevel[\"UPPER_INTERMEDIATE\"] = \"upper_intermediate\";\n    LanguageLevel[\"ADVANCED\"] = \"advanced\";\n    LanguageLevel[\"PROFICIENT\"] = \"proficient\";\n})(LanguageLevel || (LanguageLevel = {}));\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: [\n            true,\n            \"Email l\\xe0 bắt buộc\"\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            \"Email kh\\xf4ng hợp lệ\"\n        ]\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"Mật khẩu l\\xe0 bắt buộc\"\n        ],\n        minlength: [\n            8,\n            \"Mật khẩu phải c\\xf3 \\xedt nhất 8 k\\xfd tự\"\n        ],\n        select: false // Không trả về password khi query\n    },\n    role: {\n        type: String,\n        enum: Object.values(UserRole),\n        default: \"student\"\n    },\n    status: {\n        type: String,\n        enum: Object.values(UserStatus),\n        default: \"pending_verification\"\n    },\n    profile: {\n        firstName: {\n            type: String,\n            required: [\n                true,\n                \"T\\xean l\\xe0 bắt buộc\"\n            ],\n            trim: true,\n            maxlength: [\n                50,\n                \"T\\xean kh\\xf4ng được vượt qu\\xe1 50 k\\xfd tự\"\n            ]\n        },\n        lastName: {\n            type: String,\n            required: [\n                true,\n                \"Họ l\\xe0 bắt buộc\"\n            ],\n            trim: true,\n            maxlength: [\n                50,\n                \"Họ kh\\xf4ng được vượt qu\\xe1 50 k\\xfd tự\"\n            ]\n        },\n        dateOfBirth: Date,\n        phoneNumber: {\n            type: String,\n            match: [\n                /^[+]?[\\d\\s\\-\\(\\)]+$/,\n                \"Số điện thoại kh\\xf4ng hợp lệ\"\n            ]\n        },\n        address: {\n            street: String,\n            city: String,\n            state: String,\n            country: String,\n            zipCode: String\n        },\n        avatar: String,\n        bio: {\n            type: String,\n            maxlength: [\n                500,\n                \"Bio kh\\xf4ng được vượt qu\\xe1 500 k\\xfd tự\"\n            ]\n        },\n        languagePreferences: {\n            native: [\n                String\n            ],\n            learning: [\n                String\n            ],\n            currentLevel: {\n                type: String,\n                enum: Object.values(LanguageLevel)\n            }\n        },\n        timezone: String\n    },\n    preferences: {\n        notifications: {\n            email: {\n                type: Boolean,\n                default: true\n            },\n            push: {\n                type: Boolean,\n                default: true\n            },\n            sms: {\n                type: Boolean,\n                default: false\n            }\n        },\n        privacy: {\n            profileVisibility: {\n                type: String,\n                enum: [\n                    \"public\",\n                    \"private\",\n                    \"friends\"\n                ],\n                default: \"public\"\n            },\n            showProgress: {\n                type: Boolean,\n                default: true\n            },\n            showAchievements: {\n                type: Boolean,\n                default: true\n            }\n        },\n        learning: {\n            dailyGoal: {\n                type: Number,\n                min: 5,\n                max: 480\n            },\n            reminderTime: String,\n            preferredDifficulty: {\n                type: String,\n                enum: [\n                    \"easy\",\n                    \"medium\",\n                    \"hard\"\n                ],\n                default: \"medium\"\n            }\n        }\n    },\n    emailVerified: {\n        type: Boolean,\n        default: false\n    },\n    emailVerificationToken: String,\n    passwordResetToken: String,\n    passwordResetExpires: Date,\n    lastLogin: Date,\n    loginAttempts: {\n        type: Number,\n        default: 0\n    },\n    lockUntil: Date\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\n// Note: email index is automatically created by unique: true in schema\nUserSchema.index({\n    \"profile.firstName\": 1,\n    \"profile.lastName\": 1\n});\nUserSchema.index({\n    role: 1,\n    status: 1\n});\nUserSchema.index({\n    createdAt: -1\n});\n// Virtual fields\nUserSchema.virtual(\"profile.fullName\").get(function() {\n    return `${this.profile.firstName} ${this.profile.lastName}`;\n});\nUserSchema.virtual(\"isAccountLocked\").get(function() {\n    return !!(this.lockUntil && this.lockUntil.getTime() > Date.now());\n});\n// Pre-save middleware\nUserSchema.pre(\"save\", async function(next) {\n    if (!this.isModified(\"password\")) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(candidatePassword, this.password);\n};\nUserSchema.methods.generatePasswordResetToken = function() {\n    const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.passwordResetToken = resetToken;\n    this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes\n    ;\n    return resetToken;\n};\nUserSchema.methods.generateEmailVerificationToken = function() {\n    const verificationToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.emailVerificationToken = verificationToken;\n    return verificationToken;\n};\nUserSchema.methods.isLocked = function() {\n    return !!(this.lockUntil && this.lockUntil > Date.now());\n};\nUserSchema.methods.incrementLoginAttempts = async function() {\n    // If we have a previous lock that has expired, restart at 1\n    if (this.lockUntil && this.lockUntil < Date.now()) {\n        return this.updateOne({\n            $unset: {\n                lockUntil: 1\n            },\n            $set: {\n                loginAttempts: 1\n            }\n        });\n    }\n    const updates = {\n        $inc: {\n            loginAttempts: 1\n        }\n    };\n    // Lock account after 5 failed attempts for 2 hours\n    if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {\n        updates.$set = {\n            lockUntil: Date.now() + 2 * 60 * 60 * 1000\n        } // 2 hours\n        ;\n    }\n    return this.updateOne(updates);\n};\n// Export model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/bcryptjs","vendor-chunks/jose","vendor-chunks/zod","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/@next-auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.ts&appDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMIN%5COneDrive%5CDesktop%5CWebTA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();