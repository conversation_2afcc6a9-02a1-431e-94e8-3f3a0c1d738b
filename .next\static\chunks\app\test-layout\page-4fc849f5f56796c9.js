(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[298],{4980:function(e,s,a){Promise.resolve().then(a.bind(a,2609))},2609:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return i}});var r=a(7437),t=a(7648),n=a(757),l=a(6334);function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)(n.Zb,{className:"p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Layout Testing Page"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Layout Structure Test"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Trang n\xe0y để test layout structure v\xe0 đảm bảo kh\xf4ng c\xf3 duplicate headers/footers."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zb,{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Main App Routes"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c routes n\xe0y sẽ c\xf3 Header v\xe0 Footer"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(t.default,{href:"/",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83C\uDFE0 Home Page"})}),(0,r.jsx)(t.default,{href:"/courses",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDCDA Courses Page"})}),(0,r.jsx)(t.default,{href:"/about",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"ℹ️ About Page"})}),(0,r.jsx)(t.default,{href:"/contact",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDCDE Contact Page"})})]})]}),(0,r.jsxs)(n.Zb,{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Dashboard Routes"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c routes n\xe0y c\xf3 layout ri\xeang, kh\xf4ng c\xf3 Header/Footer ch\xednh"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(t.default,{href:"/dashboard",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDCCA Dashboard (Auto-redirect)"})}),(0,r.jsx)(t.default,{href:"/dashboard/student",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83C\uDF93 Student Dashboard"})}),(0,r.jsx)(t.default,{href:"/dashboard/instructor",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83D\uDC68‍\uD83C\uDFEB Instructor Dashboard"})}),(0,r.jsx)(t.default,{href:"/test-dashboard",className:"block",children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full justify-start",children:"\uD83E\uDDEA Dashboard API Test"})})]})]})]}),(0,r.jsxs)(n.Zb,{className:"p-6 bg-blue-50 border-blue-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4 text-blue-900",children:"Layout Verification Checklist"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-green-600",children:"✅"}),(0,r.jsx)("span",{children:"Main app routes c\xf3 Header v\xe0 Footer"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-green-600",children:"✅"}),(0,r.jsx)("span",{children:"Dashboard routes c\xf3 navigation ri\xeang"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-green-600",children:"✅"}),(0,r.jsx)("span",{children:"Kh\xf4ng c\xf3 duplicate headers"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-green-600",children:"✅"}),(0,r.jsx)("span",{children:"Layout transitions mượt m\xe0"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-green-600",children:"✅"}),(0,r.jsx)("span",{children:"Responsive design hoạt động đ\xfang"})]})]})]}),(0,r.jsxs)(n.Zb,{className:"p-6 bg-gray-50",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Current Route Info"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Current Path:"})," /test-layout"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Layout Type:"})," Main App Layout (with Header/Footer)"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Expected Behavior:"})," Should show main navigation header and footer"]})]})]}),(0,r.jsxs)(n.Zb,{className:"p-6 bg-yellow-50 border-yellow-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4 text-yellow-900",children:"Testing Instructions"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm text-yellow-800",children:[(0,r.jsx)("li",{children:"Kiểm tra trang n\xe0y c\xf3 Header v\xe0 Footer kh\xf4ng"}),(0,r.jsx)("li",{children:"Click v\xe0o c\xe1c main app routes v\xe0 verify c\xf3 Header/Footer"}),(0,r.jsx)("li",{children:"Click v\xe0o dashboard routes v\xe0 verify chỉ c\xf3 dashboard navigation"}),(0,r.jsx)("li",{children:"Kiểm tra kh\xf4ng c\xf3 duplicate navigation bars"}),(0,r.jsx)("li",{children:"Test responsive behavior tr\xean mobile v\xe0 desktop"}),(0,r.jsx)("li",{children:"Verify layout transitions mượt m\xe0"})]})]}),(0,r.jsx)("div",{className:"text-center pt-6",children:(0,r.jsx)(t.default,{href:"/",children:(0,r.jsx)(l.z,{children:"← Về trang chủ"})})})]})]})})})}},6334:function(e,s,a){"use strict";a.d(s,{z:function(){return c}});var r=a(7437),t=a(2265),n=a(535),l=a(3448);let i=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,size:n,asChild:c=!1,...d}=e;return(0,r.jsx)("button",{className:(0,l.cn)(i({variant:t,size:n,className:a})),ref:s,...d})});c.displayName="Button"},757:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c}});var r=a(7437),t=a(2265),n=a(535),l=a(3448);let i=(0,n.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,size:n,...c}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)(i({variant:t,size:n}),a),...c})});c.displayName="Card",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...t})}).displayName="CardHeader",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})}).displayName="CardTitle",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...t})}).displayName="CardDescription",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",a),...t})}).displayName="CardContent",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",a),...t})}).displayName="CardFooter"},3448:function(e,s,a){"use strict";a.d(s,{cn:function(){return n}});var r=a(1994),t=a(3335);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.m6)((0,r.W)(s))}}},function(e){e.O(0,[851,648,971,117,744],function(){return e(e.s=4980)}),_N_E=e.O()}]);