import mongoose, { Document, Schema } from 'mongoose'

// Enum definitions
export enum CourseStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  SUSPENDED = 'suspended'
}

export enum CourseLevel {
  BEGINNER = 'beginner',
  ELEMENTARY = 'elementary', 
  INTERMEDIATE = 'intermediate',
  UPPER_INTERMEDIATE = 'upper_intermediate',
  ADVANCED = 'advanced',
  PROFICIENT = 'proficient'
}

export enum CourseLanguage {
  ENGLISH = 'english',
  VIETNAMESE = 'vietnamese',
  CHINESE = 'chinese',
  JAPANESE = 'japanese',
  KOREAN = 'korean',
  FRENCH = 'french',
  GERMAN = 'german',
  SPANISH = 'spanish'
}

// Interface definitions
export interface ICourseContent {
  description: string
  objectives: string[]
  prerequisites: string[]
  syllabus: {
    week: number
    title: string
    topics: string[]
    duration: number // in minutes
  }[]
}

export interface ICoursePricing {
  basePrice: number
  currency: string
  discountPrice?: number
  discountValidUntil?: Date
  installmentOptions?: {
    enabled: boolean
    plans: {
      months: number
      monthlyAmount: number
    }[]
  }
}

export interface ICourseSettings {
  maxStudents?: number
  allowComments: boolean
  allowRatings: boolean
  certificateEnabled: boolean
  downloadableResources: boolean
  mobileAccess: boolean
  lifetimeAccess: boolean
  accessDuration?: number // in days, null for lifetime
}

export interface ICourse extends Document {
  title: string
  slug: string
  shortDescription: string
  content: ICourseContent
  instructor: mongoose.Types.ObjectId
  category: mongoose.Types.ObjectId
  subcategory?: mongoose.Types.ObjectId
  language: CourseLanguage
  level: CourseLevel
  status: CourseStatus
  thumbnail?: string
  previewVideo?: string
  tags: string[]
  pricing: ICoursePricing
  settings: ICourseSettings
  
  // Statistics
  stats: {
    totalStudents: number
    totalLessons: number
    totalDuration: number // in minutes
    averageRating: number
    totalRatings: number
    completionRate: number
  }
  
  // SEO
  seo: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
  }
  
  // Timestamps
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  
  // Methods
  generateSlug(): string
  updateStats(): Promise<void>
  isPublished(): boolean
  canEnroll(): boolean
}

const CourseSchema = new Schema<ICourse>({
  title: {
    type: String,
    required: [true, 'Tiêu đề khóa học là bắt buộc'],
    trim: true,
    maxlength: [200, 'Tiêu đề không được vượt quá 200 ký tự']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  shortDescription: {
    type: String,
    required: [true, 'Mô tả ngắn là bắt buộc'],
    maxlength: [500, 'Mô tả ngắn không được vượt quá 500 ký tự']
  },
  content: {
    description: {
      type: String,
      required: [true, 'Mô tả chi tiết là bắt buộc']
    },
    objectives: [{
      type: String,
      required: true
    }],
    prerequisites: [String],
    syllabus: [{
      week: { type: Number, required: true },
      title: { type: String, required: true },
      topics: [String],
      duration: { type: Number, required: true }
    }]
  },
  instructor: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Giảng viên là bắt buộc']
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Danh mục là bắt buộc']
  },
  subcategory: {
    type: Schema.Types.ObjectId,
    ref: 'Category'
  },
  language: {
    type: String,
    enum: Object.values(CourseLanguage),
    required: [true, 'Ngôn ngữ khóa học là bắt buộc']
  },
  level: {
    type: String,
    enum: Object.values(CourseLevel),
    required: [true, 'Cấp độ khóa học là bắt buộc']
  },
  status: {
    type: String,
    enum: Object.values(CourseStatus),
    default: CourseStatus.DRAFT
  },
  thumbnail: String,
  previewVideo: String,
  tags: [String],
  pricing: {
    basePrice: {
      type: Number,
      required: [true, 'Giá khóa học là bắt buộc'],
      min: [0, 'Giá không được âm']
    },
    currency: {
      type: String,
      default: 'VND'
    },
    discountPrice: {
      type: Number,
      min: [0, 'Giá giảm không được âm']
    },
    discountValidUntil: Date,
    installmentOptions: {
      enabled: { type: Boolean, default: false },
      plans: [{
        months: { type: Number, required: true },
        monthlyAmount: { type: Number, required: true }
      }]
    }
  },
  settings: {
    maxStudents: {
      type: Number,
      min: [1, 'Số học viên tối đa phải ít nhất là 1']
    },
    allowComments: { type: Boolean, default: true },
    allowRatings: { type: Boolean, default: true },
    certificateEnabled: { type: Boolean, default: true },
    downloadableResources: { type: Boolean, default: true },
    mobileAccess: { type: Boolean, default: true },
    lifetimeAccess: { type: Boolean, default: true },
    accessDuration: {
      type: Number,
      min: [1, 'Thời gian truy cập phải ít nhất 1 ngày']
    }
  },
  stats: {
    totalStudents: { type: Number, default: 0 },
    totalLessons: { type: Number, default: 0 },
    totalDuration: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0, min: 0, max: 5 },
    totalRatings: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0, min: 0, max: 100 }
  },
  seo: {
    metaTitle: {
      type: String,
      maxlength: [60, 'Meta title không được vượt quá 60 ký tự']
    },
    metaDescription: {
      type: String,
      maxlength: [160, 'Meta description không được vượt quá 160 ký tự']
    },
    keywords: [String]
  },
  publishedAt: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
CourseSchema.index({ slug: 1 })
CourseSchema.index({ instructor: 1 })
CourseSchema.index({ category: 1, subcategory: 1 })
CourseSchema.index({ language: 1, level: 1 })
CourseSchema.index({ status: 1, publishedAt: -1 })
CourseSchema.index({ 'stats.averageRating': -1 })
CourseSchema.index({ 'stats.totalStudents': -1 })
CourseSchema.index({ tags: 1 })
CourseSchema.index({ 'pricing.basePrice': 1 })

// Virtual fields
CourseSchema.virtual('isOnSale').get(function() {
  return !!(this.pricing.discountPrice && 
           this.pricing.discountValidUntil && 
           this.pricing.discountValidUntil > new Date())
})

CourseSchema.virtual('currentPrice').get(function() {
  if (this.isOnSale) {
    return this.pricing.discountPrice
  }
  return this.pricing.basePrice
})

// Pre-save middleware
CourseSchema.pre('save', function(next) {
  if (this.isModified('title') && !this.slug) {
    this.slug = this.generateSlug()
  }
  next()
})

// Instance methods
CourseSchema.methods.generateSlug = function(): string {
  return this.title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
}

CourseSchema.methods.updateStats = async function(): Promise<void> {
  // This will be implemented when we have Enrollment and Lesson models
  // For now, just a placeholder
  console.log('Updating course stats...')
}

CourseSchema.methods.isPublished = function(): boolean {
  return this.status === CourseStatus.PUBLISHED && !!this.publishedAt
}

CourseSchema.methods.canEnroll = function(): boolean {
  if (!this.isPublished()) return false
  if (this.settings.maxStudents && this.stats.totalStudents >= this.settings.maxStudents) {
    return false
  }
  return true
}

// Export model
export default mongoose.models.Course || mongoose.model<ICourse>('Course', CourseSchema)
