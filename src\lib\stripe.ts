import Stripe from 'stripe'

// Initialize Stripe
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
  typescript: true,
})

// Stripe configuration
export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  currency: 'vnd',
  country: 'VN',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
}

// Product and price management
export async function createStripeProduct(courseData: {
  name: string
  description: string
  images?: string[]
  metadata?: Record<string, string>
}): Promise<Stripe.Product> {
  return await stripe.products.create({
    name: courseData.name,
    description: courseData.description,
    images: courseData.images || [],
    metadata: {
      type: 'course',
      ...courseData.metadata
    }
  })
}

export async function createStripePrice(productId: string, priceData: {
  amount: number
  currency?: string
  metadata?: Record<string, string>
}): Promise<Stripe.Price> {
  return await stripe.prices.create({
    product: productId,
    unit_amount: priceData.amount,
    currency: priceData.currency || STRIPE_CONFIG.currency,
    metadata: priceData.metadata || {}
  })
}

// Payment Intent management
export async function createPaymentIntent(data: {
  amount: number
  currency?: string
  courseId: string
  userId: string
  metadata?: Record<string, string>
}): Promise<Stripe.PaymentIntent> {
  return await stripe.paymentIntents.create({
    amount: data.amount,
    currency: data.currency || STRIPE_CONFIG.currency,
    automatic_payment_methods: {
      enabled: true,
    },
    metadata: {
      courseId: data.courseId,
      userId: data.userId,
      type: 'course_purchase',
      ...data.metadata
    }
  })
}

export async function confirmPaymentIntent(
  paymentIntentId: string
): Promise<Stripe.PaymentIntent> {
  return await stripe.paymentIntents.confirm(paymentIntentId)
}

export async function retrievePaymentIntent(
  paymentIntentId: string
): Promise<Stripe.PaymentIntent> {
  return await stripe.paymentIntents.retrieve(paymentIntentId)
}

// Refund management
export async function createRefund(data: {
  paymentIntentId: string
  amount?: number
  reason?: Stripe.RefundCreateParams.Reason
  metadata?: Record<string, string>
}): Promise<Stripe.Refund> {
  return await stripe.refunds.create({
    payment_intent: data.paymentIntentId,
    amount: data.amount,
    reason: data.reason || 'requested_by_customer',
    metadata: data.metadata || {}
  })
}

// Customer management
export async function createStripeCustomer(userData: {
  email: string
  name?: string
  metadata?: Record<string, string>
}): Promise<Stripe.Customer> {
  return await stripe.customers.create({
    email: userData.email,
    name: userData.name,
    metadata: {
      source: 'webta_lms',
      ...userData.metadata
    }
  })
}

export async function retrieveStripeCustomer(
  customerId: string
): Promise<Stripe.Customer> {
  return await stripe.customers.retrieve(customerId) as Stripe.Customer
}

// Webhook verification
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string
): Stripe.Event {
  return stripe.webhooks.constructEvent(
    payload,
    signature,
    STRIPE_CONFIG.webhookSecret
  )
}

// Utility functions
export function formatStripeAmount(amount: number, currency: string = 'vnd'): string {
  // Stripe amounts are in smallest currency unit
  // For VND, this is already the base unit
  if (currency.toLowerCase() === 'vnd') {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }
  
  // For other currencies, divide by 100
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(amount / 100)
}

export function convertToStripeAmount(amount: number, currency: string = 'vnd'): number {
  // For VND, Stripe uses the base unit (no conversion needed)
  if (currency.toLowerCase() === 'vnd') {
    return Math.round(amount)
  }
  
  // For other currencies, multiply by 100 to get smallest unit
  return Math.round(amount * 100)
}

// Error handling
export function handleStripeError(error: any): {
  message: string
  type: string
  code?: string
} {
  if (error.type === 'StripeCardError') {
    return {
      message: 'Thẻ của bạn bị từ chối. Vui lòng kiểm tra thông tin thẻ.',
      type: 'card_error',
      code: error.code
    }
  }
  
  if (error.type === 'StripeRateLimitError') {
    return {
      message: 'Quá nhiều yêu cầu. Vui lòng thử lại sau.',
      type: 'rate_limit_error'
    }
  }
  
  if (error.type === 'StripeInvalidRequestError') {
    return {
      message: 'Yêu cầu không hợp lệ. Vui lòng thử lại.',
      type: 'invalid_request_error',
      code: error.code
    }
  }
  
  if (error.type === 'StripeAPIError') {
    return {
      message: 'Lỗi hệ thống thanh toán. Vui lòng thử lại sau.',
      type: 'api_error'
    }
  }
  
  if (error.type === 'StripeConnectionError') {
    return {
      message: 'Lỗi kết nối. Vui lòng kiểm tra mạng và thử lại.',
      type: 'connection_error'
    }
  }
  
  if (error.type === 'StripeAuthenticationError') {
    return {
      message: 'Lỗi xác thực thanh toán.',
      type: 'authentication_error'
    }
  }
  
  return {
    message: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại.',
    type: 'unknown_error'
  }
}

export default stripe
