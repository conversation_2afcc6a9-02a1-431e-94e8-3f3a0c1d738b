'use client'

import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'
import Link from 'next/link'
import { Loading } from '@/components/ui/Loading'
import { Button } from '@/components/ui/Button'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loading size="lg" />
      </div>
    )
  }

  if (!session) {
    return null
  }

  // Determine which dashboard to show based on user role
  const userRole = session.user?.role || 'student'

  // Redirect to appropriate dashboard if on base /dashboard route
  useEffect(() => {
    if (pathname === '/dashboard') {
      if (userRole === 'instructor' || userRole === 'admin') {
        router.push('/dashboard/instructor')
      } else {
        router.push('/dashboard/student')
      }
    }
  }, [pathname, userRole, router])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Dashboard Navigation - Replaces main header for dashboard pages */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center">
                <div className="text-2xl font-bold text-primary">WebTA</div>
                <span className="ml-2 text-sm text-gray-600">LMS</span>
              </Link>

              {/* Dashboard Navigation */}
              <nav className="hidden md:flex space-x-1">
                <Link
                  href="/dashboard/student"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    pathname === '/dashboard/student'
                      ? 'bg-primary text-white'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Dashboard Học viên
                </Link>

                {(userRole === 'instructor' || userRole === 'admin') && (
                  <Link
                    href="/dashboard/instructor"
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      pathname === '/dashboard/instructor'
                        ? 'bg-primary text-white'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    Dashboard Giảng viên
                  </Link>
                )}

                <Link
                  href="/courses"
                  className="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                >
                  Khóa học
                </Link>
              </nav>
            </div>

            {/* User Menu */}
            <div className="flex items-center space-x-4">
              <span className="hidden sm:block text-sm text-gray-600">
                Xin chào, {session.user?.name || session.user?.email}
              </span>

              <div className="flex items-center space-x-2">
                <Link
                  href="/profile"
                  className="text-sm text-gray-600 hover:text-gray-900 px-2 py-1 rounded transition-colors"
                >
                  Hồ sơ
                </Link>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/api/auth/signout')}
                  className="text-sm"
                >
                  Đăng xuất
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Dashboard Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
}
