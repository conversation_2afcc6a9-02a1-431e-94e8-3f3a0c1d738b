(()=>{var e={};e.id=506,e.ids=[506],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},43313:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),s(62508),s(39285),s(35866);var a=s(23191),t=s(88716),d=s(37922),n=s.n(d),l=s(95231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(r,i);let o=["",{children:["test-dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62508)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\test-dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,39285)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\WebTA\\src\\app\\test-dashboard\\page.tsx"],m="/test-dashboard/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/test-dashboard/page",pathname:"/test-dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},47036:(e,r,s)=>{Promise.resolve().then(s.bind(s,61805))},61805:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var a=s(10326),t=s(17577),d=s(77109),n=s(99837),l=s(47375),i=s(16545);function o(){let{data:e,status:r}=(0,d.useSession)(),[s,o]=(0,t.useState)(null),[c,m]=(0,t.useState)(null),[x,u]=(0,t.useState)(!1),[h,b]=(0,t.useState)(null),p=async()=>{u(!0),b(null);try{let e=await fetch("/api/dashboard/student"),r=await e.json();r.success?o(r.data):b(`Student API Error: ${r.error}`)}catch(e){b(`Student API Error: ${e}`)}finally{u(!1)}},f=async()=>{u(!0),b(null);try{let e=await fetch("/api/dashboard/instructor"),r=await e.json();r.success?m(r.data):b(`Instructor API Error: ${r.error}`)}catch(e){b(`Instructor API Error: ${e}`)}finally{u(!1)}};return"loading"===r?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(i.gb,{size:"lg"})}):e?a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(l.Zb,{className:"p-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Dashboard API Test"}),(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Session Information"}),a.jsx("div",{className:"bg-gray-100 p-4 rounded-lg",children:a.jsx("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e,null,2)})})]}),(0,a.jsxs)("div",{className:"flex gap-4 mb-8",children:[a.jsx(n.z,{onClick:p,disabled:x,children:x?a.jsx(i.gb,{size:"sm"}):"Test Student API"}),a.jsx(n.z,{onClick:f,disabled:x,variant:"outline",children:x?a.jsx(i.gb,{size:"sm"}):"Test Instructor API"})]}),h&&(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h3",{className:"text-lg font-semibold text-red-600 mb-2",children:"Error:"}),a.jsx("div",{className:"bg-red-50 border border-red-200 p-4 rounded-lg",children:a.jsx("p",{className:"text-red-800",children:h})})]}),s&&(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Student Dashboard Data:"}),a.jsx("div",{className:"bg-green-50 border border-green-200 p-4 rounded-lg",children:a.jsx("pre",{className:"text-sm overflow-auto",children:JSON.stringify(s,null,2)})})]}),c&&(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Instructor Dashboard Data:"}),a.jsx("div",{className:"bg-blue-50 border border-blue-200 p-4 rounded-lg",children:a.jsx("pre",{className:"text-sm overflow-auto",children:JSON.stringify(c,null,2)})})]}),(0,a.jsxs)("div",{className:"mt-8 pt-8 border-t",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Navigation Test:"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx("a",{href:"/dashboard/student",className:"text-blue-600 hover:underline",children:"Go to Student Dashboard"}),a.jsx("a",{href:"/dashboard/instructor",className:"text-blue-600 hover:underline",children:"Go to Instructor Dashboard"}),a.jsx("a",{href:"/dashboard",className:"text-blue-600 hover:underline",children:"Go to Dashboard (Auto-redirect)"})]})]})]})})}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)(l.Zb,{className:"p-8",children:[a.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Dashboard Test"}),a.jsx("p",{className:"text-red-600",children:"Bạn cần đăng nhập để test dashboard"})]})})}},47375:(e,r,s)=>{"use strict";s.d(r,{Zb:()=>i});var a=s(10326),t=s(17577),d=s(79360),n=s(51223);let l=(0,d.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),i=t.forwardRef(({className:e,variant:r,size:s,...t},d)=>a.jsx("div",{ref:d,className:(0,n.cn)(l({variant:r,size:s}),e),...t}));i.displayName="Card",t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",t.forwardRef(({className:e,...r},s)=>a.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",t.forwardRef(({className:e,...r},s)=>a.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription",t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...r})).displayName="CardContent",t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},16545:(e,r,s)=>{"use strict";s.d(r,{gb:()=>l});var a=s(10326),t=s(79360),d=s(51223);let n=(0,t.j)("animate-spin rounded-full border-2",{variants:{variant:{default:"border-gray-300 border-t-primary",primary:"border-primary/20 border-t-primary",secondary:"border-secondary/20 border-t-secondary",success:"border-green-200 border-t-green-600",warning:"border-yellow-200 border-t-yellow-600",error:"border-red-200 border-t-red-600"},size:{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"}},defaultVariants:{variant:"default",size:"md"}});function l({variant:e,size:r,className:s,text:t}){return a.jsx("div",{className:(0,d.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[a.jsx("div",{className:(0,d.cn)(n({variant:e,size:r}))}),t&&a.jsx("p",{className:"text-sm text-gray-600",children:t})]})})}},62508:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\WebTA\src\app\test-dashboard\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[276,105,826],()=>s(43313));module.exports=a})();