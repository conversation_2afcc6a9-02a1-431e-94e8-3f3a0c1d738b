"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[777],{8629:function(e,r,s){s.d(r,{g7:function(){return m}});var a=s(7437),n=s(2265),t=s(535),l=s(3448);let i=(0,t.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 [&>svg]:text-green-600",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 [&>svg]:text-yellow-600",info:"border-blue-500/50 text-blue-700 bg-blue-50 [&>svg]:text-blue-600"}},defaultVariants:{variant:"default"}}),d=n.forwardRef((e,r)=>{let{className:s,variant:n,...t}=e;return(0,a.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(i({variant:n}),s),...t})});d.displayName="Alert";let o=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",s),...n})});o.displayName="AlertTitle";let c=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",s),...n})});c.displayName="AlertDescription";let u={success:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),warning:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),info:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})};function m(e){let{title:r,message:s,variant:n="info",onClose:t}=e;return(0,a.jsxs)(d,{variant:{success:"success",error:"destructive",warning:"warning",info:"info"}[n],className:"relative",children:[u[n],(0,a.jsxs)("div",{className:"flex-1",children:[r&&(0,a.jsx)(o,{children:r}),(0,a.jsx)(c,{children:s})]}),t&&(0,a.jsx)("button",{onClick:t,className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Đ\xf3ng th\xf4ng b\xe1o",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},8711:function(e,r,s){s.d(r,{C:function(){return i}});var a=s(7437);s(2265);var n=s(535),t=s(3448);let l=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600",info:"border-transparent bg-blue-500 text-white hover:bg-blue-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:s,...n}=e;return(0,a.jsx)("div",{className:(0,t.cn)(l({variant:s}),r),...n})}},6334:function(e,r,s){s.d(r,{z:function(){return d}});var a=s(7437),n=s(2265),t=s(535),l=s(3448);let i=(0,t.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:s,variant:n,size:t,asChild:d=!1,...o}=e;return(0,a.jsx)("button",{className:(0,l.cn)(i({variant:n,size:t,className:s})),ref:r,...o})});d.displayName="Button"},757:function(e,r,s){s.d(r,{Zb:function(){return d}});var a=s(7437),n=s(2265),t=s(535),l=s(3448);let i=(0,t.j)("rounded-lg border bg-card text-card-foreground",{variants:{variant:{default:"shadow-sm",elevated:"shadow-md",outlined:"border-2 shadow-none",ghost:"border-none shadow-none bg-transparent"},size:{sm:"p-3",md:"p-4",lg:"p-6"}},defaultVariants:{variant:"default",size:"md"}}),d=n.forwardRef((e,r)=>{let{className:s,variant:n,size:t,...d}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)(i({variant:n,size:t}),s),...d})});d.displayName="Card",n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...n})}).displayName="CardHeader",n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})}).displayName="CardTitle",n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",s),...n})}).displayName="CardDescription",n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",s),...n})}).displayName="CardContent",n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},1235:function(e,r,s){s.d(r,{h0:function(){return m},t6:function(){return f}});var a=s(7437),n=s(3448);function t(e){let{className:r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("animate-pulse rounded-md bg-gray-200",r)})}function l(e){let{lines:r=1,className:s}=e;return(0,a.jsx)("div",{className:(0,n.cn)("space-y-2",s),children:Array.from({length:r}).map((e,s)=>(0,a.jsx)(t,{className:(0,n.cn)("h-4",s===r-1?"w-3/4":"w-full")},s))})}function i(e){let{className:r}=e;return(0,a.jsxs)("div",{className:(0,n.cn)("card-base overflow-hidden",r),children:[(0,a.jsx)(t,{className:"aspect-video w-full"}),(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[(0,a.jsx)(t,{className:"h-6 w-full"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t,{className:"h-4 w-4 rounded-full"}),(0,a.jsx)(t,{className:"h-4 w-24"})]}),(0,a.jsx)(l,{lines:2}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(t,{className:"h-4 w-16"}),(0,a.jsx)(t,{className:"h-4 w-12"})]}),(0,a.jsx)(t,{className:"h-6 w-20"})]})]})}function d(e){let{className:r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("dashboard-stats",r),children:Array.from({length:4}).map((e,r)=>(0,a.jsx)("div",{className:"card-base card-padding-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(t,{className:"h-4 w-16"}),(0,a.jsx)(t,{className:"h-8 w-12"})]}),(0,a.jsx)(t,{className:"h-8 w-8 rounded-full"})]})},r))})}function o(e){let{rows:r=5,cols:s=4,className:l}=e;return(0,a.jsxs)("div",{className:(0,n.cn)("space-y-4",l),children:[(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,r)=>(0,a.jsx)(t,{className:"h-6 w-full"},r))}),Array.from({length:r}).map((e,r)=>(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,r)=>(0,a.jsx)(t,{className:"h-4 w-full"},r))},r))]})}function c(e){let{className:r}=e;return(0,a.jsxs)("div",{className:(0,n.cn)("card-base card-padding-md space-y-4",r),children:[(0,a.jsx)(t,{className:"h-6 w-48"}),(0,a.jsx)("div",{className:"space-y-2",children:Array.from({length:6}).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(t,{className:"h-4 w-12"}),(0,a.jsx)(t,{className:"h-6",style:{width:"".concat(60*Math.random()+20,"%")}})]},r))}),(0,a.jsx)("div",{className:"flex justify-between",children:Array.from({length:7}).map((e,r)=>(0,a.jsx)(t,{className:"h-4 w-8"},r))})]})}function u(e){let{items:r=5,className:s}=e;return(0,a.jsx)("div",{className:(0,n.cn)("space-y-4",s),children:Array.from({length:r}).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,a.jsx)(t,{className:"h-12 w-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(t,{className:"h-5 w-3/4"}),(0,a.jsx)(t,{className:"h-4 w-1/2"})]}),(0,a.jsx)(t,{className:"h-8 w-20"})]},r))})}function m(e){let{className:r}=e;return(0,a.jsxs)("div",{className:(0,n.cn)("container-7xl section-padding",r),children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)(t,{className:"h-12 w-96 mx-auto mb-4"}),(0,a.jsx)(t,{className:"h-6 w-128 mx-auto"})]}),(0,a.jsx)("div",{className:"flex gap-4 mb-8",children:Array.from({length:4}).map((e,r)=>(0,a.jsx)(t,{className:"h-10 w-24"},r))}),(0,a.jsx)("div",{className:"course-grid",children:Array.from({length:8}).map((e,r)=>(0,a.jsx)(i,{},r))})]})}function f(e){let{className:r}=e;return(0,a.jsxs)("div",{className:(0,n.cn)("container-7xl section-padding",r),children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)(t,{className:"h-10 w-64 mb-2"}),(0,a.jsx)(t,{className:"h-6 w-48"})]}),(0,a.jsx)(d,{className:"mb-8"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsx)(c,{}),(0,a.jsx)(o,{})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(u,{})})]})]})}},3448:function(e,r,s){s.d(r,{cn:function(){return t}});var a=s(1994),n=s(3335);function t(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,n.m6)((0,a.W)(r))}}}]);