'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Loading } from '@/components/ui/Loading'
import { AlertMessage } from '@/components/ui/Alert'
import { Modal } from '@/components/ui/Modal'
import { useToast } from '@/components/ui/Toast'
import PaymentForm from '@/components/payment/PaymentForm'

interface CourseData {
  _id: string
  title: string
  slug: string
  shortDescription: string
  content: {
    description: string
    objectives: string[]
    prerequisites: string[]
    syllabus: Array<{
      week: number
      title: string
      topics: string[]
      duration: number
    }>
  }
  instructor: {
    _id: string
    profile: {
      firstName: string
      lastName: string
      avatar?: string
      bio?: string
    }
  }
  category: {
    name: string
    slug: string
  }
  level: string
  language: string
  pricing: {
    basePrice: number
    currency: string
  }
  thumbnail?: string
  stats: {
    totalStudents: number
    averageRating: number
    totalRatings: number
    totalLessons: number
    totalDuration: number
  }
  lessons: Array<{
    _id: string
    title: string
    slug: string
    description: string
    order: number
    type: string
    difficulty: string
    canAccess: boolean
    settings: {
      isPreview: boolean
    }
  }>
  enrollment?: {
    _id: string
    status: string
    progress: {
      completionPercentage: number
      completedLessons: number
      totalLessons: number
    }
  }
  canEnroll: boolean
  canEdit: boolean
  publishedAt: string
}

interface CourseDetailPageProps {
  params: { slug: string }
}

export default function CourseDetailPage({ params }: CourseDetailPageProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const { addToast } = useToast()
  
  const [course, setCourse] = useState<CourseData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [enrolling, setEnrolling] = useState(false)
  const [showEnrollModal, setShowEnrollModal] = useState(false)
  const [selectedTab, setSelectedTab] = useState<'overview' | 'curriculum' | 'instructor' | 'reviews'>('overview')

  // Fetch course data
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch(`/api/courses/${params.slug}`)
        const data = await response.json()
        
        if (data.success) {
          setCourse(data.data)
        } else {
          setError(data.error || 'Không tìm thấy khóa học')
        }
      } catch (err) {
        setError('Lỗi kết nối server')
      } finally {
        setLoading(false)
      }
    }

    if (params.slug) {
      fetchCourse()
    }
  }, [params.slug])

  // Handle enrollment
  const handleEnroll = async (type: 'free' | 'paid' | 'activation_code', options?: any) => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      setEnrolling(true)
      
      const response = await fetch(`/api/courses/${course?._id}/enroll`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          ...options
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        addToast({
          message: 'Đăng ký khóa học thành công!',
          variant: 'success'
        })
        
        // Refresh course data
        window.location.reload()
      } else {
        addToast({
          message: data.error || 'Lỗi khi đăng ký khóa học',
          variant: 'error'
        })
      }
    } catch (error) {
      addToast({
        message: 'Lỗi kết nối server',
        variant: 'error'
      })
    } finally {
      setEnrolling(false)
      setShowEnrollModal(false)
    }
  }

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return 'Miễn phí'
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency === 'VND' ? 'VND' : 'USD'
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  if (error || !course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😞</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Không tìm thấy khóa học
          </h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link href="/courses">
            <Button>Về danh sách khóa học</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Course Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <Badge variant="secondary">
                  {course.category.name}
                </Badge>
                <Badge variant="outline">
                  {course.level}
                </Badge>
                <Badge variant="outline">
                  {course.language}
                </Badge>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {course.title}
              </h1>
              
              <p className="text-lg text-gray-600 mb-6">
                {course.shortDescription}
              </p>
              
              <div className="flex items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <span>👨‍🏫</span>
                  <span>
                    {course.instructor.profile.firstName} {course.instructor.profile.lastName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span>👥</span>
                  <span>{course.stats.totalStudents} học viên</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>📚</span>
                  <span>{course.stats.totalLessons} bài học</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>⏱️</span>
                  <span>{formatDuration(course.stats.totalDuration)}</span>
                </div>
              </div>
              
              {course.stats.totalRatings > 0 && (
                <div className="flex items-center gap-2 mt-4">
                  <div className="flex items-center">
                    <span className="text-yellow-400">⭐</span>
                    <span className="font-semibold ml-1">
                      {course.stats.averageRating.toFixed(1)}
                    </span>
                  </div>
                  <span className="text-gray-500">
                    ({course.stats.totalRatings} đánh giá)
                  </span>
                </div>
              )}
            </div>
            
            {/* Course Card */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <div className="aspect-video bg-gray-200 rounded-t-lg relative overflow-hidden">
                  {course.thumbnail ? (
                    <Image
                      src={course.thumbnail}
                      alt={course.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-400 text-4xl">
                      📚
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <div className="text-center mb-6">
                    <div className="text-3xl font-bold text-primary mb-2">
                      {formatPrice(course.pricing.basePrice, course.pricing.currency)}
                    </div>
                    {course.pricing.basePrice > 0 && (
                      <div className="text-sm text-gray-500">
                        Thanh toán một lần
                      </div>
                    )}
                  </div>
                  
                  {course.enrollment ? (
                    <div className="space-y-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 text-green-800 mb-2">
                          <span>✅</span>
                          <span className="font-semibold">Đã đăng ký</span>
                        </div>
                        <div className="text-sm text-green-700">
                          Tiến độ: {course.enrollment.progress.completionPercentage}%
                          ({course.enrollment.progress.completedLessons}/{course.enrollment.progress.totalLessons} bài học)
                        </div>
                      </div>
                      
                      <Button 
                        className="w-full"
                        onClick={() => router.push(`/learn/${course.slug}`)}
                      >
                        Tiếp tục học
                      </Button>
                    </div>
                  ) : course.canEnroll ? (
                    <div className="space-y-4">
                      <Button
                        className="w-full"
                        onClick={() => {
                          if (course.pricing.basePrice === 0) {
                            handleEnroll('free')
                          } else {
                            setShowEnrollModal(true)
                          }
                        }}
                        disabled={enrolling}
                      >
                        {enrolling ? <Loading size="sm" /> : 'Đăng ký ngay'}
                      </Button>
                      
                      {course.pricing.basePrice > 0 && (
                        <div className="text-center">
                          <button
                            onClick={() => setShowEnrollModal(true)}
                            className="text-sm text-primary hover:underline"
                          >
                            Có mã kích hoạt?
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500">
                      Khóa học không khả dụng
                    </div>
                  )}
                  
                  {course.canEdit && (
                    <div className="mt-4 pt-4 border-t">
                      <Link href={`/instructor/courses/${course.slug}/edit`}>
                        <Button variant="outline" className="w-full">
                          Chỉnh sửa khóa học
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Content Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Tổng quan' },
                { id: 'curriculum', label: 'Chương trình học' },
                { id: 'instructor', label: 'Giảng viên' },
                { id: 'reviews', label: 'Đánh giá' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    selectedTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {selectedTab === 'overview' && (
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Mô tả khóa học</h3>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed">
                      {course.content.description}
                    </p>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-4">Mục tiêu học tập</h3>
                  <ul className="space-y-2">
                    {course.content.objectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <span className="text-green-500 mt-1">✓</span>
                        <span className="text-gray-700">{objective}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {course.content.prerequisites.length > 0 && (
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Yêu cầu trước khi học</h3>
                    <ul className="space-y-2">
                      {course.content.prerequisites.map((prerequisite, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="text-blue-500 mt-1">•</span>
                          <span className="text-gray-700">{prerequisite}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'curriculum' && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold">Chương trình học</h3>
                
                {course.content.syllabus.map((week, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-semibold text-lg">
                        Tuần {week.week}: {week.title}
                      </h4>
                      <Badge variant="outline">
                        {formatDuration(week.duration)}
                      </Badge>
                    </div>
                    <ul className="space-y-1">
                      {week.topics.map((topic, topicIndex) => (
                        <li key={topicIndex} className="text-gray-600 text-sm">
                          • {topic}
                        </li>
                      ))}
                    </ul>
                  </Card>
                ))}
                
                {course.lessons.length > 0 && (
                  <div className="mt-8">
                    <h4 className="font-semibold text-lg mb-4">Danh sách bài học</h4>
                    <div className="space-y-2">
                      {course.lessons.map((lesson, index) => (
                        <div
                          key={lesson._id}
                          className={`flex items-center justify-between p-3 rounded-lg border ${
                            lesson.canAccess ? 'bg-white' : 'bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <span className="text-sm text-gray-500 w-8">
                              {lesson.order}
                            </span>
                            <div>
                              <div className={`font-medium ${
                                lesson.canAccess ? 'text-gray-900' : 'text-gray-500'
                              }`}>
                                {lesson.title}
                              </div>
                              <div className="text-sm text-gray-500">
                                {lesson.description}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" size="sm">
                              {lesson.type}
                            </Badge>
                            {lesson.settings.isPreview && (
                              <Badge variant="secondary" size="sm">
                                Xem trước
                              </Badge>
                            )}
                            {!lesson.canAccess && (
                              <span className="text-gray-400">🔒</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'instructor' && (
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    {course.instructor.profile.avatar ? (
                      <Image
                        src={course.instructor.profile.avatar}
                        alt={`${course.instructor.profile.firstName} ${course.instructor.profile.lastName}`}
                        width={64}
                        height={64}
                        className="rounded-full"
                      />
                    ) : (
                      <span className="text-2xl">👨‍🏫</span>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold">
                      {course.instructor.profile.firstName} {course.instructor.profile.lastName}
                    </h3>
                    <p className="text-gray-600">Giảng viên</p>
                  </div>
                </div>
                
                {course.instructor.profile.bio && (
                  <div>
                    <h4 className="font-semibold mb-2">Giới thiệu</h4>
                    <p className="text-gray-700 leading-relaxed">
                      {course.instructor.profile.bio}
                    </p>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'reviews' && (
              <div className="space-y-6">
                <div className="text-center py-12">
                  <div className="text-4xl mb-4">⭐</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Đánh giá sẽ sớm có mặt
                  </h3>
                  <p className="text-gray-600">
                    Tính năng đánh giá đang được phát triển
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enrollment Modal */}
      <Modal
        isOpen={showEnrollModal}
        onClose={() => setShowEnrollModal(false)}
        title="Đăng ký khóa học"
        size="lg"
      >
        <PaymentForm
          courseId={course._id}
          courseTitle={course.title}
          amount={course.pricing.basePrice}
          currency={course.pricing.currency}
          onSuccess={(enrollmentId) => {
            addToast({
              message: 'Đăng ký khóa học thành công!',
              variant: 'success'
            })
            setShowEnrollModal(false)
            // Refresh page to show enrollment status
            window.location.reload()
          }}
          onError={(error) => {
            addToast({
              message: error,
              variant: 'error'
            })
          }}
          onCancel={() => setShowEnrollModal(false)}
        />
      </Modal>
    </div>
  )
}
