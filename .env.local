# Database
MONGODB_URI=mongodb://localhost:27017/webta-lms

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here-make-it-long-and-random-for-development

# Email Configuration (for development)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# OAuth Providers (optional - for development)
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Upload (Cloudinary - for development)
# CLOUDINARY_CLOUD_NAME=your-cloud-name
# CLOUDINARY_API_KEY=your-api-key
# CLOUDINARY_API_SECRET=your-api-secret

# Application Settings
NODE_ENV=development
APP_NAME=WebTA LMS
APP_URL=http://localhost:3000
APP_VERSION=0.1.0

# Security
JWT_SECRET=your-jwt-secret-key-for-development
ENCRYPTION_KEY=your-encryption-key-32-characters

# Development Tools
ANALYZE_BUNDLE=false
DISABLE_ESLINT=false
