'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Form, FormField, FormLabel, FormError, FormHelp, RadioGroup } from '@/components/ui/Form'
import { AlertMessage } from '@/components/ui/Alert'
import { Loading } from '@/components/ui/Loading'

export default function SignUpPage() {
  const router = useRouter()

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'student',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Tên là bắt buộc'
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Họ là bắt buộc'
    }

    if (!formData.email) {
      newErrors.email = 'Email là bắt buộc'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ'
    }

    if (!formData.password) {
      newErrors.password = 'Mật khẩu là bắt buộc'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Mật khẩu phải có ít nhất 8 ký tự'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Xác nhận mật khẩu là bắt buộc'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Mật khẩu xác nhận không khớp'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName.trim(),
          lastName: formData.lastName.trim(),
          email: formData.email.toLowerCase(),
          password: formData.password,
          role: formData.role,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
      } else {
        if (data.details) {
          // Validation errors from server
          const serverErrors: Record<string, string> = {}
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              serverErrors[error.path[0]] = error.message
            }
          })
          setErrors(serverErrors)
        } else {
          setErrors({ general: data.error || 'Đã xảy ra lỗi trong quá trình đăng ký' })
        }
      }
    } catch (error) {
      setErrors({ general: 'Đã xảy ra lỗi không mong muốn' })
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <div className="bg-white py-8 px-6 shadow rounded-lg text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Đăng ký thành công!
            </h2>
            <p className="text-gray-600 mb-6">
              Chúng tôi đã gửi email xác thực đến địa chỉ email của bạn. 
              Vui lòng kiểm tra email và nhấp vào liên kết để kích hoạt tài khoản.
            </p>
            <Button onClick={() => router.push('/auth/signin')} className="w-full">
              Đến trang đăng nhập
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const roleOptions = [
    { value: 'student', label: 'Học viên - Tôi muốn học các khóa học' },
    { value: 'instructor', label: 'Giảng viên - Tôi muốn tạo và bán khóa học' },
  ]

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary">
            <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Tạo tài khoản mới
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Đã có tài khoản?{' '}
            <Link href="/auth/signin" className="font-medium text-primary hover:text-primary/80">
              Đăng nhập ngay
            </Link>
          </p>
        </div>

        <div className="bg-white py-8 px-6 shadow rounded-lg">
          {errors.general && (
            <div className="mb-6">
              <AlertMessage
                variant="error"
                message={errors.general}
              />
            </div>
          )}

          <Form onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <FormField>
                <FormLabel htmlFor="firstName" required>
                  Tên
                </FormLabel>
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  autoComplete="given-name"
                  value={formData.firstName}
                  onChange={handleChange}
                  error={!!errors.firstName}
                  placeholder="Tên của bạn"
                />
                <FormError message={errors.firstName} />
              </FormField>

              <FormField>
                <FormLabel htmlFor="lastName" required>
                  Họ
                </FormLabel>
                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  autoComplete="family-name"
                  value={formData.lastName}
                  onChange={handleChange}
                  error={!!errors.lastName}
                  placeholder="Họ của bạn"
                />
                <FormError message={errors.lastName} />
              </FormField>
            </div>

            <FormField>
              <FormLabel htmlFor="email" required>
                Email
              </FormLabel>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={formData.email}
                onChange={handleChange}
                error={!!errors.email}
                placeholder="Nhập email của bạn"
              />
              <FormError message={errors.email} />
            </FormField>

            <FormField>
              <FormLabel htmlFor="password" required>
                Mật khẩu
              </FormLabel>
              <Input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                value={formData.password}
                onChange={handleChange}
                error={!!errors.password}
                placeholder="Tạo mật khẩu"
              />
              <FormHelp>
                Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số
              </FormHelp>
              <FormError message={errors.password} />
            </FormField>

            <FormField>
              <FormLabel htmlFor="confirmPassword" required>
                Xác nhận mật khẩu
              </FormLabel>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                autoComplete="new-password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={!!errors.confirmPassword}
                placeholder="Nhập lại mật khẩu"
              />
              <FormError message={errors.confirmPassword} />
            </FormField>

            <FormField>
              <FormLabel>Bạn là:</FormLabel>
              <RadioGroup
                name="role"
                options={roleOptions}
                value={formData.role}
                onChange={handleRoleChange}
              />
            </FormField>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? <Loading size="sm" /> : 'Tạo tài khoản'}
            </Button>
          </Form>

          <p className="mt-4 text-xs text-gray-500 text-center">
            Bằng cách tạo tài khoản, bạn đồng ý với{' '}
            <Link href="/terms" className="text-primary hover:text-primary/80">
              Điều khoản sử dụng
            </Link>{' '}
            và{' '}
            <Link href="/privacy" className="text-primary hover:text-primary/80">
              Chính sách bảo mật
            </Link>{' '}
            của chúng tôi.
          </p>
        </div>
      </div>
    </div>
  )
}
